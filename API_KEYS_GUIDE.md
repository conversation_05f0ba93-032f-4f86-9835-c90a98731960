# 🔑 API Keys Setup Guide

## Quick Start (5 minutes)

### 1. 🚀 **Immediate Testing (Demo Mode)**
```bash
# Already done! You have demo keys configured
python3 run_real_system.py
```

### 2. 🎯 **Get Real API Keys (FREE)**

All these services offer **FREE tiers** - no payment required!

---

## 🌐 **Free API Keys Setup**

### **1. Groq AI (REQUIRED for AI responses)**
- 🌐 **URL**: https://console.groq.com/keys
- ⏱️ **Time**: 2 minutes
- 💰 **Cost**: FREE (generous limits)
- 🎯 **Purpose**: AI chat responses, code generation

**Steps:**
1. Go to https://console.groq.com/keys
2. Sign up with Google/GitHub (instant)
3. Click "Create API Key"
4. Copy the key (starts with `gsk_`)
5. Add to `.env`: `GROQ_API_KEY=********************************************************
`

---

### **2. Giphy (GIFs)**
- 🌐 **URL**: 
- ⏱️ **Time**: 2 minutes  https://developers.giphy.com/
- 💰 **Cost**: FREE (1000 requests/day)
- 🎯 **Purpose**: GIF search and animated images

**Steps:**
1. Go to https://developers.giphy.com/
2. Click "Create an App"
3. Choose "API" → "Next"
4. Fill app name: "AI Chat System"
5. Copy API key
6. Add to `.env`: `EvOSLXqGpuqiHjj2CTulJwm6fRXJMJpu`

---

### **3. OpenWeather (Weather)**
- 🌐 **URL**: https://openweathermap.org/api
- ⏱️ **Time**: 2 minutes
- 💰 **Cost**: FREE (1000 calls/day)
- 🎯 **Purpose**: Weather information

**Steps:**
1. Go to https://openweathermap.org/api
2. Click "Sign Up" (free account)
3. Verify email
4. Go to "API Keys" tab
5. Copy the default key
6. Add to `.env`: `OPENWEATHER_API_KEY=your_key_here`

---

### **4. Hugging Face (AI Audio)**
- 🌐 **URL**: https://huggingface.co/settings/tokens
- ⏱️ **Time**: 1 minute
- 💰 **Cost**: FREE (good limits)
- 🎯 **Purpose**: AI music/audio generation

**Steps:**
1. Go to https://huggingface.co/settings/tokens
2. Sign up (instant with Google/GitHub)
3. Click "New token"
4. Name: "AI Chat System"
5. Role: "Read"
6. Copy token (starts with `hf_`)
7. Add to `.env`: `HUGGINGFACE_API_KEY=hf_your_token_here`

---

### **5. Unsplash (Photos)**
- 🌐 **URL**: https://unsplash.com/developers
- ⏱️ **Time**: 2 minutes
- 💰 **Cost**: FREE (5000 requests/hour)
- 🎯 **Purpose**: High-quality photos

**Steps:**
1. Go to https://unsplash.com/developers
2. Sign up (free account)
3. Click "New Application"
4. Accept terms
5. Fill app details:
   - Name: "AI Chat System"
   - Description: "AI chat with image search"
6. Copy "Access Key"
7. Add to `.env`: `UNSPLASH_API_KEY=your_access_key_here`

---

## 🔧 **Setup Instructions**

### **Method 1: Interactive Setup**
```bash
python3 setup_api_keys.py
# Choose option 1 for full setup
```

### **Method 2: Manual Edit**
```bash
# Edit .env file directly
nano .env

# Add your keys:
GROQ_API_KEY=gsk_your_groq_key_here
GIPHY_API_KEY=your_giphy_key_here
OPENWEATHER_API_KEY=your_weather_key_here
HUGGINGFACE_API_KEY=hf_your_hf_token_here
UNSPLASH_API_KEY=your_unsplash_key_here
```

### **Method 3: Environment Variables**
```bash
# Set in terminal (temporary)
export GROQ_API_KEY="gsk_your_key_here"
export GIPHY_API_KEY="your_key_here"
export OPENWEATHER_API_KEY="your_key_here"
```

---

## 🧪 **Testing Your Setup**

### **1. Test Individual APIs**
```bash
python3 -c "
import os, requests

# Load .env
if os.path.exists('.env'):
    with open('.env') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                os.environ[key] = value.strip()

# Test Giphy
api_key = os.getenv('GIPHY_API_KEY')
if api_key:
    response = requests.get(f'https://api.giphy.com/v1/gifs/search?api_key={api_key}&q=test&limit=1')
    print(f'Giphy: {\"✅ Working\" if response.status_code == 200 else \"❌ Failed\"}')
"
```

### **2. Test Full System**
```bash
python3 run_real_system.py
# Choose option 1 to test all plugins
```

### **3. Interactive Chat**
```bash
python3 run_real_system.py
# Choose option 2 for interactive chat
```

---

## 🎯 **Priority Setup (Recommended Order)**

### **Essential (Start Here):**
1. ✅ **Giphy** - Already configured with demo key
2. 🔑 **Groq** - For AI responses (most important)
3. 🌤️ **OpenWeather** - For weather info

### **Optional (Add Later):**
4. 🎵 **Hugging Face** - For AI audio generation
5. 📷 **Unsplash** - For high-quality photos

---

## 🚀 **What You Get**

### **With Demo Keys (Current):**
- ✅ Plugin detection working
- ✅ HTML generation working  
- ✅ Basic GIF search (limited)
- ✅ Web interface working
- 🎭 Simulated responses for other services

### **With Real API Keys:**
- 🎉 **REAL GIFs** from Giphy
- 🌤️ **REAL weather** data
- 🎵 **REAL AI-generated** music
- 📷 **REAL photos** from Unsplash
- 🤖 **REAL AI** chat responses
- 📰 **REAL news** articles

---

## 🔒 **Security Notes**

- ✅ API keys stored in `.env` file (not committed to git)
- ✅ Keys are encrypted when stored by the system
- ✅ Rate limiting prevents abuse
- ✅ No payment info required for free tiers

---

## 🆘 **Troubleshooting**

### **"API key not working"**
- Check key format (Groq: `gsk_`, Hugging Face: `hf_`)
- Verify key is active in service dashboard
- Check rate limits

### **"Import errors"**
```bash
pip install requests aiohttp
```

### **"Permission denied"**
```bash
chmod +x setup_api_keys.py
chmod +x run_real_system.py
```

---

## 🎉 **Ready to Go!**

Once you have API keys:

```bash
# Test everything
python3 run_real_system.py

# Start web interface  
streamlit run src/ui/plugin_interface.py

# Interactive chat
python3 scripts/run_plugin_manager.py chat
```

**Total setup time: ~10 minutes for all services** 🚀
