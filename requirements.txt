# Core dependencies
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AI/ML Libraries
groq==0.4.1
transformers==4.36.0
torch==2.1.0
tokenizers==0.15.0
datasets==2.14.0
accelerate==0.24.0

# Web Frameworks
fastapi==0.104.0
uvicorn[standard]==0.24.0
streamlit==1.28.0
websockets==12.0

# HTTP Client
httpx==0.25.0
aiohttp==3.9.0
requests==2.31.0

# Database (optional)
sqlalchemy==2.0.23
alembic==1.12.0
sqlite3

# Caching
redis==5.0.1

# Utilities
click==8.1.7
rich==13.7.0
loguru==0.7.2
python-multipart==0.0.6

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0
mypy==1.7.0
pre-commit==3.5.0

# Frontend (React dependencies will be in package.json)
# These are for serving static files
jinja2==3.1.2
aiofiles==23.2.1

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.7

# Monitoring
prometheus-client==0.19.0

# Testing utilities
factory-boy==3.3.0
faker==20.1.0
responses==0.24.1
