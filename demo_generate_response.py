#!/usr/bin/env python3
"""
Demo script for generate_response function.
Shows all features and usage examples.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.response_generator import (
    generate_response,
    generate_groq_response,
    generate_huggingface_response,
    generate_streaming_response,
    get_available_providers,
    validate_provider
)
from utils.simple_chat import SimpleChatBot, ask_ai


async def demo_basic_usage():
    """Demo 1: Basic usage."""
    print("🤖 Demo 1: Basic Usage")
    print("=" * 50)
    
    # Simple question
    messages = [{"role": "user", "content": "What is artificial intelligence?"}]
    
    try:
        response = await generate_response(messages)
        print(f"Question: {messages[0]['content']}")
        print(f"Answer: {response[:200]}...")
        print("✅ Basic usage successful!")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def demo_conversation_history():
    """Demo 2: Conversation with history."""
    print("\n💬 Demo 2: Conversation History")
    print("=" * 50)
    
    # Build conversation step by step
    conversation = []
    
    # User asks first question
    conversation.append({"role": "user", "content": "What is Python programming?"})
    response1 = await generate_response(conversation)
    conversation.append({"role": "assistant", "content": response1})
    
    print(f"User: {conversation[0]['content']}")
    print(f"AI: {response1[:150]}...")
    
    # User asks follow-up
    conversation.append({"role": "user", "content": "Can you show me a simple Python example?"})
    response2 = await generate_response(conversation)
    conversation.append({"role": "assistant", "content": response2})
    
    print(f"\nUser: {conversation[2]['content']}")
    print(f"AI: {response2[:150]}...")
    
    print(f"\n✅ Conversation has {len(conversation)} messages")


async def demo_providers():
    """Demo 3: Different providers."""
    print("\n🔧 Demo 3: Different Providers")
    print("=" * 50)
    
    messages = [{"role": "user", "content": "Explain machine learning in one sentence."}]
    
    # Check available providers
    providers = await get_available_providers()
    print(f"Available providers: {providers}")
    
    # Test each provider
    for provider in providers:
        try:
            print(f"\n--- Testing {provider.upper()} ---")
            
            # Validate provider first
            is_valid = await validate_provider(provider)
            print(f"Provider valid: {is_valid}")
            
            if is_valid:
                response = await generate_response(messages, provider=provider)
                print(f"Response: {response[:100]}...")
            
        except Exception as e:
            print(f"❌ {provider} failed: {e}")
    
    # Test auto selection
    print("\n--- Testing AUTO selection ---")
    try:
        response = await generate_response(messages, provider="auto")
        print(f"Auto response: {response[:100]}...")
    except Exception as e:
        print(f"❌ Auto failed: {e}")


async def demo_parameters():
    """Demo 4: Different parameters."""
    print("\n⚙️ Demo 4: Parameter Effects")
    print("=" * 50)
    
    messages = [{"role": "user", "content": "Write a creative story opening about a robot."}]
    
    # Test different temperatures
    temperatures = [0.1, 0.7, 0.9]
    
    for temp in temperatures:
        try:
            print(f"\n--- Temperature: {temp} ---")
            response = await generate_response(
                messages, 
                temperature=temp,
                max_tokens=150
            )
            print(f"Response: {response[:120]}...")
            
        except Exception as e:
            print(f"❌ Temperature {temp} failed: {e}")


async def demo_streaming():
    """Demo 5: Streaming responses."""
    print("\n🌊 Demo 5: Streaming Response")
    print("=" * 50)
    
    messages = [{"role": "user", "content": "Tell me a short joke about programming."}]
    
    try:
        print("Question: Tell me a short joke about programming.")
        print("AI: ", end="", flush=True)
        
        async for chunk in generate_streaming_response(messages):
            print(chunk, end="", flush=True)
        
        print("\n✅ Streaming completed!")
        
    except Exception as e:
        print(f"\n❌ Streaming failed: {e}")


async def demo_simple_chat():
    """Demo 6: Simple chat interface."""
    print("\n🗣️ Demo 6: Simple Chat Interface")
    print("=" * 50)
    
    try:
        # Create chatbot
        bot = SimpleChatBot(
            provider="auto",
            system_message="You are a helpful programming assistant."
        )
        
        # Simulate conversation
        questions = [
            "What is a variable in programming?",
            "How do I create a variable in Python?",
            "What's the difference between a list and a tuple?"
        ]
        
        for question in questions:
            response = await bot.chat(question)
            print(f"\nUser: {question}")
            print(f"Bot: {response[:120]}...")
        
        # Show conversation history
        history = bot.get_conversation()
        print(f"\n✅ Chat completed! History has {len(history)} messages")
        
    except Exception as e:
        print(f"❌ Chat failed: {e}")


async def demo_error_handling():
    """Demo 7: Error handling."""
    print("\n🛡️ Demo 7: Error Handling")
    print("=" * 50)
    
    # Test various error conditions
    error_tests = [
        ("Empty messages", []),
        ("Invalid message format", [{"invalid": "format"}]),
        ("Invalid role", [{"role": "invalid", "content": "test"}]),
        ("Empty content", [{"role": "user", "content": ""}]),
        ("Non-dict message", ["not a dict"])
    ]
    
    for test_name, messages in error_tests:
        try:
            print(f"\nTesting: {test_name}")
            await generate_response(messages)
            print("❌ Should have failed!")
        except ValueError as e:
            print(f"✅ Caught expected error: {e}")
        except Exception as e:
            print(f"⚠️ Unexpected error: {e}")
    
    # Test invalid provider
    try:
        print(f"\nTesting: Invalid provider")
        await generate_response([{"role": "user", "content": "test"}], provider="invalid")
        print("❌ Should have failed!")
    except ValueError as e:
        print(f"✅ Caught expected error: {e}")


async def demo_convenience_functions():
    """Demo 8: Convenience functions."""
    print("\n🎯 Demo 8: Convenience Functions")
    print("=" * 50)
    
    try:
        # Simple ask function
        print("--- Using ask_ai() ---")
        response = await ask_ai("What is the capital of France?")
        print(f"Question: What is the capital of France?")
        print(f"Answer: {response}")
        
        # Provider-specific functions
        print("\n--- Provider-specific functions ---")
        messages = [{"role": "user", "content": "What is 2+2?"}]
        
        providers = await get_available_providers()
        
        if "groq" in providers:
            groq_response = await generate_groq_response(messages)
            print(f"Groq: {groq_response}")
        
        if "huggingface" in providers:
            hf_response = await generate_huggingface_response(messages)
            print(f"HuggingFace: {hf_response}")
        
        print("✅ Convenience functions work!")
        
    except Exception as e:
        print(f"❌ Convenience functions failed: {e}")


async def demo_real_world_example():
    """Demo 9: Real-world example."""
    print("\n🌍 Demo 9: Real-World Example")
    print("=" * 50)
    
    try:
        # Simulate a coding help session
        print("Simulating a coding help session...")
        
        conversation = [
            {"role": "system", "content": "You are an expert Python tutor. Provide clear, helpful explanations with examples."}
        ]
        
        # Student questions
        student_questions = [
            "I'm new to Python. What should I learn first?",
            "How do I create a function that adds two numbers?",
            "Can you explain what a loop is?"
        ]
        
        for i, question in enumerate(student_questions, 1):
            print(f"\n--- Question {i} ---")
            
            # Add student question
            conversation.append({"role": "user", "content": question})
            print(f"Student: {question}")
            
            # Generate tutor response
            response = await generate_response(conversation, temperature=0.3)  # Lower temp for educational content
            conversation.append({"role": "assistant", "content": response})
            
            print(f"Tutor: {response[:200]}...")
        
        print(f"\n✅ Tutoring session completed! {len(conversation)} total messages")
        
    except Exception as e:
        print(f"❌ Real-world example failed: {e}")


async def main():
    """Run all demos."""
    print("🚀 Generate Response Function - Complete Demo")
    print("=" * 60)
    
    try:
        # Check if any providers are available
        providers = await get_available_providers()
        if not providers:
            print("❌ No AI providers available!")
            print("Please check your API keys in the .env file.")
            print("Run: python scripts/test_api_keys.py")
            return
        
        print(f"✅ Found {len(providers)} available providers: {providers}")
        
        # Run all demos
        await demo_basic_usage()
        await demo_conversation_history()
        await demo_providers()
        await demo_parameters()
        await demo_streaming()
        await demo_simple_chat()
        await demo_error_handling()
        await demo_convenience_functions()
        await demo_real_world_example()
        
        print("\n🎉 All demos completed successfully!")
        print("\nNext steps:")
        print("1. Try the examples in your own code")
        print("2. Read the documentation: docs/generate_response_guide.md")
        print("3. Run the test suite: pytest tests/test_core/test_response_generator.py")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
