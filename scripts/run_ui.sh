#!/bin/bash

# UI Runner Script for Chat AI Project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    echo "Chat AI UI Runner"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  streamlit     Run Streamlit professional UI"
    echo "  react         Run React + Tailwind UI (development)"
    echo "  react-build   Build React UI for production"
    echo "  both          Run both UIs simultaneously"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 streamlit"
    echo "  $0 react"
    echo "  $0 both"
}

check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    
    # Check if virtual environment is activated
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        print_warning "Virtual environment not activated"
        if [ -f "venv/bin/activate" ]; then
            print_status "Activating virtual environment..."
            source venv/bin/activate
        else
            print_error "Virtual environment not found. Run setup_env.sh first."
            exit 1
        fi
    fi
    
    # Check Node.js for React
    if [[ "$1" == "react"* ]] || [[ "$1" == "both" ]]; then
        if ! command -v node &> /dev/null; then
            print_error "Node.js is not installed"
            exit 1
        fi
        
        if ! command -v npm &> /dev/null; then
            print_error "npm is not installed"
            exit 1
        fi
    fi
    
    print_success "Dependencies check passed"
}

run_streamlit() {
    print_status "Starting Streamlit Professional UI..."
    
    # Check if streamlit is installed
    if ! python3 -c "import streamlit" 2>/dev/null; then
        print_error "Streamlit is not installed. Installing..."
        pip install streamlit
    fi
    
    # Run Streamlit app
    print_success "Streamlit UI starting at http://localhost:8501"
    python3 src/ui/professional_streamlit_app.py
}

run_react() {
    print_status "Starting React + Tailwind UI..."
    
    cd src/ui/react_frontend
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing React dependencies..."
        npm install
    fi
    
    # Start development server
    print_success "React UI starting at http://localhost:3000"
    npm start
}

build_react() {
    print_status "Building React UI for production..."
    
    cd src/ui/react_frontend
    
    # Install dependencies
    if [ ! -d "node_modules" ]; then
        print_status "Installing React dependencies..."
        npm install
    fi
    
    # Build for production
    npm run build
    
    print_success "React UI built successfully in src/ui/react_frontend/build/"
}

run_both() {
    print_status "Starting both UIs simultaneously..."
    
    # Start Streamlit in background
    print_status "Starting Streamlit in background..."
    nohup bash -c "source venv/bin/activate && python3 src/ui/professional_streamlit_app.py" > streamlit.log 2>&1 &
    STREAMLIT_PID=$!
    
    # Wait a moment for Streamlit to start
    sleep 3
    
    # Start React
    print_status "Starting React UI..."
    cd src/ui/react_frontend
    
    if [ ! -d "node_modules" ]; then
        print_status "Installing React dependencies..."
        npm install
    fi
    
    # Trap to kill background processes on exit
    trap "kill $STREAMLIT_PID 2>/dev/null; exit" INT TERM EXIT
    
    print_success "Both UIs are starting:"
    print_success "  Streamlit: http://localhost:8501"
    print_success "  React:     http://localhost:3000"
    
    npm start
}

# Main script logic
case "${1:-help}" in
    streamlit)
        check_dependencies "streamlit"
        run_streamlit
        ;;
    react)
        check_dependencies "react"
        run_react
        ;;
    react-build)
        check_dependencies "react"
        build_react
        ;;
    both)
        check_dependencies "both"
        run_both
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown option: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
