#!/usr/bin/env python3
"""
<PERSON>ript to run the Diagram Generator with various modes.
"""

import sys
import asyncio
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.diagram_generator import DiagramGenerator, generate_architecture_diagram
from core.chat_with_diagrams import DiagramChatAssistant


async def interactive_diagram_generation():
    """Interactive diagram generation mode."""
    print("🏗️ Interactive Architecture Diagram Generator")
    print("=" * 50)
    
    generator = DiagramGenerator()
    
    while True:
        print("\n" + "─" * 50)
        description = input("📝 Describe your architecture (or 'quit' to exit): ")
        
        if description.lower() in ['quit', 'exit', 'q']:
            break
        
        if not description.strip():
            print("❌ Please provide a description")
            continue
        
        # Get options
        print("\n⚙️ Options:")
        diagram_type = input("Diagram type [system/network/deployment/data_flow] (default: system): ").strip() or "system"
        style = input("Style [modern/minimal/detailed] (default: modern): ").strip() or "modern"
        provider = input("AI Provider [auto/groq/huggingface] (default: auto): ").strip() or "auto"
        
        print(f"\n🤖 Generating diagram with {provider}...")
        
        try:
            # Generate diagram
            result = await generator.generate_diagram_with_ai_image(
                description=description,
                provider=provider
            )
            
            diagram = result.get("diagram")
            if diagram:
                print("\n✅ Diagram generated successfully!")
                print(f"📋 Title: {diagram.title}")
                print(f"📝 Description: {diagram.description}")
                print(f"🔧 Components: {len(diagram.components)}")
                print(f"🔗 Connections: {len(diagram.connections)}")
                
                if diagram.generated_image_path:
                    print(f"🖼️ Image saved: {diagram.generated_image_path}")
                
                if diagram.mermaid_code:
                    print("🔄 Mermaid code generated")
                    
                    show_mermaid = input("\n📊 Show Mermaid code? [y/N]: ").strip().lower() in ['y', 'yes']
                    if show_mermaid:
                        print("\n" + "─" * 60)
                        print(diagram.mermaid_code)
                        print("─" * 60)
                
                # Show components
                if diagram.components:
                    show_components = input("\n🔧 Show components? [y/N]: ").strip().lower() in ['y', 'yes']
                    if show_components:
                        print("\n📋 Components:")
                        for comp in diagram.components:
                            print(f"  • {comp.name} ({comp.type}): {comp.description}")
                
                # Show connections
                if diagram.connections:
                    show_connections = input("\n🔗 Show connections? [y/N]: ").strip().lower() in ['y', 'yes']
                    if show_connections:
                        print("\n🔗 Connections:")
                        for conn in diagram.connections:
                            label = f" - {conn.label}" if conn.label else ""
                            print(f"  • {conn.from_component} → {conn.to_component}{label}")
            
        except Exception as e:
            print(f"❌ Error generating diagram: {e}")


async def chat_mode():
    """Interactive chat mode with diagram generation."""
    print("💬 Chat Mode with Diagram Generation")
    print("=" * 50)
    
    assistant = DiagramChatAssistant()
    conversation = []
    
    print("Chat with AI assistant. Mention architecture, diagrams, or system design to trigger diagram generation.")
    print("Type 'quit' to exit.\n")
    
    while True:
        message = input("You: ")
        
        if message.lower() in ['quit', 'exit', 'q']:
            break
        
        if not message.strip():
            continue
        
        print("🤖 Processing...")
        
        try:
            result = await assistant.process_message(message, conversation)
            
            print(f"\nAI: {result['response']}")
            
            if result.get('has_diagram'):
                print("\n📊 Diagram generated!")
                diagram_result = result.get('diagram_result')
                if diagram_result:
                    diagram = diagram_result.get('diagram')
                    if diagram:
                        print(f"  Title: {diagram.title}")
                        print(f"  Components: {len(diagram.components)}")
                        print(f"  Image: {diagram.generated_image_path}")
            
            # Add to conversation
            conversation.append({"role": "user", "content": message})
            conversation.append({"role": "assistant", "content": result['response']})
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def batch_generate_diagrams(input_file: str, output_dir: str):
    """Batch diagram generation from file."""
    print(f"📦 Batch Diagram Generation")
    print(f"Input: {input_file}")
    print(f"Output: {output_dir}")
    print("=" * 50)
    
    generator = DiagramGenerator()
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(input_file, 'r') as f:
            descriptions = [line.strip() for line in f if line.strip()]
        
        print(f"📋 Found {len(descriptions)} architecture descriptions")
        
        for i, description in enumerate(descriptions, 1):
            print(f"\n🔄 Processing {i}/{len(descriptions)}: {description[:50]}...")
            
            try:
                result = await generator.generate_diagram_with_ai_image(
                    description=description,
                    provider="auto"
                )
                
                diagram = result.get("diagram")
                if diagram:
                    # Save diagram data
                    diagram_file = output_path / f"diagram_{i:03d}.md"
                    
                    with open(diagram_file, 'w') as f:
                        f.write(f"# {diagram.title}\n\n")
                        f.write(f"**Description:** {description}\n\n")
                        f.write(f"**Generated:** {diagram.description}\n\n")
                        
                        if diagram.mermaid_code:
                            f.write("## Mermaid Diagram\n\n")
                            f.write("```mermaid\n")
                            f.write(diagram.mermaid_code)
                            f.write("\n```\n\n")
                        
                        if diagram.components:
                            f.write("## Components\n\n")
                            for comp in diagram.components:
                                f.write(f"- **{comp.name}** ({comp.type}): {comp.description}\n")
                            f.write("\n")
                        
                        if diagram.connections:
                            f.write("## Connections\n\n")
                            for conn in diagram.connections:
                                label = f" - {conn.label}" if conn.label else ""
                                f.write(f"- {conn.from_component} → {conn.to_component}{label}\n")
                    
                    print(f"✅ Saved to {diagram_file.name}")
                    
                    # Copy image if available
                    if diagram.generated_image_path and Path(diagram.generated_image_path).exists():
                        import shutil
                        image_dest = output_path / f"diagram_{i:03d}.png"
                        shutil.copy2(diagram.generated_image_path, image_dest)
                        print(f"📷 Image saved to {image_dest.name}")
                
            except Exception as e:
                print(f"❌ Error processing description {i}: {e}")
        
        print(f"\n🎉 Batch generation completed! Files saved to {output_dir}")
        
    except Exception as e:
        print(f"❌ Error in batch processing: {e}")


async def quick_generate(description: str, output_format: str = "all"):
    """Quick diagram generation with single command."""
    print(f"⚡ Quick Generate: {description[:50]}...")
    print("=" * 50)
    
    try:
        response = await generate_architecture_diagram(description)
        
        if output_format == "text":
            print(response)
        elif output_format == "mermaid":
            # Extract Mermaid code from response
            import re
            mermaid_match = re.search(r'```mermaid\n(.*?)\n```', response, re.DOTALL)
            if mermaid_match:
                print(mermaid_match.group(1))
            else:
                print("No Mermaid code found in response")
        else:
            print(response)
        
    except Exception as e:
        print(f"❌ Error: {e}")


def show_templates():
    """Show example architecture templates."""
    print("📋 Architecture Templates")
    print("=" * 50)
    
    templates = {
        "E-commerce Microservices": """
E-commerce platform with microservices:
- React frontend with Next.js
- API Gateway (Kong/Nginx)
- User Management Service
- Product Catalog Service
- Order Processing Service
- Payment Service
- PostgreSQL databases
- Redis cache
- RabbitMQ message queue
- Docker containers
- Kubernetes orchestration
""",
        "Data Pipeline": """
Real-time data processing pipeline:
- Data ingestion from APIs and databases
- Apache Kafka for streaming
- Apache Spark for processing
- Data warehouse (Snowflake)
- Analytics dashboard (Tableau)
- Monitoring (Prometheus/Grafana)
- Data lake storage (S3)
- ML pipeline (MLflow)
""",
        "Serverless Web App": """
Serverless web application:
- React frontend (Vercel)
- API Gateway
- AWS Lambda functions
- DynamoDB database
- S3 file storage
- CloudFront CDN
- Cognito authentication
- CloudWatch monitoring
""",
        "Mobile App Backend": """
Mobile application backend:
- iOS/Android apps
- API Gateway with auth
- User service
- Content service
- Push notifications
- Analytics service
- File storage
- PostgreSQL + Redis
- CDN
"""
    }
    
    for name, template in templates.items():
        print(f"\n📝 {name}")
        print("─" * 30)
        print(template.strip())


async def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(description="Architecture Diagram Generator")
    parser.add_argument("mode", choices=["interactive", "chat", "batch", "quick", "templates", "ui"], 
                       help="Mode to run")
    parser.add_argument("--input", "-i", help="Input file for batch mode or description for quick mode")
    parser.add_argument("--output", "-o", help="Output directory for batch mode")
    parser.add_argument("--format", "-f", choices=["all", "text", "mermaid"], default="all",
                       help="Output format for quick mode")
    parser.add_argument("--provider", "-p", choices=["auto", "groq", "huggingface"], 
                       default="auto", help="AI provider to use")
    
    args = parser.parse_args()
    
    if args.mode == "interactive":
        await interactive_diagram_generation()
    
    elif args.mode == "chat":
        await chat_mode()
    
    elif args.mode == "batch":
        if not args.input or not args.output:
            print("❌ Batch mode requires --input and --output arguments")
            return
        await batch_generate_diagrams(args.input, args.output)
    
    elif args.mode == "quick":
        if not args.input:
            print("❌ Quick mode requires --input argument with description")
            return
        await quick_generate(args.input, args.format)
    
    elif args.mode == "templates":
        show_templates()
    
    elif args.mode == "ui":
        print("🚀 Starting Streamlit UI...")
        import subprocess
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "src/ui/diagram_interface.py"
        ])
    
    else:
        parser.print_help()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
