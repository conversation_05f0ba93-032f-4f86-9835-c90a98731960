#!/usr/bin/env python3
"""
<PERSON>ript to run the Code Assistant with various modes.
"""

import sys
import asyncio
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.code_assistant import CodeAssistant
from core.prompt_templates import prompt_manager


async def generate_code_interactive():
    """Interactive code generation mode."""
    print("🐍 Interactive Code Generator")
    print("=" * 40)
    
    assistant = CodeAssistant()
    
    while True:
        print("\n" + "─" * 40)
        description = input("📝 Describe what you want to code (or 'quit' to exit): ")
        
        if description.lower() in ['quit', 'exit', 'q']:
            break
        
        if not description.strip():
            print("❌ Please provide a description")
            continue
        
        # Get additional options
        print("\n⚙️ Options:")
        category = input("Category [general/web/data/ml/automation/api] (default: general): ").strip() or "general"
        complexity = input("Complexity [simple/medium/complex] (default: medium): ").strip() or "medium"
        include_tests = input("Include tests? [y/N] (default: N): ").strip().lower() in ['y', 'yes']
        provider = input("AI Provider [auto/groq/huggingface] (default: auto): ").strip() or "auto"
        
        print(f"\n🤖 Generating code with {provider}...")
        
        try:
            snippet = await assistant.generate_code_snippet(
                description=description,
                category=category,
                complexity=complexity,
                include_tests=include_tests,
                provider=provider
            )
            
            print("\n✅ Code generated successfully!")
            print(f"📋 Description: {snippet.description}")
            
            if snippet.dependencies:
                print(f"📦 Dependencies: {', '.join(snippet.dependencies)}")
            
            print("\n🐍 Generated Code:")
            print("─" * 60)
            print(snippet.code)
            print("─" * 60)
            
            if snippet.test_code:
                print("\n🧪 Test Code:")
                print("─" * 60)
                print(snippet.test_code)
                print("─" * 60)
            
            # Ask if user wants to test the code
            test_code = input("\n🧪 Test the generated code? [y/N]: ").strip().lower() in ['y', 'yes']
            
            if test_code:
                print("🏃 Running code...")
                result = assistant.run_code_safely(snippet.code, timeout=10)
                
                if result["success"]:
                    print(f"✅ Code executed successfully in {result['execution_time']:.2f}s")
                    if result["output"]:
                        print("📤 Output:")
                        print(result["output"])
                else:
                    print("❌ Code execution failed:")
                    print(result["error"])
            
        except Exception as e:
            print(f"❌ Error generating code: {e}")


async def debug_error_interactive():
    """Interactive error debugging mode."""
    print("🔍 Interactive Error Debugger")
    print("=" * 40)
    
    assistant = CodeAssistant()
    
    while True:
        print("\n" + "─" * 40)
        print("📋 Error Input Options:")
        print("1. Paste error log")
        print("2. Load from file")
        print("3. Quit")
        
        choice = input("Choose option [1-3]: ").strip()
        
        if choice == '3':
            break
        
        error_log = ""
        code_context = ""
        
        if choice == '1':
            print("\n📝 Paste your error log (press Enter twice when done):")
            lines = []
            while True:
                line = input()
                if line == "" and lines and lines[-1] == "":
                    break
                lines.append(line)
            error_log = "\n".join(lines[:-1])  # Remove last empty line
            
            print("\n📝 Paste code context (optional, press Enter twice when done):")
            lines = []
            while True:
                line = input()
                if line == "" and (not lines or lines[-1] == ""):
                    break
                lines.append(line)
            if lines:
                code_context = "\n".join(lines[:-1])
        
        elif choice == '2':
            file_path = input("📁 Enter path to error log file: ").strip()
            try:
                with open(file_path, 'r') as f:
                    error_log = f.read()
                print(f"✅ Loaded error log from {file_path}")
            except Exception as e:
                print(f"❌ Error loading file: {e}")
                continue
        
        else:
            print("❌ Invalid choice")
            continue
        
        if not error_log.strip():
            print("❌ No error log provided")
            continue
        
        provider = input("AI Provider [auto/groq/huggingface] (default: auto): ").strip() or "auto"
        
        print(f"\n🤖 Analyzing error with {provider}...")
        
        try:
            analysis = await assistant.analyze_error_log(
                error_log=error_log,
                code_context=code_context,
                provider=provider
            )
            
            print("\n✅ Error analysis completed!")
            print("─" * 60)
            print(f"🔍 Error Type: {analysis.error_type}")
            print(f"📝 Error Message: {analysis.error_message}")
            print(f"⚠️  Severity: {analysis.severity.title()}")
            print(f"🎯 Confidence: {analysis.confidence:.0%}")
            
            if analysis.suggested_fixes:
                print("\n🔧 Suggested Fixes:")
                for i, fix in enumerate(analysis.suggested_fixes, 1):
                    print(f"  {i}. {fix}")
            
            if analysis.code_suggestions:
                print("\n💡 Code Suggestions:")
                for suggestion in analysis.code_suggestions:
                    print("─" * 40)
                    print(suggestion)
                    print("─" * 40)
            
            if analysis.related_docs:
                print("\n📚 Related Documentation:")
                for doc in analysis.related_docs:
                    print(f"  • {doc}")
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")


async def batch_generate_code(descriptions_file: str, output_dir: str):
    """Batch code generation from file."""
    print(f"📦 Batch Code Generation")
    print(f"Input: {descriptions_file}")
    print(f"Output: {output_dir}")
    print("=" * 40)
    
    assistant = CodeAssistant()
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(descriptions_file, 'r') as f:
            descriptions = [line.strip() for line in f if line.strip()]
        
        print(f"📋 Found {len(descriptions)} code descriptions")
        
        for i, description in enumerate(descriptions, 1):
            print(f"\n🔄 Processing {i}/{len(descriptions)}: {description[:50]}...")
            
            try:
                snippet = await assistant.generate_code_snippet(
                    description=description,
                    category="general",
                    complexity="medium",
                    include_tests=True
                )
                
                # Save to file
                filename = f"snippet_{i:03d}.py"
                filepath = output_path / filename
                
                with open(filepath, 'w') as f:
                    f.write(f"# {description}\n")
                    f.write(f"# Generated: {snippet.description}\n\n")
                    f.write(snippet.code)
                    
                    if snippet.test_code:
                        f.write(f"\n\n# Test Code\n")
                        f.write(snippet.test_code)
                
                print(f"✅ Saved to {filename}")
                
            except Exception as e:
                print(f"❌ Error processing description {i}: {e}")
        
        print(f"\n🎉 Batch generation completed! Files saved to {output_dir}")
        
    except Exception as e:
        print(f"❌ Error in batch processing: {e}")


def show_templates():
    """Show available prompt templates."""
    print("📋 Available Prompt Templates")
    print("=" * 40)
    
    templates = prompt_manager.list_templates()
    
    for template in templates:
        print(f"\n📝 {template.name}")
        print(f"   Type: {template.type.value}")
        print(f"   Description: {template.description}")
        print(f"   Variables: {', '.join(template.variables[:5])}{'...' if len(template.variables) > 5 else ''}")
        
        if template.examples:
            print(f"   Examples:")
            for example in template.examples[:2]:
                print(f"     • {example}")


async def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(description="AI Code Assistant")
    parser.add_argument("mode", choices=["generate", "debug", "batch", "templates", "ui"], 
                       help="Mode to run")
    parser.add_argument("--input", "-i", help="Input file for batch mode")
    parser.add_argument("--output", "-o", help="Output directory for batch mode")
    parser.add_argument("--provider", "-p", choices=["auto", "groq", "huggingface"], 
                       default="auto", help="AI provider to use")
    
    args = parser.parse_args()
    
    if args.mode == "generate":
        await generate_code_interactive()
    
    elif args.mode == "debug":
        await debug_error_interactive()
    
    elif args.mode == "batch":
        if not args.input or not args.output:
            print("❌ Batch mode requires --input and --output arguments")
            return
        await batch_generate_code(args.input, args.output)
    
    elif args.mode == "templates":
        show_templates()
    
    elif args.mode == "ui":
        print("🚀 Starting Streamlit UI...")
        import subprocess
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "src/ui/code_assistant_interface.py"
        ])
    
    else:
        parser.print_help()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
