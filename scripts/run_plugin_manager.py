#!/usr/bin/env python3
"""
<PERSON>ript to run the External Plugin Manager with various modes.
"""

import sys
import asyncio
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.plugin_manager import (
    ExternalPluginManager,
    process_message_with_plugins,
    search_gifs,
    generate_music,
    get_weather,
    search_photos,
    create_plugin_response
)


async def interactive_plugin_chat():
    """Interactive chat mode with plugin detection."""
    print("🔌 Interactive Plugin Chat")
    print("=" * 40)
    print("Chat with AI assistant. Plugins will be automatically detected and executed.")
    print("Type 'quit' to exit, 'help' for commands.\n")
    
    while True:
        message = input("You: ")
        
        if message.lower() in ['quit', 'exit', 'q']:
            break
        
        if message.lower() == 'help':
            print("\n📋 Available Commands:")
            print("- quit/exit/q: Exit the chat")
            print("- help: Show this help")
            print("- plugins: List available plugins")
            print("\n🔌 Plugin Keywords:")
            print("- Music: 'generate music', 'create audio', 'make song'")
            print("- GIFs: 'show gif', 'funny gif', 'animated image'")
            print("- Photos: 'find photos', 'show pictures', 'search images'")
            print("- Weather: 'weather in', 'temperature', 'forecast'")
            print("- News: 'latest news', 'headlines', 'breaking news'")
            print("- Fun: 'tell joke', 'random quote', 'cat fact'")
            continue
        
        if message.lower() == 'plugins':
            manager = ExternalPluginManager()
            plugins = manager.get_available_plugins()
            
            print("\n🔌 Available Plugins:")
            for plugin in plugins:
                status = "✅" if plugin['enabled'] else "❌"
                print(f"{status} {plugin['name']}: {plugin['description']}")
            continue
        
        if not message.strip():
            continue
        
        print("🤖 Processing...")
        
        try:
            result = await process_message_with_plugins(message, max_results=3)
            
            if result.get('has_plugin_result'):
                plugin_result = result['plugin_result']
                intent = result['intent']
                
                print(f"\n🔌 Plugin detected: {intent['plugin_name']}")
                print(f"📊 Confidence: {intent['confidence']:.0%}")
                print(f"🔍 Search query: '{intent['search_query']}'")
                
                if plugin_result.success:
                    print(f"✅ Plugin executed successfully!")
                    print(f"📄 Content type: {plugin_result.content_type}")
                    
                    # Show metadata summary
                    if plugin_result.metadata:
                        if 'count' in plugin_result.metadata:
                            print(f"📊 Results: {plugin_result.metadata['count']}")
                        
                        # Show specific content info
                        if plugin_result.content_type == "audio" and plugin_result.content_url:
                            print(f"🎵 Audio saved: {plugin_result.content_url}")
                        elif plugin_result.content_type == "gif":
                            gifs = plugin_result.metadata.get('gifs', [])
                            if gifs:
                                print(f"🎭 First GIF: {gifs[0].get('title', 'Untitled')}")
                        elif plugin_result.content_type == "image":
                            photos = plugin_result.metadata.get('photos', [])
                            if photos:
                                print(f"📷 First photo by: {photos[0].get('photographer', 'Unknown')}")
                        elif plugin_result.content_type == "json":
                            if plugin_result.plugin_name == "weather":
                                weather = plugin_result.metadata
                                if 'main' in weather:
                                    temp = weather['main']['temp']
                                    desc = weather['weather'][0]['description']
                                    print(f"🌤️ {weather['name']}: {temp}°C, {desc}")
                    
                    # Ask if user wants to see HTML embed
                    show_html = input("\n📄 Show HTML embed? [y/N]: ").strip().lower() in ['y', 'yes']
                    if show_html and plugin_result.embed_html:
                        print("\n📄 HTML Embed:")
                        print("─" * 60)
                        print(plugin_result.embed_html[:500] + "..." if len(plugin_result.embed_html) > 500 else plugin_result.embed_html)
                        print("─" * 60)
                else:
                    print(f"❌ Plugin failed: {plugin_result.error_message}")
            else:
                print("ℹ️ No plugin detected for this message")
                print("💬 This would be handled by regular chat AI")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()  # Empty line for readability


async def test_specific_plugin(plugin_name: str, query: str = ""):
    """Test a specific plugin."""
    print(f"🧪 Testing Plugin: {plugin_name}")
    print("=" * 40)
    
    try:
        if plugin_name == "giphy":
            query = query or "funny cats"
            print(f"Searching GIFs for: '{query}'")
            result = await search_gifs(query, max_results=3)
            
        elif plugin_name == "huggingface_audio":
            query = query or "happy upbeat music"
            print(f"Generating music: '{query}'")
            result = await generate_music(query)
            
        elif plugin_name == "weather":
            query = query or "London"
            print(f"Getting weather for: '{query}'")
            result = await get_weather(query)
            
        elif plugin_name == "unsplash":
            query = query or "nature"
            print(f"Searching photos for: '{query}'")
            result = await search_photos(query, max_results=3)
            
        else:
            # Generic plugin test
            manager = ExternalPluginManager()
            
            if plugin_name not in manager.plugins:
                print(f"❌ Plugin '{plugin_name}' not found")
                return
            
            intent = {
                "plugin_name": plugin_name,
                "plugin_config": manager.plugins[plugin_name],
                "search_query": query,
                "confidence": 1.0
            }
            
            result = await manager.execute_plugin(intent, max_results=3)
        
        # Display results
        if result.success:
            print(f"✅ Plugin test successful!")
            print(f"📄 Content type: {result.content_type}")
            
            if result.metadata:
                print(f"📊 Metadata keys: {list(result.metadata.keys())}")
                
                if 'count' in result.metadata:
                    print(f"📊 Results count: {result.metadata['count']}")
            
            if result.embed_html:
                print(f"📄 HTML embed available ({len(result.embed_html)} chars)")
                
                show_embed = input("Show HTML embed? [y/N]: ").strip().lower() in ['y', 'yes']
                if show_embed:
                    print("\n📄 HTML Embed:")
                    print("─" * 60)
                    print(result.embed_html)
                    print("─" * 60)
        else:
            print(f"❌ Plugin test failed: {result.error_message}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")


async def list_plugins():
    """List all available plugins."""
    print("🔌 Available Plugins")
    print("=" * 40)
    
    manager = ExternalPluginManager()
    plugins = manager.get_available_plugins()
    
    # Group by content type
    by_type = {}
    for plugin in plugins:
        content_type = plugin['content_type']
        if content_type not in by_type:
            by_type[content_type] = []
        by_type[content_type].append(plugin)
    
    for content_type, type_plugins in by_type.items():
        print(f"\n📄 {content_type.upper()} Plugins:")
        
        for plugin in type_plugins:
            status = "✅ Enabled" if plugin['enabled'] else "❌ Disabled"
            api_key = "🔑 API Key Required" if plugin['api_key_required'] else "🆓 Free"
            
            print(f"  🔌 {plugin['name']}")
            print(f"     {plugin['description']}")
            print(f"     Status: {status} | {api_key}")
            print(f"     Keywords: {', '.join(plugin['keywords'][:5])}...")
            print()
    
    # Show statistics
    total = len(plugins)
    enabled = sum(1 for p in plugins if p['enabled'])
    api_required = sum(1 for p in plugins if p['api_key_required'])
    
    print(f"📊 Statistics:")
    print(f"   Total plugins: {total}")
    print(f"   Enabled: {enabled}")
    print(f"   Disabled: {total - enabled}")
    print(f"   Require API key: {api_required}")
    print(f"   Free to use: {total - api_required}")


async def batch_test_messages(input_file: str):
    """Test multiple messages from file."""
    print(f"📦 Batch Testing Messages")
    print(f"Input file: {input_file}")
    print("=" * 40)
    
    try:
        with open(input_file, 'r') as f:
            messages = [line.strip() for line in f if line.strip()]
        
        print(f"📋 Found {len(messages)} messages to test")
        
        results = []
        
        for i, message in enumerate(messages, 1):
            print(f"\n🔄 Testing {i}/{len(messages)}: {message[:50]}...")
            
            try:
                result = await process_message_with_plugins(message, max_results=2)
                
                if result.get('has_plugin_result'):
                    plugin_result = result['plugin_result']
                    intent = result['intent']
                    
                    results.append({
                        'message': message,
                        'plugin': intent['plugin_name'],
                        'confidence': intent['confidence'],
                        'success': plugin_result.success,
                        'error': plugin_result.error_message if not plugin_result.success else None
                    })
                    
                    print(f"   ✅ Plugin: {intent['plugin_name']} ({intent['confidence']:.0%})")
                else:
                    results.append({
                        'message': message,
                        'plugin': None,
                        'confidence': 0,
                        'success': True,
                        'error': None
                    })
                    
                    print(f"   ℹ️ No plugin detected")
                    
            except Exception as e:
                results.append({
                    'message': message,
                    'plugin': None,
                    'confidence': 0,
                    'success': False,
                    'error': str(e)
                })
                
                print(f"   ❌ Error: {e}")
        
        # Summary
        print(f"\n📊 Batch Test Summary:")
        plugin_detected = sum(1 for r in results if r['plugin'])
        successful = sum(1 for r in results if r['success'])
        
        print(f"   Messages processed: {len(results)}")
        print(f"   Plugins detected: {plugin_detected}")
        print(f"   Successful: {successful}")
        print(f"   Failed: {len(results) - successful}")
        
        # Plugin usage
        plugin_usage = {}
        for r in results:
            if r['plugin']:
                plugin_usage[r['plugin']] = plugin_usage.get(r['plugin'], 0) + 1
        
        if plugin_usage:
            print(f"\n🔌 Plugin Usage:")
            for plugin, count in sorted(plugin_usage.items(), key=lambda x: x[1], reverse=True):
                print(f"   {plugin}: {count} times")
        
    except Exception as e:
        print(f"❌ Batch test error: {e}")


def show_examples():
    """Show example messages for testing."""
    print("📋 Example Messages for Testing")
    print("=" * 40)
    
    examples = {
        "🎵 Music & Audio": [
            "Generate relaxing piano music",
            "Create upbeat electronic dance music",
            "Make a sad violin melody",
            "Generate nature sounds"
        ],
        "🎭 GIFs & Animation": [
            "Show me funny cat GIFs",
            "Find dancing GIFs",
            "Get reaction GIFs for happy",
            "Show animated memes"
        ],
        "📷 Photos & Images": [
            "Find beautiful sunset photos",
            "Show me mountain landscapes",
            "Get coffee shop pictures",
            "Find nature photography"
        ],
        "📺 Videos": [
            "Find cooking tutorial videos",
            "Search for music videos",
            "Get programming tutorials",
            "Find funny animal videos"
        ],
        "🌤️ Weather & Data": [
            "What's the weather in Tokyo?",
            "Get weather forecast for London",
            "Temperature in New York",
            "Climate in Paris today"
        ],
        "📰 News & Information": [
            "Latest technology news",
            "Breaking news headlines",
            "Get science news",
            "Current events today"
        ],
        "😄 Fun & Entertainment": [
            "Tell me a random joke",
            "Give me an inspirational quote",
            "Share a cat fact",
            "Random fun fact"
        ]
    }
    
    for category, messages in examples.items():
        print(f"\n{category}:")
        for message in messages:
            print(f"  • {message}")


async def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(description="External Plugin Manager")
    parser.add_argument("mode", choices=["chat", "test", "list", "batch", "examples", "ui"], 
                       help="Mode to run")
    parser.add_argument("--plugin", "-p", help="Plugin name for test mode")
    parser.add_argument("--query", "-q", help="Query for test mode")
    parser.add_argument("--input", "-i", help="Input file for batch mode")
    
    args = parser.parse_args()
    
    if args.mode == "chat":
        await interactive_plugin_chat()
    
    elif args.mode == "test":
        if not args.plugin:
            print("❌ Test mode requires --plugin argument")
            return
        await test_specific_plugin(args.plugin, args.query or "")
    
    elif args.mode == "list":
        await list_plugins()
    
    elif args.mode == "batch":
        if not args.input:
            print("❌ Batch mode requires --input argument")
            return
        await batch_test_messages(args.input)
    
    elif args.mode == "examples":
        show_examples()
    
    elif args.mode == "ui":
        print("🚀 Starting Streamlit UI...")
        import subprocess
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "src/ui/plugin_interface.py"
        ])
    
    else:
        parser.print_help()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
