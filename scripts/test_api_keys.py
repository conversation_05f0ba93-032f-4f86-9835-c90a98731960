#!/usr/bin/env python3
"""
Script to test and validate API keys for Chat AI project.
"""

import sys
import asyncio
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config.logging_config import app_logger
from utils.api_key_tester import APIKeyTester, test_all_api_keys
from utils.env_loader import validate_api_keys_from_env
from utils.client_factory import get_available_providers
from config.api_keys import api_key_manager


async def main():
    """Main function for API key testing."""
    parser = argparse.ArgumentParser(description="Test Chat AI API keys")
    parser.add_argument("--provider", choices=["groq", "huggingface", "all"], 
                       default="all", help="Provider to test")
    parser.add_argument("--benchmark", action="store_true", 
                       help="Run performance benchmark")
    parser.add_argument("--streaming", action="store_true", 
                       help="Test streaming capabilities")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="Verbose output")
    parser.add_argument("--report", action="store_true", 
                       help="Generate detailed report")
    
    args = parser.parse_args()
    
    print("🔑 Chat AI - API Key Tester")
    print("=" * 40)
    
    # Initialize tester
    tester = APIKeyTester()
    
    try:
        # Basic validation first
        print("\n📋 Step 1: Environment Validation")
        env_validation = validate_api_keys_from_env()
        
        for provider, is_valid in env_validation.items():
            status = "✅" if is_valid else "❌"
            print(f"  {provider.capitalize()}: {status}")
        
        if not any(env_validation.values()):
            print("\n❌ No valid API keys found in environment!")
            print("Please check your .env file and ensure API keys are properly set.")
            return 1
        
        # Test API connections
        print("\n🔌 Step 2: API Connection Tests")
        
        if args.provider == "all":
            results = await tester.test_all_api_keys()
        elif args.provider == "groq":
            results = {"groq": await tester.test_groq_api_key()}
        elif args.provider == "huggingface":
            results = {"huggingface": await tester.test_huggingface_api_key()}
        
        # Display results
        success_count = 0
        for provider, result in results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"  {provider.capitalize()}: {status}")
            
            if result["success"]:
                success_count += 1
                if args.verbose:
                    response_time = result.get("response_time", 0)
                    print(f"    Response time: {response_time:.2f}s")
                    
                    if result.get("model_info"):
                        model_info = result["model_info"]
                        print(f"    Model: {model_info.get('model_name', 'Unknown')}")
                        
                        if provider == "huggingface" and model_info.get("using_local"):
                            print("    Mode: Local model")
            else:
                error = result.get("error", "Unknown error")
                print(f"    Error: {error}")
        
        # Streaming tests
        if args.streaming:
            print("\n🌊 Step 3: Streaming Tests")
            streaming_results = await tester.test_streaming_capabilities()
            
            for provider, result in streaming_results.items():
                if result["streaming_success"]:
                    chunks = result["chunks_received"]
                    print(f"  {provider.capitalize()}: ✅ {chunks} chunks received")
                else:
                    error = result.get("error", "Unknown error")
                    print(f"  {provider.capitalize()}: ❌ {error}")
        
        # Benchmark tests
        if args.benchmark:
            print("\n⚡ Step 4: Performance Benchmark")
            print("Running 3 requests per provider...")
            
            benchmark_results = await tester.benchmark_response_times(3)
            
            for provider, result in benchmark_results.items():
                if "error" not in result:
                    avg_time = result["avg_response_time"]
                    success_rate = result["success_rate"]
                    print(f"  {provider.capitalize()}:")
                    print(f"    Avg response time: {avg_time:.2f}s")
                    print(f"    Success rate: {success_rate:.1f}%")
                else:
                    print(f"  {provider.capitalize()}: ❌ {result['error']}")
        
        # Generate report
        if args.report:
            print("\n📊 Detailed Report")
            print("-" * 40)
            report = tester.generate_test_report()
            print(report)
        
        # Summary
        print(f"\n📈 Summary: {success_count}/{len(results)} providers working")
        
        if success_count == 0:
            print("\n❌ No API keys are working. Please check your configuration.")
            return 1
        elif success_count < len(results):
            print("\n⚠️  Some API keys are not working. Check the errors above.")
            return 0
        else:
            print("\n✅ All API keys are working correctly!")
            return 0
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def setup_api_keys():
    """Interactive setup for API keys."""
    print("🔧 API Key Setup")
    print("=" * 30)
    
    env_file = Path(".env")
    
    # Check if .env exists
    if not env_file.exists():
        print("Creating .env file from template...")
        from utils.env_loader import SecureEnvLoader
        loader = SecureEnvLoader()
        loader.create_env_template()
    
    print("\nPlease edit your .env file with the following API keys:")
    print("\n1. Groq API Key:")
    print("   - Visit: https://console.groq.com/")
    print("   - Create account and generate API key")
    print("   - Format: gsk_xxxxxxxxxxxxxxxxxx")
    
    print("\n2. Hugging Face API Key:")
    print("   - Visit: https://huggingface.co/settings/tokens")
    print("   - Create a new token")
    print("   - Format: hf_xxxxxxxxxxxxxxxxxx")
    
    print(f"\nEdit the file: {env_file.absolute()}")
    print("\nAfter editing, run: python scripts/test_api_keys.py")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "setup":
        setup_api_keys()
    else:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
