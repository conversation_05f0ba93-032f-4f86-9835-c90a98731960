#!/usr/bin/env python3
"""
Simple lightweight web interface for AI Chat System.
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import threading
import time

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class ChatHandler(BaseHTTPRequestHandler):
    """Simple HTTP handler for chat interface."""
    
    def do_GET(self):
        """Handle GET requests."""
        path = urlparse(self.path).path
        
        if path == '/' or path == '/chat':
            self.serve_chat_page()
        elif path == '/api/test':
            self.serve_api_test()
        elif path == '/status':
            self.serve_status()
        else:
            self.send_error(404)
    
    def do_POST(self):
        """Handle POST requests."""
        path = urlparse(self.path).path
        
        if path == '/api/chat':
            self.handle_chat_message()
        else:
            self.send_error(404)
    
    def serve_chat_page(self):
        """Serve main chat page."""
        html = """<!DOCTYPE html>
<html>
<head>
    <title>AI Chat - Real API Test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { background: #e8f5e8; padding: 15px; border-radius: 6px; margin-bottom: 20px; }
        .chat-area { border: 1px solid #ddd; height: 400px; overflow-y: auto; padding: 15px; margin-bottom: 15px; background: #fafafa; }
        .input-area { display: flex; gap: 10px; }
        .input-area input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .input-area button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .message { margin: 10px 0; padding: 10px; border-radius: 6px; }
        .user { background: #e3f2fd; text-align: right; }
        .ai { background: #f1f8e9; }
        .error { background: #ffebee; color: #c62828; }
        .loading { color: #666; font-style: italic; }
        .api-test { margin: 20px 0; }
        .api-result { margin: 5px 0; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 0.9em; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .failed { background: #ffebee; color: #c62828; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Chat System</h1>
            <p>Real API Integration Test</p>
        </div>
        
        <div class="status" id="status">
            <strong>📊 System Status:</strong> <span id="statusText">Loading...</span>
        </div>
        
        <div class="api-test">
            <h3>🧪 API Test Results</h3>
            <div id="apiResults">Testing APIs...</div>
            <button onclick="testAPIs()" style="margin-top: 10px; padding: 8px 16px; background: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 Refresh Tests</button>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message ai">
                <strong>🤖 AI:</strong> Hello! I'm ready to test real APIs. Try asking for:
                <ul>
                    <li>🎭 "Show me celebration GIFs"</li>
                    <li>🌤️ "Weather in Tokyo"</li>
                    <li>🤖 "Tell me about AI"</li>
                    <li>📷 "Find sunset photos"</li>
                </ul>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        // Test APIs on load
        window.onload = function() {
            testAPIs();
        };
        
        function testAPIs() {
            document.getElementById('apiResults').innerHTML = 'Testing APIs...';
            
            fetch('/api/test')
                .then(response => response.json())
                .then(data => {
                    let html = '';
                    for (let api in data.results) {
                        let result = data.results[api];
                        let className = result.success ? 'success' : 'failed';
                        let icon = result.success ? '✅' : '❌';
                        html += `<div class="api-result ${className}">${icon} ${api}: ${result.message}</div>`;
                    }
                    document.getElementById('apiResults').innerHTML = html;
                    
                    let working = Object.values(data.results).filter(r => r.success).length;
                    let total = Object.keys(data.results).length;
                    document.getElementById('statusText').innerHTML = `${working}/${total} APIs working`;
                })
                .catch(error => {
                    document.getElementById('apiResults').innerHTML = '<div class="api-result failed">❌ Error testing APIs</div>';
                });
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function sendMessage() {
            let input = document.getElementById('messageInput');
            let message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            input.value = '';
            
            // Add loading message
            addMessage('Processing...', 'ai loading');
            
            // Send to API
            fetch('/api/chat', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({message: message})
            })
            .then(response => response.json())
            .then(data => {
                // Remove loading message
                let chatArea = document.getElementById('chatArea');
                let lastMessage = chatArea.lastElementChild;
                if (lastMessage && lastMessage.classList.contains('loading')) {
                    chatArea.removeChild(lastMessage);
                }
                
                // Add AI response
                if (data.success) {
                    addMessage(data.response, 'ai');
                } else {
                    addMessage('Error: ' + data.error, 'error');
                }
            })
            .catch(error => {
                // Remove loading message
                let chatArea = document.getElementById('chatArea');
                let lastMessage = chatArea.lastElementChild;
                if (lastMessage && lastMessage.classList.contains('loading')) {
                    chatArea.removeChild(lastMessage);
                }
                addMessage('Network error: ' + error.message, 'error');
            });
        }
        
        function addMessage(text, type) {
            let chatArea = document.getElementById('chatArea');
            let messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            
            if (type === 'user') {
                messageDiv.innerHTML = '<strong>👤 You:</strong> ' + text;
            } else if (type.includes('loading')) {
                messageDiv.innerHTML = '<strong>🤖 AI:</strong> ' + text;
            } else if (type === 'error') {
                messageDiv.innerHTML = '<strong>❌ Error:</strong> ' + text;
            } else {
                messageDiv.innerHTML = '<strong>🤖 AI:</strong> ' + text;
            }
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
    </script>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def serve_api_test(self):
        """Test API keys and return results."""
        results = {}
        
        # Load environment
        self.load_env()
        
        # Test Giphy
        try:
            import requests
            api_key = os.getenv('GIPHY_API_KEY')
            if api_key:
                response = requests.get(f'https://api.giphy.com/v1/gifs/search?api_key={api_key}&q=test&limit=1', timeout=5)
                if response.status_code == 200:
                    results['Giphy'] = {'success': True, 'message': 'API working'}
                else:
                    results['Giphy'] = {'success': False, 'message': f'HTTP {response.status_code}'}
            else:
                results['Giphy'] = {'success': False, 'message': 'No API key'}
        except Exception as e:
            results['Giphy'] = {'success': False, 'message': str(e)}
        
        # Test Weather
        try:
            api_key = os.getenv('OPENWEATHER_API_KEY')
            if api_key:
                response = requests.get(f'https://api.openweathermap.org/data/2.5/weather?appid={api_key}&q=London', timeout=5)
                if response.status_code == 200:
                    results['Weather'] = {'success': True, 'message': 'API working'}
                else:
                    results['Weather'] = {'success': False, 'message': f'HTTP {response.status_code}'}
            else:
                results['Weather'] = {'success': False, 'message': 'No API key'}
        except Exception as e:
            results['Weather'] = {'success': False, 'message': str(e)}
        
        # Test Groq
        try:
            api_key = os.getenv('GROQ_API_KEY')
            if api_key:
                response = requests.post(
                    'https://api.groq.com/openai/v1/chat/completions',
                    headers={'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'},
                    json={'messages': [{'role': 'user', 'content': 'Hi'}], 'model': 'mixtral-8x7b-32768', 'max_tokens': 5},
                    timeout=10
                )
                if response.status_code == 200:
                    results['Groq AI'] = {'success': True, 'message': 'API working'}
                else:
                    results['Groq AI'] = {'success': False, 'message': f'HTTP {response.status_code}'}
            else:
                results['Groq AI'] = {'success': False, 'message': 'No API key'}
        except Exception as e:
            results['Groq AI'] = {'success': False, 'message': str(e)}
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'results': results}).encode())
    
    def handle_chat_message(self):
        """Handle chat message."""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode())
            
            message = data.get('message', '')
            
            # Simple response for now
            response = f"You said: '{message}'. Real API integration would process this with the plugin system!"
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': True, 'response': response}).encode())
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode())
    
    def serve_status(self):
        """Serve system status."""
        self.load_env()
        
        keys = ['GROQ_API_KEY', 'GIPHY_API_KEY', 'OPENWEATHER_API_KEY', 'HUGGINGFACE_API_KEY', 'UNSPLASH_API_KEY']
        configured = sum(1 for key in keys if os.getenv(key))
        
        status = {
            'configured_apis': configured,
            'total_apis': len(keys),
            'status': 'ready' if configured >= 2 else 'partial'
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status).encode())
    
    def load_env(self):
        """Load .env file."""
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file) as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value.strip()

def run_server(port=3000):
    """Run the web server."""
    server = HTTPServer(('localhost', port), ChatHandler)
    print(f"🌐 Starting lightweight server on http://localhost:{port}")
    print(f"📱 Open in browser: http://localhost:{port}")
    print(f"🔧 Press Ctrl+C to stop")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print(f"\n👋 Server stopped")
        server.shutdown()

if __name__ == "__main__":
    # Change to script directory
    os.chdir(Path(__file__).parent)
    run_server()
