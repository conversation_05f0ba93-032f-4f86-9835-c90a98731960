# API Keys
GROQ_API_KEY=********************************************************
HUGGINGFACE_API_KEY=*************************************
# Application Settings
APP_NAME=Chat AI
APP_VERSION=1.0.0
DEBUG=True
LOG_LEVEL=INFO

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=4

# Database Configuration (if using database)
DATABASE_URL=sqlite:///./data/chat_ai.db
# DATABASE_URL=postgresql://user:password@localhost/chat_ai

# Redis Configuration (for caching/sessions)
REDIS_URL=redis://localhost:6379/0

# Model Configuration
DEFAULT_MODEL=groq
GROQ_MODEL=mixtral-8x7b-32768
HUGGINGFACE_MODEL=microsoft/DialoGPT-medium

# Chat Settings
MAX_CONVERSATION_LENGTH=50
MAX_MESSAGE_LENGTH=2000
SESSION_TIMEOUT=3600

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
ALLOWED_METHODS=GET,POST,PUT,DELETE
ALLOWED_HEADERS=*

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=txt,pdf,docx

# Logging
LOG_FILE=./data/logs/app.log
LOG_ROTATION=1 week
LOG_RETENTION=4 weeks
