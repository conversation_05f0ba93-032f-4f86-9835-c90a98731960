# 🧪 Testing Guide - AI Chat System

Panduan lengkap untuk menguji semua komponen sistem AI Chat dengan External Plugins.

## 📋 Overview Testing

Sistem ini memiliki 3 komponen utama yang dapat ditest:
1. **🐍 Code Assistant** - Generate code & auto-debug
2. **🏗️ Diagram Generator** - Create architecture diagrams
3. **🔌 Plugin Manager** - External API integration

## 🚀 Quick Start Testing

### 1. Setup Environment

```bash
# Clone/navigate to project directory
cd "/path/to/your/project"

# Install basic dependencies (optional)
pip install --user aiohttp requests pydantic python-dotenv streamlit

# Or create virtual environment
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows
pip install -r requirements.txt
```

### 2. Test Basic Demo (No Dependencies)

```bash
# Run simple demo
python3 demo_simple.py

# Expected output: Plugin detection, HTML embeds, chat simulation
```

## 🧪 Testing Methods

### Method 1: Simple Demo (Recommended untuk Start)

```bash
# Test plugin detection and HTML generation
python3 demo_simple.py
```

**Expected Output:**
```
🔌 Plugin Detection Demo
📝 Message: 'Show me funny cat GIFs'
✅ Plugin detected: gif_search
   Confidence: 67%
   Search query: 'cat GIFs'
```

### Method 2: Individual Component Testing

#### A. Test Code Assistant

```bash
# Test code generation
python3 -c "
import sys
sys.path.insert(0, 'src')
from core.code_assistant import CodeAssistant
import asyncio

async def test():
    assistant = CodeAssistant()
    # Test without API (will use fallback)
    snippet = await assistant.generate_code_snippet(
        'Create a function that calculates fibonacci numbers',
        provider='auto'
    )
    print('Code generated:', len(snippet.code), 'characters')

asyncio.run(test())
"
```

#### B. Test Diagram Generator

```bash
# Test diagram generation
python3 -c "
import sys
sys.path.insert(0, 'src')
from core.diagram_generator import DiagramGenerator
import asyncio

async def test():
    generator = DiagramGenerator()
    diagram = await generator.generate_diagram_from_text(
        'Simple web app with frontend and backend'
    )
    print('Diagram generated:', diagram.title)
    print('Components:', len(diagram.components))

asyncio.run(test())
"
```

#### C. Test Plugin Manager

```bash
# Test plugin detection
python3 -c "
import sys
sys.path.insert(0, 'src')
from core.plugin_manager import ExternalPluginManager
import asyncio

async def test():
    manager = ExternalPluginManager()
    intent = await manager.detect_plugin_intent('Show me funny GIFs')
    print('Plugin detected:', intent['plugin_name'] if intent else 'None')

asyncio.run(test())
"
```

### Method 3: Interactive Testing

#### A. Interactive Plugin Chat

```bash
# Run interactive chat with plugin detection
python3 scripts/run_plugin_manager.py chat
```

**Commands to try:**
```
Show me funny cat GIFs
Generate relaxing music
What's the weather in Tokyo?
Find beautiful sunset photos
Tell me a joke
```

#### B. Interactive Code Assistant

```bash
# Run interactive code generation
python3 scripts/run_code_assistant.py generate
```

**Prompts to try:**
```
Create a function that sorts a list of dictionaries
Build a REST API client for weather service
Make a data analysis script for CSV files
```

#### C. Interactive Diagram Generator

```bash
# Run interactive diagram generation
python3 scripts/run_diagram_generator.py interactive
```

**Descriptions to try:**
```
E-commerce microservices with React frontend
Data pipeline with Kafka and Spark
Serverless web application on AWS
```

### Method 4: Streamlit UI Testing

#### A. Plugin Interface

```bash
# Start plugin interface
streamlit run src/ui/plugin_interface.py
```

**Test Features:**
- Plugin overview and management
- Message processing with auto-detection
- Individual plugin testing
- HTML embed display

#### B. Code Assistant Interface

```bash
# Start code assistant interface
streamlit run src/ui/code_assistant_interface.py
```

**Test Features:**
- Code generation with different categories
- Error debugging with sample logs
- Code testing and validation

#### C. Diagram Interface

```bash
# Start diagram interface
streamlit run src/ui/diagram_interface.py
```

**Test Features:**
- Architecture diagram generation
- Mermaid code generation
- Visual diagram display

### Method 5: Batch Testing

#### A. Test Multiple Messages

```bash
# Create test file
echo "Show me funny GIFs
Generate music
Weather in London
Find nature photos
Tell me a joke" > test_messages.txt

# Run batch test
python3 scripts/run_plugin_manager.py batch -i test_messages.txt
```

#### B. Test Multiple Code Descriptions

```bash
# Create code descriptions
echo "Create a function that calculates factorial
Build a class for handling JSON data
Make a web scraper for product prices" > code_descriptions.txt

# Run batch generation
python3 scripts/run_code_assistant.py batch -i code_descriptions.txt -o output/
```

## 🔧 Testing Specific Features

### 1. Plugin Detection Accuracy

```bash
python3 -c "
import sys
sys.path.insert(0, 'src')
from core.plugin_manager import ExternalPluginManager
import asyncio

async def test_accuracy():
    manager = ExternalPluginManager()
    
    test_cases = [
        ('Show me GIFs', 'giphy'),
        ('Generate music', 'huggingface_audio'),
        ('Weather forecast', 'weather'),
        ('Find photos', 'unsplash'),
        ('Tell joke', 'jokes'),
        ('Hello world', None)  # Should not trigger
    ]
    
    correct = 0
    for message, expected in test_cases:
        intent = await manager.detect_plugin_intent(message)
        detected = intent['plugin_name'] if intent else None
        
        if detected == expected:
            correct += 1
            print(f'✅ {message} -> {detected}')
        else:
            print(f'❌ {message} -> Expected: {expected}, Got: {detected}')
    
    print(f'Accuracy: {correct}/{len(test_cases)} ({correct/len(test_cases)*100:.1f}%)')

asyncio.run(test_accuracy())
"
```

### 2. HTML Embed Generation

```bash
python3 -c "
import sys
sys.path.insert(0, 'src')
exec(open('demo_simple.py').read())

# Test all embed types
manager = SimplePluginManager()
test_plugins = ['gif_search', 'music_generator', 'weather', 'photo_search', 'joke_generator']

for plugin in test_plugins:
    intent = {'plugin_name': plugin, 'search_query': 'test'}
    result = manager.execute_plugin(intent)
    
    if result['success'] and 'embed_html' in result:
        print(f'✅ {plugin}: {len(result[\"embed_html\"])} chars')
    else:
        print(f'❌ {plugin}: Failed')
"
```

### 3. Error Handling

```bash
python3 -c "
import sys
sys.path.insert(0, 'src')
from core.plugin_manager import ExternalPluginManager
import asyncio

async def test_errors():
    manager = ExternalPluginManager()
    
    # Test edge cases
    test_cases = [
        '',  # Empty string
        '   ',  # Whitespace
        'a' * 1000,  # Very long
        '🎵🎭🌤️',  # Emoji only
        'Invalid \x00 chars'  # Invalid characters
    ]
    
    for message in test_cases:
        try:
            intent = await manager.detect_plugin_intent(message)
            print(f'✅ Handled: \"{message[:20]}...\"')
        except Exception as e:
            print(f'❌ Error: \"{message[:20]}...\" -> {e}')

asyncio.run(test_errors())
"
```

## 📊 Performance Testing

### 1. Response Time Testing

```bash
python3 -c "
import time
import sys
sys.path.insert(0, 'src')
exec(open('demo_simple.py').read())

manager = SimplePluginManager()
messages = ['Show GIFs', 'Generate music', 'Weather info', 'Find photos', 'Tell joke']

for message in messages:
    start = time.time()
    intent = manager.detect_plugin_intent(message)
    if intent:
        result = manager.execute_plugin(intent)
    end = time.time()
    
    print(f'{message}: {(end-start)*1000:.1f}ms')
"
```

### 2. Memory Usage Testing

```bash
python3 -c "
import psutil
import os
import sys
sys.path.insert(0, 'src')

process = psutil.Process(os.getpid())
initial_memory = process.memory_info().rss / 1024 / 1024

# Load and test system
exec(open('demo_simple.py').read())

final_memory = process.memory_info().rss / 1024 / 1024
print(f'Memory usage: {initial_memory:.1f}MB -> {final_memory:.1f}MB (+{final_memory-initial_memory:.1f}MB)')
"
```

## 🔍 Debugging & Troubleshooting

### 1. Check System Status

```bash
python3 -c "
import sys
sys.path.insert(0, 'src')

# Check Python version
print(f'Python: {sys.version}')

# Check imports
try:
    from core.plugin_manager import ExternalPluginManager
    print('✅ Plugin Manager imported')
except Exception as e:
    print(f'❌ Plugin Manager: {e}')

try:
    from core.code_assistant import CodeAssistant
    print('✅ Code Assistant imported')
except Exception as e:
    print(f'❌ Code Assistant: {e}')

try:
    from core.diagram_generator import DiagramGenerator
    print('✅ Diagram Generator imported')
except Exception as e:
    print(f'❌ Diagram Generator: {e}')
"
```

### 2. Test API Keys (if configured)

```bash
python3 -c "
import os

api_keys = [
    'HUGGINGFACE_API_KEY',
    'GIPHY_API_KEY',
    'OPENWEATHER_API_KEY',
    'GROQ_API_KEY'
]

for key in api_keys:
    value = os.getenv(key)
    if value:
        print(f'✅ {key}: {value[:10]}...')
    else:
        print(f'❌ {key}: Not set')
"
```

### 3. Verbose Error Testing

```bash
python3 -c "
import sys
import traceback
sys.path.insert(0, 'src')

try:
    exec(open('demo_simple.py').read())
    print('✅ Demo completed successfully')
except Exception as e:
    print(f'❌ Demo failed: {e}')
    traceback.print_exc()
"
```

## 📝 Test Results Validation

### Expected Outputs:

1. **Plugin Detection**: 80%+ accuracy on test messages
2. **HTML Generation**: All 5 plugin types generate valid HTML
3. **Error Handling**: No crashes on edge cases
4. **Performance**: <100ms response time for simple operations
5. **Memory**: <50MB additional memory usage

### Success Criteria:

- ✅ All demos run without errors
- ✅ Plugin detection works for common phrases
- ✅ HTML embeds are generated correctly
- ✅ Streamlit interfaces load properly
- ✅ Interactive modes respond to user input

## 🚀 Advanced Testing

### 1. Integration Testing

```bash
# Test full workflow
python3 -c "
import sys
sys.path.insert(0, 'src')
from core.plugin_manager import process_message_with_plugins
import asyncio

async def integration_test():
    messages = [
        'Show me cat GIFs',
        'Generate happy music', 
        'Weather in Tokyo',
        'Find sunset photos',
        'Tell me a joke'
    ]
    
    for message in messages:
        result = await process_message_with_plugins(message)
        success = result.get('has_plugin_result', False)
        print(f'{message}: {\"✅\" if success else \"❌\"}')

asyncio.run(integration_test())
"
```

### 2. Load Testing

```bash
# Test multiple concurrent requests
python3 -c "
import asyncio
import time
import sys
sys.path.insert(0, 'src')
from core.plugin_manager import ExternalPluginManager

async def load_test():
    manager = ExternalPluginManager()
    
    async def test_request():
        return await manager.detect_plugin_intent('Show me GIFs')
    
    start = time.time()
    tasks = [test_request() for _ in range(10)]
    results = await asyncio.gather(*tasks)
    end = time.time()
    
    successful = sum(1 for r in results if r is not None)
    print(f'Load test: {successful}/10 successful in {end-start:.2f}s')

asyncio.run(load_test())
"
```

## 📋 Testing Checklist

- [ ] Basic demo runs successfully
- [ ] Plugin detection works for all types
- [ ] HTML embeds generate correctly
- [ ] Error handling works for edge cases
- [ ] Streamlit interfaces load
- [ ] Interactive modes respond
- [ ] Performance is acceptable
- [ ] Memory usage is reasonable
- [ ] API integrations work (if keys configured)
- [ ] Batch processing works

---

**🧪 Gunakan panduan ini untuk menguji semua aspek sistem secara menyeluruh sebelum deployment ke production.**
