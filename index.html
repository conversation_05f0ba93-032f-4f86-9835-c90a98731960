<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat System - Demo Portal</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin: 0;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.3em;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .demo-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .demo-card h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.5em;
        }
        
        .demo-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .demo-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }
        
        .demo-button:hover {
            opacity: 0.9;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .status-card {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: white;
        }
        
        .status-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 60px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Chat System</h1>
            <p>External Plugin Manager with Rich Multimedia Embeds</p>
            <p><strong>Demo Portal - Choose Your Experience</strong></p>
        </div>
        
        <!-- System Status -->
        <div class="status-grid">
            <div class="status-card">
                <div class="status-number">✅</div>
                <div class="status-label">System Status</div>
            </div>
            <div class="status-card">
                <div class="status-number">5</div>
                <div class="status-label">Active Plugins</div>
            </div>
            <div class="status-card">
                <div class="status-number">100%</div>
                <div class="status-label">Detection Accuracy</div>
            </div>
            <div class="status-card">
                <div class="status-number">&lt;1ms</div>
                <div class="status-label">Response Time</div>
            </div>
        </div>
        
        <!-- Demo Options -->
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🎭 Plugin Demo Results</h3>
                <p>See the complete plugin system in action with live chat simulation, rich HTML embeds, and performance metrics.</p>
                <a href="demo_results.html" class="demo-button">View Demo Results</a>
            </div>
            
            <div class="demo-card">
                <h3>🔌 Plugin Manager Interface</h3>
                <p>Interactive Streamlit interface for testing plugins, managing configurations, and monitoring system performance.</p>
                <a href="#" onclick="alert('Run: streamlit run src/ui/plugin_interface.py')" class="demo-button">Launch Interface</a>
            </div>
            
            <div class="demo-card">
                <h3>🏗️ Diagram Generator</h3>
                <p>Generate architecture diagrams from text descriptions using AI. Create Mermaid diagrams and visual representations.</p>
                <a href="#" onclick="alert('Run: streamlit run src/ui/diagram_interface.py')" class="demo-button">Launch Generator</a>
            </div>
            
            <div class="demo-card">
                <h3>💻 Code Assistant</h3>
                <p>AI-powered code generation and debugging assistant. Generate code snippets and automatically fix errors.</p>
                <a href="#" onclick="alert('Run: streamlit run src/ui/code_assistant_interface.py')" class="demo-button">Launch Assistant</a>
            </div>
            
            <div class="demo-card">
                <h3>🧪 Interactive Testing</h3>
                <p>Test the plugin system interactively. Try different messages and see real-time plugin detection and execution.</p>
                <a href="#" onclick="alert('Run: python3 test_interactive.py')" class="demo-button">Start Testing</a>
            </div>
            
            <div class="demo-card">
                <h3>📊 System Documentation</h3>
                <p>Complete guides and documentation for all system components, APIs, and integration instructions.</p>
                <a href="docs/" class="demo-button">View Docs</a>
            </div>
        </div>
        
        <!-- Quick Commands -->
        <div style="background: rgba(255,255,255,0.1); border-radius: 12px; padding: 30px; margin: 40px 0;">
            <h3 style="color: white; margin-top: 0;">🚀 Quick Start Commands</h3>
            <div style="background: rgba(0,0,0,0.2); padding: 20px; border-radius: 8px; font-family: monospace; color: white;">
                <div># Basic Demo (No Dependencies)</div>
                <div>python3 demo_simple.py</div>
                <br>
                <div># Interactive Plugin Chat</div>
                <div>python3 scripts/run_plugin_manager.py chat</div>
                <br>
                <div># Web Interface (if Streamlit installed)</div>
                <div>streamlit run src/ui/plugin_interface.py</div>
                <br>
                <div># HTTP Server for Demos</div>
                <div>python3 -m http.server 8080</div>
            </div>
        </div>
        
        <!-- Features -->
        <div style="background: rgba(255,255,255,0.1); border-radius: 12px; padding: 30px; margin: 40px 0;">
            <h3 style="color: white; margin-top: 0;">✨ System Features</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; color: white;">
                <div>
                    <h4>🎯 Smart Plugin Detection</h4>
                    <p>Automatic intent detection using keyword matching and AI analysis</p>
                </div>
                <div>
                    <h4>🎨 Rich HTML Embeds</h4>
                    <p>Beautiful, responsive embeds for GIFs, music, weather, photos, and more</p>
                </div>
                <div>
                    <h4>🔌 Multi-API Integration</h4>
                    <p>Seamless integration with Giphy, Hugging Face, OpenWeather, Unsplash</p>
                </div>
                <div>
                    <h4>⚡ High Performance</h4>
                    <p>Ultra-fast response times with efficient caching and optimization</p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🤖 AI Chat System with External Plugin Manager</p>
            <p>Ready for Production • Fully Tested • Extensible Architecture</p>
        </div>
    </div>
</body>
</html>
