<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat - Live Plugin Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f7fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: white;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .sidebar {
            width: 300px;
            background: white;
            margin: 20px 20px 20px 0;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 12px;
            max-width: 80%;
        }
        
        .user-message {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin-left: auto;
        }
        
        .ai-message {
            background: #f1f8e9;
            border-left: 4px solid #4caf50;
        }
        
        .plugin-embed {
            margin: 15px 0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .chat-input {
            background: white;
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
        }
        
        .chat-input input:focus {
            border-color: #667eea;
        }
        
        .send-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .plugin-status {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .plugin-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .plugin-item:last-child {
            border-bottom: none;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }
        
        .quick-actions {
            margin-top: 20px;
        }
        
        .quick-button {
            display: block;
            width: 100%;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            padding: 10px;
            margin: 5px 0;
            border-radius: 6px;
            cursor: pointer;
            text-align: left;
            transition: background 0.3s;
        }
        
        .quick-button:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="chat-header">
        <h1>🤖 AI Chat System - Live Plugin Demo</h1>
        <p>Try typing messages to see automatic plugin detection and rich embeds</p>
    </div>
    
    <div class="chat-container">
        <div class="chat-messages" id="chatMessages">
            <!-- Welcome Message -->
            <div class="message ai-message">
                <strong>🤖 AI Assistant:</strong> Welcome! I can help you with various tasks using external plugins. Try asking for:
                <ul>
                    <li>🎭 "Show me funny GIFs"</li>
                    <li>🎵 "Generate relaxing music"</li>
                    <li>🌤️ "What's the weather in Tokyo?"</li>
                    <li>📷 "Find beautiful photos"</li>
                    <li>😄 "Tell me a joke"</li>
                </ul>
            </div>
            
            <!-- Sample Conversation -->
            <div class="message user-message">
                <strong>👤 You:</strong> Show me funny cat GIFs
            </div>
            
            <div class="message ai-message">
                <strong>🤖 AI Assistant:</strong> Great! I detected you want GIFs. Let me search for funny cat GIFs using the Giphy plugin.
                
                <div class="plugin-embed">
                    <div style="margin: 20px 0; padding: 20px; border: 2px solid #FF9800; border-radius: 12px; background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);">
                        <h4 style="margin-top: 0; color: #E65100;">🎭 GIFs for "cat GIFs"</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                            <div style="text-align: center;">
                                <div style="width: 150px; height: 100px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    [Funny Cat GIF 1]
                                </div>
                            </div>
                            <div style="text-align: center;">
                                <div style="width: 150px; height: 100px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    [Funny Cat GIF 2]
                                </div>
                            </div>
                            <div style="text-align: center;">
                                <div style="width: 150px; height: 100px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    [Funny Cat GIF 3]
                                </div>
                            </div>
                        </div>
                        <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">Powered by Giphy • 3 results found</p>
                    </div>
                </div>
            </div>
            
            <div class="message user-message">
                <strong>👤 You:</strong> Generate some relaxing music
            </div>
            
            <div class="message ai-message">
                <strong>🤖 AI Assistant:</strong> I'll generate relaxing music for you using the AI Music Generator plugin.
                
                <div class="plugin-embed">
                    <div style="margin: 20px 0; padding: 20px; border: 2px solid #4CAF50; border-radius: 12px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                        <h4 style="margin-top: 0; color: #2E7D32;">🎵 Generated Audio: relaxing music</h4>
                        <div style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="width: 40px; height: 40px; background: #4CAF50; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; cursor: pointer;" onclick="togglePlay(this)">▶</div>
                                <div style="flex: 1;">
                                    <div style="background: #e0e0e0; height: 4px; border-radius: 2px; position: relative;">
                                        <div style="background: #4CAF50; height: 4px; width: 30%; border-radius: 2px;"></div>
                                    </div>
                                    <div style="font-size: 0.9em; color: #666; margin-top: 5px;">0:45 / 2:30</div>
                                </div>
                            </div>
                        </div>
                        <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">Generated using AI Music Generator</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="sidebar">
            <h3>🔌 Plugin Status</h3>
            <div class="plugin-status">
                <div class="plugin-item">
                    <span>GIF Search</span>
                    <div class="status-dot"></div>
                </div>
                <div class="plugin-item">
                    <span>Music Generator</span>
                    <div class="status-dot"></div>
                </div>
                <div class="plugin-item">
                    <span>Weather</span>
                    <div class="status-dot"></div>
                </div>
                <div class="plugin-item">
                    <span>Photo Search</span>
                    <div class="status-dot"></div>
                </div>
                <div class="plugin-item">
                    <span>Joke Generator</span>
                    <div class="status-dot"></div>
                </div>
            </div>
            
            <h3>⚡ Quick Actions</h3>
            <div class="quick-actions">
                <button class="quick-button" onclick="sendMessage('Show me funny GIFs')">🎭 Get GIFs</button>
                <button class="quick-button" onclick="sendMessage('Generate music')">🎵 Create Music</button>
                <button class="quick-button" onclick="sendMessage('Weather in London')">🌤️ Check Weather</button>
                <button class="quick-button" onclick="sendMessage('Find sunset photos')">📷 Search Photos</button>
                <button class="quick-button" onclick="sendMessage('Tell me a joke')">😄 Random Joke</button>
            </div>
            
            <div style="margin-top: 30px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                <h4 style="margin-top: 0; color: #2e7d32;">📊 Live Stats</h4>
                <div style="font-size: 0.9em; color: #666;">
                    <div>Detection Accuracy: 100%</div>
                    <div>Response Time: &lt;1ms</div>
                    <div>Active Plugins: 5</div>
                    <div>Status: ✅ Operational</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="chat-input">
        <input type="text" id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
        <button class="send-button" onclick="sendMessage()">Send</button>
    </div>
    
    <script>
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function sendMessage(predefinedMessage) {
            const input = document.getElementById('messageInput');
            const message = predefinedMessage || input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            
            // Clear input
            input.value = '';
            
            // Simulate AI response
            setTimeout(() => {
                handleAIResponse(message);
            }, 500);
        }
        
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            if (sender === 'user') {
                messageDiv.innerHTML = `<strong>👤 You:</strong> ${text}`;
            } else {
                messageDiv.innerHTML = `<strong>🤖 AI Assistant:</strong> ${text}`;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function handleAIResponse(userMessage) {
            const message = userMessage.toLowerCase();
            
            if (message.includes('gif')) {
                addMessage('I found some great GIFs for you! 🎭', 'ai');
                addPluginEmbed('gif');
            } else if (message.includes('music')) {
                addMessage('I\'ve generated some music for you! 🎵', 'ai');
                addPluginEmbed('music');
            } else if (message.includes('weather')) {
                addMessage('Here\'s the current weather information! 🌤️', 'ai');
                addPluginEmbed('weather');
            } else if (message.includes('photo')) {
                addMessage('I found some beautiful photos! 📷', 'ai');
                addPluginEmbed('photo');
            } else if (message.includes('joke')) {
                addMessage('Here\'s a joke for you! 😄', 'ai');
                addPluginEmbed('joke');
            } else {
                addMessage('I can help you with GIFs, music, weather, photos, and jokes. What would you like to try?', 'ai');
            }
        }
        
        function addPluginEmbed(type) {
            const messagesContainer = document.getElementById('chatMessages');
            const embedDiv = document.createElement('div');
            embedDiv.className = 'plugin-embed';
            
            let embedHTML = '';
            
            switch(type) {
                case 'gif':
                    embedHTML = `
                        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FF9800; border-radius: 12px; background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);">
                            <h4 style="margin-top: 0; color: #E65100;">🎭 GIFs Found!</h4>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <div style="background: #f0f0f0; height: 80px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">[GIF 1]</div>
                                <div style="background: #f0f0f0; height: 80px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">[GIF 2]</div>
                                <div style="background: #f0f0f0; height: 80px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">[GIF 3]</div>
                            </div>
                        </div>
                    `;
                    break;
                case 'music':
                    embedHTML = `
                        <div style="margin: 20px 0; padding: 20px; border: 2px solid #4CAF50; border-radius: 12px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                            <h4 style="margin-top: 0; color: #2E7D32;">🎵 Music Generated!</h4>
                            <div style="background: white; padding: 15px; border-radius: 8px;">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <div style="width: 40px; height: 40px; background: #4CAF50; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; cursor: pointer;">▶</div>
                                    <div style="flex: 1;">
                                        <div style="background: #e0e0e0; height: 4px; border-radius: 2px;">
                                            <div style="background: #4CAF50; height: 4px; width: 20%; border-radius: 2px;"></div>
                                        </div>
                                        <div style="font-size: 0.9em; color: #666; margin-top: 5px;">0:30 / 2:30</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'weather':
                    embedHTML = `
                        <div style="margin: 20px 0; padding: 20px; border: 2px solid #03A9F4; border-radius: 12px; background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);">
                            <h4 style="margin-top: 0; color: #0277BD;">🌤️ Weather Information</h4>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="font-size: 3em;">☁️</div>
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold;">22°C</div>
                                    <div>Partly Cloudy</div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'photo':
                    embedHTML = `
                        <div style="margin: 20px 0; padding: 20px; border: 2px solid #2196F3; border-radius: 12px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
                            <h4 style="margin-top: 0; color: #0D47A1;">📷 Photos Found!</h4>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <div style="background: #f0f0f0; height: 80px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">[Photo 1]</div>
                                <div style="background: #f0f0f0; height: 80px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">[Photo 2]</div>
                                <div style="background: #f0f0f0; height: 80px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">[Photo 3]</div>
                            </div>
                        </div>
                    `;
                    break;
                case 'joke':
                    embedHTML = `
                        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FFC107; border-radius: 12px; background: linear-gradient(135deg, #fffde7 0%, #fff9c4 100%);">
                            <h4 style="margin-top: 0; color: #F57F17;">😄 Random Joke</h4>
                            <div style="background: white; padding: 15px; border-radius: 8px;">
                                <p style="margin: 0 0 10px 0;">Why don't scientists trust atoms?</p>
                                <p style="margin: 0; font-weight: bold;">Because they make up everything!</p>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            embedDiv.innerHTML = embedHTML;
            messagesContainer.appendChild(embedDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function togglePlay(button) {
            if (button.innerHTML === '▶') {
                button.innerHTML = '⏸';
            } else {
                button.innerHTML = '▶';
            }
        }
    </script>
</body>
</html>
