#!/usr/bin/env python3
"""
Simple API test for configured keys.
"""

import os
import requests
import json

def load_env():
    """Load .env file."""
    if os.path.exists('.env'):
        with open('.env') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip()

def test_giphy():
    """Test Giphy API."""
    api_key = os.getenv('GIPHY_API_KEY')
    if not api_key:
        return False, "No API key"
    
    try:
        response = requests.get(
            'https://api.giphy.com/v1/gifs/search',
            params={'api_key': api_key, 'q': 'success', 'limit': 1},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('data'):
                return True, f"Found {len(data['data'])} GIFs"
            else:
                return False, "No data returned"
        else:
            return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def test_weather():
    """Test OpenWeather API."""
    api_key = os.getenv('OPENWEATHER_API_KEY')
    if not api_key:
        return False, "No API key"
    
    try:
        response = requests.get(
            'https://api.openweathermap.org/data/2.5/weather',
            params={'appid': api_key, 'q': 'London', 'units': 'metric'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            temp = data['main']['temp']
            return True, f"London: {temp}°C"
        else:
            return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def test_groq():
    """Test Groq API."""
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        return False, "No API key"
    
    try:
        response = requests.post(
            'https://api.groq.com/openai/v1/chat/completions',
            headers={
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            json={
                'messages': [{'role': 'user', 'content': 'Say hello'}],
                'model': 'mixtral-8x7b-32768',
                'max_tokens': 10
            },
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data['choices'][0]['message']['content']
            return True, f"AI: {message.strip()}"
        else:
            return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def main():
    """Main test function."""
    print("🧪 Simple API Test")
    print("=" * 30)
    
    # Load environment
    load_env()
    
    # Check configured keys
    keys = ['GROQ_API_KEY', 'GIPHY_API_KEY', 'OPENWEATHER_API_KEY', 'HUGGINGFACE_API_KEY', 'UNSPLASH_API_KEY']
    configured = []
    
    for key in keys:
        if os.getenv(key):
            configured.append(key)
    
    print(f"📋 Configured: {len(configured)}/{len(keys)} APIs")
    for key in configured:
        value = os.getenv(key)
        print(f"   ✅ {key}: {value[:15]}...")
    
    # Test APIs
    tests = [
        ("🎭 Giphy", test_giphy),
        ("🌤️ Weather", test_weather),
        ("🤖 Groq AI", test_groq)
    ]
    
    working = 0
    
    for name, test_func in tests:
        print(f"\n{name}:")
        success, message = test_func()
        if success:
            print(f"   ✅ {message}")
            working += 1
        else:
            print(f"   ❌ {message}")
    
    print(f"\n📊 Results: {working}/{len(tests)} APIs working")
    
    if working >= 2:
        print("🎉 EXCELLENT! Multiple APIs working!")
        print("🚀 System ready for real functionality!")
    elif working >= 1:
        print("👍 GOOD! At least one API working!")
    else:
        print("⚠️ No APIs working - check configuration")
    
    return working > 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🔥 REAL API INTEGRATION CONFIRMED!")
        else:
            print("\n💡 Check API keys and try again")
    except Exception as e:
        print(f"\n❌ Error: {e}")
