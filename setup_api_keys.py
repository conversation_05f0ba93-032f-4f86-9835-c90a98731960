#!/usr/bin/env python3
"""
Interactive API key setup for AI Chat System.
"""

import os
import sys
import re
from pathlib import Path

class APIKeySetup:
    """Interactive API key setup."""
    
    def __init__(self):
        self.env_file = Path('.env')
        self.api_services = {
            'GROQ_API_KEY': {
                'name': 'Groq AI',
                'description': 'AI chat responses and code generation',
                'url': 'https://console.groq.com/keys',
                'format': 'gsk_...',
                'free': True,
                'required': True
            },
            'GIPHY_API_KEY': {
                'name': 'Giphy',
                'description': 'GIF search and animated images',
                'url': 'https://developers.giphy.com/',
                'format': 'alphanumeric string',
                'free': True,
                'required': False
            },
            'OPENWEATHER_API_KEY': {
                'name': 'OpenWeather',
                'description': 'Weather information and forecasts',
                'url': 'https://openweathermap.org/api',
                'format': 'alphanumeric string',
                'free': True,
                'required': False
            },
            'HUGGINGFACE_API_KEY': {
                'name': 'Hugging Face',
                'description': 'AI models and audio generation',
                'url': 'https://huggingface.co/settings/tokens',
                'format': 'hf_...',
                'free': True,
                'required': False
            },
            'UNSPLASH_API_KEY': {
                'name': 'Unsplash',
                'description': 'High-quality photos and images',
                'url': 'https://unsplash.com/developers',
                'format': 'alphanumeric string',
                'free': True,
                'required': False
            }
        }
    
    def display_welcome(self):
        """Display welcome message."""
        print("🔑 AI Chat System - API Key Setup")
        print("=" * 50)
        print("This script will help you configure API keys for external services.")
        print("All services offer FREE tiers - no payment required!")
        print()
    
    def display_service_info(self, key, info):
        """Display information about an API service."""
        print(f"🌐 {info['name']}")
        print(f"   Purpose: {info['description']}")
        print(f"   Get key: {info['url']}")
        print(f"   Format: {info['format']}")
        print(f"   Free tier: {'✅ Yes' if info['free'] else '❌ No'}")
        print(f"   Required: {'✅ Yes' if info['required'] else '⚠️ Optional'}")
        print()
    
    def validate_api_key(self, key_name, api_key):
        """Validate API key format."""
        if not api_key or api_key.strip() == '':
            return False, "API key cannot be empty"
        
        api_key = api_key.strip()
        
        if key_name == 'GROQ_API_KEY':
            if not api_key.startswith('gsk_'):
                return False, "Groq API key should start with 'gsk_'"
            if len(api_key) < 20:
                return False, "Groq API key appears too short"
        
        elif key_name == 'HUGGINGFACE_API_KEY':
            if not api_key.startswith('hf_'):
                return False, "Hugging Face token should start with 'hf_'"
            if len(api_key) < 20:
                return False, "Hugging Face token appears too short"
        
        elif len(api_key) < 10:
            return False, "API key appears too short"
        
        return True, "Valid format"
    
    def load_existing_keys(self):
        """Load existing API keys from .env file."""
        existing_keys = {}
        
        if self.env_file.exists():
            try:
                with open(self.env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if '=' in line and not line.startswith('#'):
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip().strip('"\'')
                            if value and not value.endswith('_here'):
                                existing_keys[key] = value
            except Exception as e:
                print(f"⚠️ Error reading .env file: {e}")
        
        return existing_keys
    
    def save_api_keys(self, api_keys):
        """Save API keys to .env file."""
        try:
            with open(self.env_file, 'w') as f:
                f.write("# AI Chat System - API Keys Configuration\n")
                f.write("# Generated by setup_api_keys.py\n\n")
                
                for key_name, info in self.api_services.items():
                    f.write(f"# {info['name']} - {info['description']}\n")
                    f.write(f"# Get key at: {info['url']}\n")
                    
                    if key_name in api_keys and api_keys[key_name]:
                        f.write(f"{key_name}={api_keys[key_name]}\n")
                    else:
                        f.write(f"# {key_name}=your_api_key_here\n")
                    f.write("\n")
            
            print(f"✅ API keys saved to {self.env_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving API keys: {e}")
            return False
    
    def test_api_key(self, key_name, api_key):
        """Test API key by making a simple request."""
        print(f"🧪 Testing {key_name}...")
        
        try:
            if key_name == 'GIPHY_API_KEY':
                import requests
                response = requests.get(
                    'https://api.giphy.com/v1/gifs/search',
                    params={'api_key': api_key, 'q': 'test', 'limit': 1},
                    timeout=10
                )
                if response.status_code == 200:
                    print("✅ Giphy API key working!")
                    return True
                else:
                    print(f"❌ Giphy API error: {response.status_code}")
                    return False
            
            elif key_name == 'OPENWEATHER_API_KEY':
                import requests
                response = requests.get(
                    'https://api.openweathermap.org/data/2.5/weather',
                    params={'appid': api_key, 'q': 'London'},
                    timeout=10
                )
                if response.status_code == 200:
                    print("✅ OpenWeather API key working!")
                    return True
                else:
                    print(f"❌ OpenWeather API error: {response.status_code}")
                    return False
            
            else:
                print("⚠️ Cannot test this API key automatically")
                return True
                
        except ImportError:
            print("⚠️ requests library not available for testing")
            return True
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
    
    def interactive_setup(self):
        """Run interactive API key setup."""
        self.display_welcome()
        
        # Load existing keys
        existing_keys = self.load_existing_keys()
        if existing_keys:
            print(f"📋 Found {len(existing_keys)} existing API keys")
            for key in existing_keys:
                print(f"   ✅ {key}")
            print()
        
        api_keys = existing_keys.copy()
        
        # Setup each service
        for key_name, info in self.api_services.items():
            self.display_service_info(key_name, info)
            
            # Check if key already exists
            if key_name in existing_keys:
                update = input(f"🔄 {key_name} already configured. Update? [y/N]: ").strip().lower()
                if update not in ['y', 'yes']:
                    continue
            
            # Get API key from user
            while True:
                if info['required']:
                    prompt = f"🔑 Enter {info['name']} API key (REQUIRED): "
                else:
                    prompt = f"🔑 Enter {info['name']} API key (optional, press Enter to skip): "
                
                api_key = input(prompt).strip()
                
                # Skip if optional and empty
                if not api_key and not info['required']:
                    print(f"⏭️ Skipping {info['name']}")
                    break
                
                # Validate key
                is_valid, message = self.validate_api_key(key_name, api_key)
                
                if is_valid:
                    # Test key if possible
                    test_key = input(f"🧪 Test {info['name']} API key? [Y/n]: ").strip().lower()
                    if test_key not in ['n', 'no']:
                        if self.test_api_key(key_name, api_key):
                            api_keys[key_name] = api_key
                            print(f"✅ {info['name']} API key configured!")
                            break
                        else:
                            retry = input("❌ Test failed. Use anyway? [y/N]: ").strip().lower()
                            if retry in ['y', 'yes']:
                                api_keys[key_name] = api_key
                                break
                    else:
                        api_keys[key_name] = api_key
                        print(f"✅ {info['name']} API key configured (not tested)")
                        break
                else:
                    print(f"❌ {message}")
                    if not info['required']:
                        skip = input("Skip this service? [y/N]: ").strip().lower()
                        if skip in ['y', 'yes']:
                            break
            
            print()
        
        # Save configuration
        if self.save_api_keys(api_keys):
            print("🎉 API key setup completed!")
            
            # Show summary
            configured_keys = [k for k, v in api_keys.items() if v]
            print(f"\n📊 Summary:")
            print(f"   Configured: {len(configured_keys)} services")
            print(f"   Available: {len(self.api_services)} total services")
            
            if configured_keys:
                print(f"\n✅ Configured services:")
                for key in configured_keys:
                    service_name = self.api_services[key]['name']
                    print(f"   • {service_name}")
            
            print(f"\n🚀 Next steps:")
            print(f"   1. Run: python3 test_real_apis.py")
            print(f"   2. Run: python3 scripts/run_plugin_manager.py chat")
            print(f"   3. Run: streamlit run src/ui/plugin_interface.py")
        
        else:
            print("❌ Setup failed!")
    
    def quick_setup_demo_keys(self):
        """Quick setup with demo/test keys for immediate testing."""
        print("🚀 Quick Demo Setup")
        print("=" * 30)
        print("Setting up with demo keys for immediate testing...")
        
        demo_keys = {
            'GIPHY_API_KEY': 'demo_api_key',  # Giphy provides this for testing
            # Other services need real keys
        }
        
        if self.save_api_keys(demo_keys):
            print("✅ Demo configuration saved!")
            print("🎭 Note: Only Giphy will work with demo key")
            print("🔑 Add real API keys for full functionality")


def main():
    """Main setup function."""
    setup = APIKeySetup()
    
    print("🔑 API Key Setup Options:")
    print("1. 🎯 Full interactive setup")
    print("2. 🚀 Quick demo setup")
    print("3. 📋 Show service information")
    print("4. ❌ Exit")
    
    while True:
        choice = input("\nSelect option [1-4]: ").strip()
        
        if choice == '1':
            setup.interactive_setup()
            break
        elif choice == '2':
            setup.quick_setup_demo_keys()
            break
        elif choice == '3':
            for key, info in setup.api_services.items():
                setup.display_service_info(key, info)
        elif choice == '4':
            print("👋 Setup cancelled")
            break
        else:
            print("❌ Invalid choice. Please select 1-4.")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup interrupted")
    except Exception as e:
        print(f"\n❌ Setup error: {e}")
        import traceback
        traceback.print_exc()
