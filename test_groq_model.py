#!/usr/bin/env python3
"""
Test Groq models with your API key.
"""

import os
import requests
import json

def load_env():
    """Load .env file."""
    if os.path.exists('.env'):
        with open('.env') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip()

def test_groq_model(api_key, model_name):
    """Test a specific Groq model."""
    try:
        response = requests.post(
            'https://api.groq.com/openai/v1/chat/completions',
            headers={
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            },
            json={
                'messages': [
                    {'role': 'user', 'content': f'Say "Hello from {model_name}" in exactly 5 words'}
                ],
                'model': model_name,
                'max_tokens': 20,
                'temperature': 0.1
            },
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            message = data['choices'][0]['message']['content'].strip()
            usage = data.get('usage', {})
            return True, message, usage.get('total_tokens', 0)
        else:
            return False, f"HTTP {response.status_code}", 0
            
    except Exception as e:
        return False, str(e), 0

def main():
    """Main test function."""
    print("🤖 Groq Model Test")
    print("=" * 30)
    
    # Load environment
    load_env()
    
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        print("❌ No Groq API key found in .env")
        return
    
    print(f"🔑 API Key: {api_key[:20]}...")
    
    # Test models in order of preference
    models_to_test = [
        ("llama-3.3-70b-versatile", "Latest & Most Capable"),
        ("llama-3.1-8b-instant", "Fast & Efficient"),
        ("llama3-70b-8192", "Stable & Reliable"),
        ("gemma2-9b-it", "Lightweight"),
        ("mixtral-8x7b-32768", "Legacy (might be deprecated)")
    ]
    
    working_models = []
    
    for model, description in models_to_test:
        print(f"\n🧪 Testing {model}")
        print(f"   📝 {description}")
        
        success, message, tokens = test_groq_model(api_key, model)
        
        if success:
            print(f"   ✅ Working! Response: {message}")
            print(f"   📊 Tokens: {tokens}")
            working_models.append(model)
        else:
            print(f"   ❌ Failed: {message}")
    
    # Summary
    print(f"\n📊 Test Results")
    print("=" * 20)
    print(f"Working models: {len(working_models)}/{len(models_to_test)}")
    
    if working_models:
        print(f"\n✅ Available Models:")
        for model in working_models:
            print(f"   • {model}")
        
        # Update .env with best working model
        best_model = working_models[0]  # First working model (highest priority)
        
        print(f"\n🎯 Recommended Model: {best_model}")
        
        # Update .env file
        update_env = input(f"\n🔧 Update .env to use {best_model}? [Y/n]: ").strip().lower()
        if update_env in ['', 'y', 'yes']:
            update_env_file(best_model)
            print(f"✅ Updated .env with model: {best_model}")
        
        print(f"\n🚀 Groq API is working with {len(working_models)} models!")
        
    else:
        print(f"\n❌ No models working. Check your API key.")

def update_env_file(model_name):
    """Update .env file with new model."""
    lines = []
    model_updated = False
    
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            lines = f.readlines()
    
    # Update existing GROQ_MODEL line or add new one
    for i, line in enumerate(lines):
        if line.startswith('GROQ_MODEL='):
            lines[i] = f'GROQ_MODEL={model_name}\n'
            model_updated = True
            break
    
    # Add new line if not found
    if not model_updated:
        lines.append(f'GROQ_MODEL={model_name}\n')
    
    # Write back to file
    with open('.env', 'w') as f:
        f.writelines(lines)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
