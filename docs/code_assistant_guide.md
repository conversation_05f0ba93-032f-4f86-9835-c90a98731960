# 🐍 AI Code Assistant - Complete Guide

Panduan lengkap untuk AI Code Assistant yang dapat men<PERSON><PERSON>lkan snippet kode Python dan melakukan auto-debug dengan analisis error log yang canggih.

## 📋 Overview

AI Code Assistant adalah sistem AI yang powerful untuk:

1. **🎯 Code Generation** - <PERSON><PERSON><PERSON><PERSON>an snippet kode Python berdasarkan deskripsi natural language
2. **🔍 Auto Debug** - Menganalisis error log dan memberikan saran perbaikan yang detail
3. **🧪 Code Testing** - Menjalankan kode secara aman dengan timeout protection
4. **📝 Smart Prompts** - Template prompt yang dapat disesuaikan untuk berbagai use case

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Test API keys
python scripts/test_api_keys.py
```

### Basic Usage

```python
from core.code_assistant import CodeAssistant

assistant = CodeAssistant()

# Generate code
snippet = await assistant.generate_code_snippet(
    description="Create a function that calculates fibonacci numbers",
    category="general",
    complexity="medium"
)

print(snippet.code)
```

### Run Interface

```bash
# Streamlit UI
python scripts/run_code_assistant.py ui

# Interactive CLI
python scripts/run_code_assistant.py generate
python scripts/run_code_assistant.py debug
```

## 🎯 Code Generation Features

### Supported Categories

| Category | Description | Use Cases |
|----------|-------------|-----------|
| **general** | General purpose code | Algorithms, utilities, basic functions |
| **web** | Web development | API clients, web scraping, HTTP requests |
| **data** | Data analysis | Pandas operations, data processing, visualization |
| **ml** | Machine learning | Model training, data preprocessing, evaluation |
| **automation** | Automation scripts | File processing, task automation, monitoring |
| **api** | API development | REST APIs, client libraries, authentication |

### Complexity Levels

- **Simple** - Basic functionality, minimal dependencies
- **Medium** - Proper error handling, configuration options
- **Complex** - Advanced features, optimization, comprehensive logging

### Example Usage

```python
# Web scraping code
snippet = await assistant.generate_code_snippet(
    description="Create a web scraper that extracts product prices from an e-commerce site",
    category="web",
    complexity="medium",
    include_tests=True
)

# Data analysis code
snippet = await assistant.generate_code_snippet(
    description="Analyze sales data and create visualizations showing trends",
    category="data",
    complexity="complex",
    include_tests=True
)

# API client code
snippet = await assistant.generate_code_snippet(
    description="Create a REST API client for weather service with authentication",
    category="api",
    complexity="medium",
    include_tests=False
)
```

## 🔍 Auto Debug Features

### Error Analysis Capabilities

1. **Pattern Recognition** - Mengenali error patterns yang umum
2. **Root Cause Analysis** - Mengidentifikasi penyebab fundamental
3. **Contextual Analysis** - Menganalisis kode context untuk pemahaman yang lebih baik
4. **AI-Powered Insights** - Menggunakan AI untuk analisis mendalam

### Supported Error Types

- **SyntaxError** - Syntax dan indentation issues
- **NameError** - Variable scope dan definition problems
- **TypeError** - Type mismatch dan conversion issues
- **ImportError** - Module dan dependency problems
- **AttributeError** - Object attribute issues
- **ValueError** - Invalid value problems
- **IndexError** - List/array access issues
- **KeyError** - Dictionary key problems

### Debug Analysis Example

```python
error_log = """
Traceback (most recent call last):
  File "script.py", line 10, in calculate_average
    return sum(data) / len(data)
NameError: name 'data' is not defined
"""

code_context = """
def calculate_average(numbers):
    return sum(data) / len(data)  # Error: should be 'numbers'

result = calculate_average([1, 2, 3, 4, 5])
"""

analysis = await assistant.analyze_error_log(error_log, code_context)

print(f"Error Type: {analysis.error_type}")
print(f"Severity: {analysis.severity}")
print(f"Confidence: {analysis.confidence:.0%}")

for fix in analysis.suggested_fixes:
    print(f"Fix: {fix}")
```

## 📝 Smart Prompt Templates

### Available Templates

1. **basic_function** - Generate Python functions
2. **class_generator** - Create Python classes
3. **api_client** - Build API client classes
4. **error_analysis** - Comprehensive error debugging
5. **performance_analysis** - Code optimization analysis

### Custom Template Example

```python
from core.prompt_templates import PromptTemplate, PromptType

custom_template = PromptTemplate(
    name="database_handler",
    type=PromptType.CODE_GENERATION,
    template="""
Create a database handler class for {database_type} with the following features:
- Connection management
- CRUD operations
- Error handling
- {additional_features}
""",
    variables=["database_type", "additional_features"],
    description="Template for database handler classes"
)

prompt_manager.add_custom_template(custom_template)
```

## 🧪 Code Testing & Validation

### Safe Code Execution

```python
# Test generated code safely
result = assistant.run_code_safely(
    code=snippet.code,
    timeout=10  # seconds
)

if result["success"]:
    print(f"✅ Executed in {result['execution_time']:.2f}s")
    print(f"Output: {result['output']}")
else:
    print(f"❌ Error: {result['error']}")
```

### Code Validation

```python
# Validate syntax and style
validation = assistant._validate_code(code)

if validation["is_valid"]:
    print("✅ Code is valid")
else:
    print("❌ Issues found:")
    for error in validation["errors"]:
        print(f"  - {error}")
```

## 🎨 Streamlit Interface

### Features

- **📝 Code Generator Tab**
  - Natural language input
  - Category and complexity selection
  - Quick templates
  - Real-time code generation
  - Code testing and validation

- **🔍 Debug Assistant Tab**
  - Error log input (paste/upload/run code)
  - AI-powered error analysis
  - Detailed fix suggestions
  - Code suggestions with corrections

- **🛠️ Sidebar Tools**
  - Provider selection
  - History management
  - Statistics dashboard
  - Export functionality

### Running the Interface

```bash
# Start Streamlit UI
streamlit run src/ui/code_assistant_interface.py

# Or use the script
python scripts/run_code_assistant.py ui
```

## 🔧 CLI Usage

### Interactive Mode

```bash
# Interactive code generation
python scripts/run_code_assistant.py generate

# Interactive debugging
python scripts/run_code_assistant.py debug
```

### Batch Processing

```bash
# Create descriptions file
echo "Create a function that sorts a list" > descriptions.txt
echo "Create a class for handling JSON data" >> descriptions.txt

# Batch generate code
python scripts/run_code_assistant.py batch -i descriptions.txt -o output/
```

### Template Management

```bash
# Show available templates
python scripts/run_code_assistant.py templates
```

## ⚙️ Configuration

### Provider Selection

```python
# Use specific AI provider
snippet = await assistant.generate_code_snippet(
    description="Your description",
    provider="groq"  # or "huggingface", "auto"
)
```

### Custom Settings

```python
# Customize generation parameters
snippet = await assistant.generate_code_snippet(
    description="Your description",
    category="web",
    complexity="complex",
    include_tests=True,
    provider="auto"
)
```

## 📊 Advanced Features

### Performance Analysis

```python
# Analyze code performance
from core.prompt_templates import prompt_manager

prompt = prompt_manager.render_template(
    "performance_analysis",
    code_to_analyze=your_code,
    data_size="large",
    performance_requirements="sub-second response"
)

analysis = await generate_response([{"role": "user", "content": prompt}])
```

### Code Review

```python
# Generate code review
review_prompt = f"""
Review this Python code for:
1. Code quality and style
2. Security issues
3. Performance concerns
4. Best practices

Code:
```python
{your_code}
```
"""

review = await generate_response([{"role": "user", "content": review_prompt}])
```

## 🧪 Testing

### Unit Tests

```bash
# Run tests
pytest tests/test_core/test_code_assistant.py -v

# Run with coverage
pytest tests/test_core/test_code_assistant.py --cov=src/core/code_assistant
```

### Integration Tests

```bash
# Test with real API
python examples/code_assistant_examples.py
```

## 🔍 Troubleshooting

### Common Issues

1. **API Key Issues**
   ```bash
   # Test API keys
   python scripts/test_api_keys.py
   ```

2. **Import Errors**
   ```bash
   # Check Python path
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
   ```

3. **Timeout Issues**
   ```python
   # Increase timeout for complex code
   result = assistant.run_code_safely(code, timeout=30)
   ```

4. **Memory Issues**
   ```python
   # Use streaming for large responses
   async for chunk in generate_streaming_response(messages):
       process_chunk(chunk)
   ```

## 📚 Examples

### Complete Workflow

```python
import asyncio
from core.code_assistant import CodeAssistant

async def complete_workflow():
    assistant = CodeAssistant()
    
    # 1. Generate code
    snippet = await assistant.generate_code_snippet(
        description="Create a function that processes CSV files and calculates statistics",
        category="data",
        complexity="medium",
        include_tests=True
    )
    
    print("Generated Code:")
    print(snippet.code)
    
    # 2. Test the code
    result = assistant.run_code_safely(snippet.code)
    
    if not result["success"]:
        # 3. Debug if there's an error
        analysis = await assistant.analyze_error_log(
            error_log=result["error"],
            code_context=snippet.code
        )
        
        print("Debug Analysis:")
        print(f"Error: {analysis.error_type}")
        for fix in analysis.suggested_fixes:
            print(f"Fix: {fix}")

asyncio.run(complete_workflow())
```

## 🚀 Best Practices

### Code Generation

1. **Be Specific** - Provide detailed descriptions
2. **Choose Right Category** - Select appropriate category for better results
3. **Include Context** - Mention expected input/output formats
4. **Test Generated Code** - Always test before using in production

### Debugging

1. **Provide Context** - Include relevant code context with error logs
2. **Use Full Traceback** - Provide complete error traceback
3. **Multiple Attempts** - Try different AI providers for complex issues
4. **Validate Fixes** - Test suggested fixes before implementing

### Performance

1. **Use Appropriate Complexity** - Don't over-engineer simple tasks
2. **Cache Results** - Cache frequently used snippets
3. **Batch Operations** - Use batch mode for multiple generations
4. **Monitor Usage** - Track API usage and costs

---

**🎯 AI Code Assistant dirancang untuk meningkatkan produktivitas developer dengan mengotomatisasi code generation dan debugging process menggunakan AI yang canggih.**
