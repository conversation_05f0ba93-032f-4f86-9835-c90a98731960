# 🔌 External Plugin Manager - Complete Guide

Panduan lengkap untuk External Plugin Manager yang dapat memanggil plugin eksternal (musik dari Hugging Face, GIF dari API publik, dll.) berdasarkan keyword user dan embed hasilnya di chat.

## 📋 Overview

External Plugin Manager ad<PERSON>h sistem AI yang powerful untuk:

1. **🎯 Automatic Detection** - Deteksi otomatis plugin yang dibutuhkan dari pesan user
2. **🔌 Multi-API Integration** - Integrasi dengan berbagai API eksternal (Giphy, Hugging Face, YouTube, dll.)
3. **🎨 Rich Embedding** - Embed hasil multimedia langsung di chat interface
4. **⚙️ Plugin Management** - Kelola dan konfigurasi plugin secara dinamis
5. **🛡️ Error Handling** - Robust error handling dan rate limiting

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt
pip install aiohttp  # For async HTTP requests

# Configure API keys (optional)
export HUGGINGFACE_API_KEY="your_key"
export GIPHY_API_KEY="your_key"
export OPENWEATHER_API_KEY="your_key"
```

### Basic Usage

```python
from core.plugin_manager import process_message_with_plugins

# Process message with automatic plugin detection
result = await process_message_with_plugins(
    message="Show me funny cat GIFs",
    provider="auto",
    max_results=5
)

if result["has_plugin_result"]:
    plugin_result = result["plugin_result"]
    print(plugin_result.embed_html)  # Rich HTML embed
```

### Run Interface

```bash
# Streamlit UI
streamlit run src/ui/plugin_interface.py

# Examples
python examples/plugin_manager_examples.py
```

## 🔌 Available Plugins

### Audio & Music

| Plugin | Description | API Key | Keywords |
|--------|-------------|---------|----------|
| **huggingface_audio** | Generate music using Hugging Face | Required | music, audio, sound, song, generate music |
| **freesound** | Search sound effects | Required | sound effect, sfx, audio clip, noise |

### Images & GIFs

| Plugin | Description | API Key | Keywords |
|--------|-------------|---------|----------|
| **giphy** | Search GIFs from Giphy | Optional | gif, animated, reaction, meme, funny |
| **tenor** | Search GIFs from Tenor | Required | gif, tenor, animated gif, reaction gif |
| **unsplash** | High-quality photos | Required | photo, image, picture, photography |

### Video & Entertainment

| Plugin | Description | API Key | Keywords |
|--------|-------------|---------|----------|
| **youtube_search** | Search YouTube videos | Required | video, youtube, watch, tutorial |
| **jokes** | Random jokes | Free | joke, funny, humor, laugh, comedy |
| **quotes** | Inspirational quotes | Free | quote, inspiration, motivational, wisdom |
| **cat_facts** | Random cat facts | Free | cat fact, cat, feline, pet fact |

### Data & Information

| Plugin | Description | API Key | Keywords |
|--------|-------------|---------|----------|
| **weather** | Weather information | Required | weather, temperature, forecast, climate |
| **news** | Latest news articles | Required | news, article, headlines, breaking news |

## 🎯 Automatic Plugin Detection

### Keyword-Based Detection

```python
# Direct keyword matches
"Show me funny GIFs" → giphy plugin
"Generate music" → huggingface_audio plugin
"What's the weather?" → weather plugin
```

### AI-Powered Intent Analysis

```python
from core.plugin_manager import ExternalPluginManager

manager = ExternalPluginManager()

# AI analyzes intent when keywords are ambiguous
intent = await manager.detect_plugin_intent(
    message="I want to see something funny",
    provider="groq"
)

if intent:
    print(f"Plugin: {intent['plugin_name']}")
    print(f"Confidence: {intent['confidence']}")
    print(f"Query: {intent['search_query']}")
```

## 🎨 Rich Content Embedding

### HTML Embeds

Setiap plugin menghasilkan HTML embed yang rich:

```python
# GIF Gallery
gif_result = await search_gifs("happy dance")
print(gif_result.embed_html)  # Interactive GIF gallery

# Weather Widget
weather_result = await get_weather("Tokyo")
print(weather_result.embed_html)  # Weather card with icons

# Audio Player
music_result = await generate_music("relaxing piano")
print(music_result.embed_html)  # HTML5 audio player
```

### Embed Examples

**GIF Embed:**
```html
<div style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);">
    <h3>🎭 GIFs for "funny cats"</h3>
    <img src="gif_url" style="border-radius: 8px;">
    <p>Powered by Giphy</p>
</div>
```

**Weather Embed:**
```html
<div style="background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);">
    <h3>🌤️ Weather in Tokyo, JP</h3>
    <div style="font-size: 2em;">25.3°C</div>
    <div>Partly Cloudy</div>
</div>
```

## 🔧 Plugin Configuration

### Plugin Structure

```python
@dataclass
class PluginConfig:
    name: str
    description: str
    keywords: List[str]
    api_endpoint: str
    api_key_required: bool = False
    rate_limit: int = 10  # requests per minute
    content_type: str = "json"
    enabled: bool = True
```

### Custom Plugin

```python
# Add custom plugin
custom_plugin = PluginConfig(
    name="custom_api",
    description="Custom API integration",
    keywords=["custom", "special"],
    api_endpoint="https://api.example.com/data",
    api_key_required=True,
    content_type="json"
)

manager.plugins["custom_api"] = custom_plugin
```

### Plugin Management

```python
# Enable/disable plugins
manager.enable_plugin("giphy")
manager.disable_plugin("youtube_search")

# Check status
status = manager.get_plugin_status()
print(status)  # {"giphy": True, "youtube_search": False, ...}

# Get available plugins
plugins = manager.get_available_plugins()
for plugin in plugins:
    print(f"{plugin['name']}: {plugin['enabled']}")
```

## 💬 Chat Integration

### Message Processing

```python
from core.plugin_manager import process_message_with_plugins

async def handle_chat_message(message: str):
    result = await process_message_with_plugins(
        message=message,
        provider="auto",
        max_results=5
    )
    
    if result["has_plugin_result"]:
        plugin_result = result["plugin_result"]
        
        # Create response with embedded content
        response = create_plugin_response(plugin_result, message)
        
        return {
            "text": response,
            "html_embed": plugin_result.embed_html,
            "content_type": plugin_result.content_type
        }
    else:
        # Regular chat response
        return {"text": "I can help you with that..."}
```

### Streamlit Integration

```python
import streamlit as st

# Process user input
user_message = st.text_input("Your message:")

if user_message:
    result = await process_message_with_plugins(user_message)
    
    if result["has_plugin_result"]:
        plugin_result = result["plugin_result"]
        
        # Display embedded content
        if plugin_result.embed_html:
            st.markdown(plugin_result.embed_html, unsafe_allow_html=True)
    else:
        st.write("Regular response...")
```

## 🎵 Specific Plugin Usage

### Music Generation

```python
from core.plugin_manager import generate_music

# Generate music
result = await generate_music("upbeat electronic dance music")

if result.success:
    print(f"Audio saved: {result.content_url}")
    print(f"Embed HTML: {result.embed_html}")
else:
    print(f"Error: {result.error_message}")
```

### GIF Search

```python
from core.plugin_manager import search_gifs

# Search GIFs
result = await search_gifs("happy dance", max_results=5)

if result.success:
    gifs = result.metadata["gifs"]
    for gif in gifs:
        print(f"GIF: {gif['url']} - {gif['title']}")
```

### Weather Information

```python
from core.plugin_manager import get_weather

# Get weather
result = await get_weather("London")

if result.success:
    weather_data = result.metadata
    temp = weather_data["main"]["temp"]
    description = weather_data["weather"][0]["description"]
    print(f"Temperature: {temp}°C, {description}")
```

### Photo Search

```python
from core.plugin_manager import search_photos

# Search photos
result = await search_photos("mountain sunset", max_results=3)

if result.success:
    photos = result.metadata["photos"]
    for photo in photos:
        print(f"Photo by {photo['photographer']}: {photo['url']}")
```

## 🛡️ Error Handling & Rate Limiting

### Rate Limiting

```python
# Automatic rate limiting per plugin
# Default: 10 requests per minute per plugin

# Check rate limit status
if manager._check_rate_limit("giphy"):
    # Execute plugin
    result = await manager.execute_plugin(intent)
else:
    print("Rate limit exceeded")
```

### Error Handling

```python
try:
    result = await process_message_with_plugins(message)
    
    if result["has_plugin_result"]:
        plugin_result = result["plugin_result"]
        
        if plugin_result.success:
            # Handle success
            print(plugin_result.embed_html)
        else:
            # Handle plugin error
            print(f"Plugin error: {plugin_result.error_message}")
    
except Exception as e:
    # Handle system error
    print(f"System error: {e}")
```

## 🔑 API Key Configuration

### Environment Variables

```bash
# Required for specific plugins
export HUGGINGFACE_API_KEY="hf_your_token"
export GIPHY_API_KEY="your_giphy_key"
export TENOR_API_KEY="your_tenor_key"
export UNSPLASH_API_KEY="your_unsplash_key"
export YOUTUBE_API_KEY="your_youtube_key"
export OPENWEATHER_API_KEY="your_weather_key"
export NEWS_API_KEY="your_news_key"
```

### Settings Configuration

```python
# In config/settings.py
class Settings:
    HUGGINGFACE_API_KEY: str = ""
    GIPHY_API_KEY: str = ""
    TENOR_API_KEY: str = ""
    # ... other API keys
```

## 🎨 Streamlit Interface

### Features

- **🔧 Plugin Overview** - Manage and configure all plugins
- **💬 Message Processor** - Test automatic plugin detection
- **🧪 Plugin Tester** - Test individual plugins
- **📊 Statistics** - Track plugin usage and results
- **⚙️ Settings** - Enable/disable plugins dynamically

### Running the Interface

```bash
# Start Streamlit interface
streamlit run src/ui/plugin_interface.py

# Access at http://localhost:8501
```

### Interface Sections

1. **Plugin Overview** - View all available plugins, their status, and keywords
2. **Message Processor** - Enter messages and see automatic plugin detection
3. **Plugin Tester** - Test individual plugins with custom inputs
4. **Sidebar Tools** - Plugin status, statistics, and help

## 🧪 Testing & Examples

### Unit Testing

```bash
# Run plugin tests
pytest tests/test_core/test_plugin_manager.py -v

# Test with coverage
pytest tests/test_core/test_plugin_manager.py --cov=src/core/plugin_manager
```

### Example Usage

```bash
# Run comprehensive examples
python examples/plugin_manager_examples.py

# Test specific functionality
python -c "
import asyncio
from core.plugin_manager import search_gifs
result = asyncio.run(search_gifs('funny cats'))
print(result.success)
"
```

## 🔧 Advanced Features

### Custom Embed Templates

```python
def create_custom_embed(data: Dict, title: str) -> str:
    return f"""
    <div style="custom-styling">
        <h3>{title}</h3>
        <div>{data}</div>
    </div>
    """

# Use in plugin implementation
class CustomPlugin:
    def _create_embed(self, data):
        return create_custom_embed(data, "Custom Content")
```

### Plugin Chaining

```python
# Chain multiple plugins
async def process_complex_request(message: str):
    # First, get weather
    weather_result = await get_weather("Tokyo")
    
    # Then, find related GIFs based on weather
    if weather_result.success:
        weather_desc = weather_result.metadata["weather"][0]["description"]
        gif_result = await search_gifs(f"{weather_desc} weather")
        
        return combine_results(weather_result, gif_result)
```

### Custom Response Formatting

```python
def create_multimedia_response(results: List[PluginResult]) -> str:
    response_parts = []
    
    for result in results:
        if result.content_type == "audio":
            response_parts.append("🎵 Audio content:")
        elif result.content_type == "gif":
            response_parts.append("🎭 GIF content:")
        
        if result.embed_html:
            response_parts.append(result.embed_html)
    
    return "\n\n".join(response_parts)
```

## 📚 Best Practices

### Plugin Development

1. **Clear Keywords** - Use specific, unambiguous keywords
2. **Error Handling** - Always handle API failures gracefully
3. **Rate Limiting** - Respect API rate limits
4. **Caching** - Cache results when appropriate
5. **Security** - Validate all inputs and outputs

### Performance Optimization

1. **Async Operations** - Use async/await for all API calls
2. **Connection Pooling** - Reuse HTTP connections
3. **Result Caching** - Cache frequently requested content
4. **Lazy Loading** - Load plugins only when needed

### User Experience

1. **Fast Response** - Provide immediate feedback
2. **Rich Content** - Use HTML embeds for better presentation
3. **Error Messages** - Provide helpful error messages
4. **Fallbacks** - Always have fallback options

---

**🔌 External Plugin Manager memungkinkan integrasi seamless dengan berbagai API eksternal, memberikan pengalaman chat yang rich dengan konten multimedia yang embedded secara otomatis berdasarkan intent user.**
