# 🏗️ Architecture Diagram Generator - Complete Guide

Panduan lengkap untuk mengh<PERSON>lkan diagram arsitektur proyek menggunakan AI berdasarkan deskripsi teks, dengan integrasi gambar dan Mermaid diagrams di chat.

## 📋 Overview

Architecture Diagram Generator adalah sistem AI yang powerful untuk:

1. **🎯 Text-to-Diagram** - <PERSON><PERSON><PERSON><PERSON><PERSON> diagram arsitektur dari deskripsi natural language
2. **🎨 Visual Generation** - Membuat gambar diagram dengan PIL dan AI image generation
3. **🔄 Mermaid Integration** - Generate interactive Mermaid diagrams
4. **💬 Chat Integration** - Otomatis detect dan generate diagram dalam percakapan
5. **🖼️ Image Embedding** - Sisipkan gambar dan diagram langsung di chat interface

## 🚀 Quick Start

### Installation

```bash
# Install dependencies
pip install -r requirements.txt
pip install Pillow  # For image generation

# Test API keys
python scripts/test_api_keys.py
```

### Basic Usage

```python
from core.diagram_generator import generate_architecture_diagram

# Generate diagram from description
response = await generate_architecture_diagram(
    description="Microservices e-commerce platform with React frontend, Node.js APIs, PostgreSQL database, Redis cache"
)

print(response)  # Formatted response with diagram and images
```

### Run Interface

```bash
# Streamlit UI
streamlit run src/ui/diagram_interface.py

# Examples
python examples/diagram_generator_examples.py
```

## 🎯 Core Features

### Diagram Types

| Type | Description | Use Cases |
|------|-------------|-----------|
| **system** | High-level system architecture | Application overview, service interactions |
| **network** | Network topology and connections | Infrastructure, security, connectivity |
| **deployment** | Infrastructure and deployment | DevOps, cloud architecture, containers |
| **data_flow** | Data movement and processing | ETL pipelines, analytics, data architecture |

### Visual Styles

- **modern** - Clean, contemporary design with gradients
- **minimal** - Simple, focused on essential elements
- **detailed** - Comprehensive with annotations and details

### Component Types

```python
component_types = {
    "frontend": "#FF6B6B",      # Web interfaces, mobile apps
    "backend": "#4ECDC4",       # Application servers, APIs
    "database": "#45B7D1",      # SQL, NoSQL, cache
    "api": "#96CEB4",           # REST, GraphQL, microservices
    "service": "#FFEAA7",       # Business logic, workers
    "cache": "#DDA0DD",         # Redis, Memcached
    "queue": "#98D8C8",         # Message queues, event streams
    "storage": "#F7DC6F",       # File storage, CDN
    "security": "#F1948A",      # Auth, firewall, encryption
    "monitoring": "#BB8FCE",    # Logging, metrics, alerts
    "network": "#85C1E9",       # Load balancer, proxy, CDN
    "external": "#D5DBDB"       # Third-party services, APIs
}
```

## 🎨 Diagram Generation

### Basic Generation

```python
from core.diagram_generator import DiagramGenerator

generator = DiagramGenerator()

# Generate complete diagram
diagram = await generator.generate_diagram_from_text(
    description="Your architecture description",
    diagram_type="system",
    style="modern",
    provider="auto"
)

print(f"Title: {diagram.title}")
print(f"Components: {len(diagram.components)}")
print(f"Image: {diagram.generated_image_path}")
print(f"Mermaid: {diagram.mermaid_code}")
```

### Advanced Generation with AI Images

```python
# Generate with AI image generation
result = await generator.generate_diagram_with_ai_image(
    description="Cloud-native microservices architecture",
    use_external_api=True,
    provider="groq"
)

# Access different outputs
diagram = result["diagram"]
ai_image = result["ai_generated_image"]
local_image = result["local_image_path"]
mermaid_code = result["mermaid_code"]
```

### Mermaid Diagram Generation

```python
from core.diagram_generator import create_mermaid_diagram

# Generate Mermaid code only
mermaid_code = await create_mermaid_diagram(
    description="API Gateway with microservices backend"
)

print(mermaid_code)
```

## 💬 Chat Integration

### Automatic Diagram Detection

```python
from core.chat_with_diagrams import process_chat_message_with_diagrams

# Process chat message
result = await process_chat_message_with_diagrams(
    message="Create an architecture for a social media platform",
    conversation_history=previous_messages,
    provider="auto"
)

if result["has_diagram"]:
    print("Diagram generated!")
    diagram_data = result["diagram_result"]
else:
    print("Regular chat response")

print(result["response"])
```

### Chat Assistant

```python
from core.chat_with_diagrams import DiagramChatAssistant

assistant = DiagramChatAssistant()

# Process message with context
result = await assistant.process_message(
    message="Design a microservices architecture for e-commerce",
    conversation_history=chat_history,
    provider="groq"
)

# Get formatted response with diagrams
formatted_response = result["response"]
```

### Trigger Keywords

Diagram generation is automatically triggered by:

**Direct Requests:**
- "create diagram", "generate diagram", "draw diagram"
- "show architecture", "design system", "create architecture"
- "visualize", "diagram of", "architecture for"

**Context-based:**
- Multiple architecture keywords: "microservices", "database", "api", "frontend"
- Combined with action words: "system", "design", "build", "create"

## 🖼️ Image Integration

### Display Images in Chat

```python
from core.diagram_generator import DiagramGenerator

generator = DiagramGenerator()

# Create HTML for image display
image_html = generator.create_image_html(
    image_path="path/to/diagram.png",
    title="System Architecture",
    description="Generated architecture diagram"
)

# Use in Streamlit
st.markdown(image_html, unsafe_allow_html=True)
```

### Mermaid Integration

```python
# Create Mermaid HTML
mermaid_html = generator.create_mermaid_html(
    mermaid_code=diagram.mermaid_code,
    title="Interactive Architecture Diagram"
)

# Display in web interface
st.markdown(mermaid_html, unsafe_allow_html=True)
```

### Base64 Image Embedding

```python
import base64

# Convert image to base64 for embedding
with open(image_path, 'rb') as f:
    image_data = base64.b64encode(f.read()).decode()

html = f'<img src="data:image/png;base64,{image_data}" style="max-width: 100%;">'
```

## 🎨 Streamlit Interface

### Features

- **📝 Text Input** - Natural language architecture descriptions
- **⚙️ Advanced Options** - Diagram type, style, provider selection
- **🚀 Quick Templates** - Pre-built architecture templates
- **📊 Multiple Outputs** - Visual diagrams, Mermaid code, component details
- **💾 Export Options** - Download images, copy Mermaid code
- **📈 History Management** - Track generated diagrams

### Running the Interface

```bash
# Start Streamlit interface
streamlit run src/ui/diagram_interface.py

# Access at http://localhost:8501
```

### Interface Sections

1. **Main Generator** - Primary diagram generation
2. **Visual Diagram Tab** - Generated images and AI images
3. **Mermaid Tab** - Interactive Mermaid diagrams
4. **Components Tab** - Detailed component information
5. **Details Tab** - Generation metadata and options

## 🔧 Customization

### Custom Component Colors

```python
# Customize component colors
generator = DiagramGenerator()
generator.component_colors["custom_type"] = "#FF5733"

# Use in component
component = DiagramComponent(
    name="Custom Service",
    type="custom_type",
    description="Custom service component"
)
```

### Custom Prompt Templates

```python
# Create custom diagram prompt
def create_custom_prompt(description, requirements):
    return f"""
    Create a specialized diagram for: {description}
    
    Requirements:
    {requirements}
    
    Focus on: security, scalability, performance
    """

# Use custom prompt
custom_response = await generate_response([{
    "role": "user", 
    "content": create_custom_prompt(description, requirements)
}])
```

### Image Generation Optimization

```python
# Optimize image generation prompt
def optimize_for_ai_generation(description):
    base_prompt = """
    Professional software architecture diagram, 
    clean modern style, technical illustration, 
    high quality, detailed components, clear connections
    """
    
    return f"{base_prompt}, {description}"
```

## 📊 Example Architectures

### 1. E-commerce Microservices

```python
description = """
E-commerce platform with:
- React frontend
- API Gateway (Kong)
- User service (Node.js)
- Product service (Python)
- Order service (Java)
- Payment service (Node.js)
- PostgreSQL databases
- Redis cache
- RabbitMQ messaging
- Docker + Kubernetes
"""

diagram = await generate_architecture_diagram(description)
```

### 2. Data Pipeline

```python
description = """
Real-time data pipeline:
- Data ingestion (Kafka)
- Stream processing (Spark)
- Data warehouse (Snowflake)
- Analytics dashboard (Tableau)
- Monitoring (Prometheus/Grafana)
- Data lake (S3)
- ML pipeline (MLflow)
"""

diagram = await generate_architecture_diagram(description, diagram_type="data_flow")
```

### 3. Cloud-Native Application

```python
description = """
Serverless cloud application:
- React frontend (Vercel)
- API Gateway
- Lambda functions
- DynamoDB
- S3 storage
- CloudFront CDN
- Cognito auth
- CloudWatch monitoring
"""

diagram = await generate_architecture_diagram(description, style="modern")
```

## 🔍 Advanced Features

### AI Image Generation Integration

```python
# Configure external AI image generation
async def generate_with_external_ai(description):
    # This would integrate with:
    # - DALL-E API
    # - Midjourney API  
    # - Stable Diffusion API
    
    optimized_prompt = create_image_prompt(description)
    
    # Call external API (placeholder)
    image_result = await external_ai_api.generate(optimized_prompt)
    
    return image_result
```

### Custom Layout Algorithms

```python
# Implement custom component positioning
def calculate_optimal_layout(components, connections):
    # Force-directed layout
    # Hierarchical layout
    # Grid-based layout
    
    for component in components:
        component.position = calculate_position(component, connections)
    
    return components
```

### Export Formats

```python
# Export to different formats
def export_diagram(diagram, format_type):
    if format_type == "svg":
        return generate_svg(diagram)
    elif format_type == "pdf":
        return generate_pdf(diagram)
    elif format_type == "json":
        return serialize_diagram(diagram)
    elif format_type == "plantuml":
        return generate_plantuml(diagram)
```

## 🧪 Testing

### Unit Tests

```bash
# Run diagram generator tests
pytest tests/test_core/test_diagram_generator.py -v

# Test with coverage
pytest tests/test_core/test_diagram_generator.py --cov=src/core/diagram_generator
```

### Integration Tests

```bash
# Test complete workflow
python examples/diagram_generator_examples.py

# Test chat integration
python -c "
import asyncio
from core.chat_with_diagrams import example_diagram_chat
asyncio.run(example_diagram_chat())
"
```

## 🔧 Troubleshooting

### Common Issues

1. **Image Generation Fails**
   ```python
   # Check PIL installation
   pip install Pillow
   
   # Verify font availability
   try:
       font = ImageFont.truetype("arial.ttf", 12)
   except:
       font = ImageFont.load_default()
   ```

2. **Mermaid Rendering Issues**
   ```html
   <!-- Ensure Mermaid.js is loaded -->
   <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
   <script>mermaid.initialize({startOnLoad: true});</script>
   ```

3. **AI Response Parsing**
   ```python
   # Handle malformed JSON responses
   try:
       data = json.loads(response)
   except json.JSONDecodeError:
       data = parse_structured_text(response)
   ```

## 📚 Best Practices

### Writing Good Descriptions

1. **Be Specific** - Mention exact technologies and frameworks
2. **Include Data Flow** - Describe how data moves through the system
3. **Mention Scale** - Indicate expected load and performance requirements
4. **Add Context** - Explain the business purpose and constraints

### Diagram Quality

1. **Component Naming** - Use clear, descriptive names
2. **Logical Grouping** - Group related components together
3. **Clear Connections** - Show data flow and dependencies
4. **Appropriate Detail** - Match detail level to audience

### Performance Optimization

1. **Cache Results** - Cache generated diagrams for reuse
2. **Optimize Images** - Use appropriate image sizes and formats
3. **Batch Processing** - Generate multiple diagrams efficiently
4. **Async Operations** - Use async/await for better performance

---

**🏗️ Architecture Diagram Generator memungkinkan Anda untuk dengan mudah menghasilkan diagram arsitektur profesional dari deskripsi teks menggunakan AI, dengan integrasi seamless ke dalam chat interface.**
