# 🤖 Generate Response Function - Complete Guide

Panduan lengkap untuk menggunakan fungsi `generate_response()` yang dapat memanggil Groq API atau Hugging Face sesuai parameter.

## 📋 Overview

Fungsi `generate_response()` adalah interface utama untuk men<PERSON><PERSON><PERSON><PERSON> respons AI dari history obrolan. Fungsi ini mendukung multiple AI providers dan dapat secara otomatis memilih provider terbaik yang tersedia.

## 🚀 Quick Start

### Basic Usage

```python
from core.response_generator import generate_response

# Simple question
messages = [{"role": "user", "content": "What is Python?"}]
response = await generate_response(messages)
print(response)
```

### With Conversation History

```python
# Multi-turn conversation
messages = [
    {"role": "user", "content": "What is machine learning?"},
    {"role": "assistant", "content": "Machine learning is a subset of AI..."},
    {"role": "user", "content": "Can you give me an example?"}
]

response = await generate_response(messages, provider="groq")
print(response)
```

## 📝 Function Signature

```python
async def generate_response(
    messages: List[Dict[str, str]],
    provider: str = "auto",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1024,
    **kwargs
) -> str
```

### Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `messages` | `List[Dict[str, str]]` | Required | List of message dictionaries |
| `provider` | `str` | `"auto"` | AI provider to use |
| `model` | `Optional[str]` | `None` | Specific model name |
| `temperature` | `float` | `0.7` | Response creativity (0.0-1.0) |
| `max_tokens` | `int` | `1024` | Maximum response length |
| `**kwargs` | `Any` | - | Additional provider-specific parameters |

### Message Format

Each message must be a dictionary with:
- `role`: `"user"`, `"assistant"`, or `"system"`
- `content`: The message text (non-empty string)

```python
# Valid message examples
{"role": "user", "content": "Hello!"}
{"role": "assistant", "content": "Hi there!"}
{"role": "system", "content": "You are a helpful assistant"}
```

## 🔧 Providers

### Available Providers

1. **`"auto"`** (default) - Automatically selects best available provider
2. **`"groq"`** - Use Groq API (fast inference)
3. **`"huggingface"`** - Use Hugging Face (diverse models)

### Provider Selection Logic

```python
# Auto selection priority:
# 1. Groq (if available) - preferred for speed
# 2. Hugging Face (if available) - fallback option
# 3. Error if none available

# Specific provider
response = await generate_response(messages, provider="groq")
response = await generate_response(messages, provider="huggingface")
```

## 💻 Usage Examples

### 1. Simple Question

```python
async def ask_simple_question():
    messages = [{"role": "user", "content": "What is artificial intelligence?"}]
    response = await generate_response(messages)
    print(f"AI: {response}")
```

### 2. Conversation with Context

```python
async def conversation_with_context():
    messages = [
        {"role": "system", "content": "You are a Python programming expert."},
        {"role": "user", "content": "How do I create a list in Python?"},
        {"role": "assistant", "content": "You can create a list using square brackets: my_list = [1, 2, 3]"},
        {"role": "user", "content": "How do I add items to it?"}
    ]
    
    response = await generate_response(messages, provider="groq")
    print(f"Expert: {response}")
```

### 3. Creative Writing

```python
async def creative_writing():
    messages = [
        {"role": "user", "content": "Write a short poem about coding"}
    ]
    
    # High creativity
    response = await generate_response(
        messages, 
        temperature=0.9,
        max_tokens=200
    )
    print(response)
```

### 4. Specific Provider Usage

```python
# Using specific providers
from core.response_generator import generate_groq_response, generate_huggingface_response

# Groq (fast)
groq_response = await generate_groq_response(messages)

# Hugging Face (diverse models)
hf_response = await generate_huggingface_response(messages)
```

### 5. Error Handling

```python
async def safe_generation():
    messages = [{"role": "user", "content": "Hello"}]
    
    try:
        response = await generate_response(messages)
        print(f"Success: {response}")
    except ValueError as e:
        print(f"Validation error: {e}")
    except Exception as e:
        print(f"Generation error: {e}")
```

## 🌊 Streaming Responses

For real-time response generation:

```python
from core.response_generator import generate_streaming_response

async def streaming_example():
    messages = [{"role": "user", "content": "Tell me a story"}]
    
    print("AI: ", end="", flush=True)
    async for chunk in generate_streaming_response(messages):
        print(chunk, end="", flush=True)
    print()  # New line at end
```

## 🛠️ Utility Functions

### Check Available Providers

```python
from core.response_generator import get_available_providers, validate_provider

# Get all available providers
providers = await get_available_providers()
print(f"Available: {providers}")

# Test specific provider
is_working = await validate_provider("groq")
print(f"Groq working: {is_working}")
```

### Simple Chat Interface

```python
from utils.simple_chat import SimpleChatBot

# Create chatbot
bot = SimpleChatBot(provider="groq")

# Chat
response1 = await bot.chat("Hello!")
response2 = await bot.chat("How are you?")

# Get conversation history
history = bot.get_conversation()
```

## ⚙️ Advanced Configuration

### Temperature Settings

```python
# Conservative (focused, deterministic)
response = await generate_response(messages, temperature=0.1)

# Balanced (default)
response = await generate_response(messages, temperature=0.7)

# Creative (diverse, random)
response = await generate_response(messages, temperature=0.9)
```

### Token Limits

```python
# Short response
response = await generate_response(messages, max_tokens=100)

# Long response
response = await generate_response(messages, max_tokens=2048)
```

### Provider-Specific Parameters

```python
# Groq-specific parameters
response = await generate_response(
    messages,
    provider="groq",
    temperature=0.8,
    max_tokens=1024,
    top_p=0.9,  # Additional parameter
    stop=["END"]  # Stop sequences
)
```

## 🔍 Message Validation

The function validates messages automatically:

```python
# ✅ Valid messages
valid_messages = [
    {"role": "user", "content": "Hello"},
    {"role": "assistant", "content": "Hi there!"},
    {"role": "system", "content": "You are helpful"}
]

# ❌ Invalid messages (will raise ValueError)
invalid_examples = [
    [],  # Empty list
    [{"role": "user"}],  # Missing content
    [{"role": "invalid", "content": "test"}],  # Invalid role
    [{"role": "user", "content": ""}],  # Empty content
    ["not a dict"]  # Wrong type
]
```

## 🚨 Error Handling

### Common Errors

1. **No API Keys Available**
   ```python
   # ValueError: No AI clients available. Check your API keys.
   ```

2. **Invalid Provider**
   ```python
   # ValueError: Provider 'invalid' not available
   ```

3. **Invalid Messages**
   ```python
   # ValueError: Messages must be a non-empty list
   ```

4. **API Errors**
   ```python
   # Exception: API request failed or network error
   ```

### Best Practices

```python
async def robust_generation(messages, max_retries=3):
    """Generate response with retry logic."""
    for attempt in range(max_retries):
        try:
            return await generate_response(messages)
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            print(f"Attempt {attempt + 1} failed: {e}")
            await asyncio.sleep(1)  # Wait before retry
```

## 📊 Performance Tips

### 1. Provider Selection
- Use `"groq"` for fastest responses
- Use `"huggingface"` for diverse model options
- Use `"auto"` for automatic fallback

### 2. Message Optimization
- Keep conversation history reasonable (< 50 messages)
- Use clear, specific prompts
- Include relevant context in system messages

### 3. Parameter Tuning
- Lower temperature for factual responses
- Higher temperature for creative content
- Adjust max_tokens based on expected response length

## 🧪 Testing

### Unit Tests

```python
import pytest
from core.response_generator import generate_response

@pytest.mark.asyncio
async def test_basic_generation():
    messages = [{"role": "user", "content": "Hello"}]
    response = await generate_response(messages)
    assert isinstance(response, str)
    assert len(response) > 0
```

### Integration Tests

```python
async def test_all_providers():
    messages = [{"role": "user", "content": "Test"}]
    
    providers = await get_available_providers()
    for provider in providers:
        response = await generate_response(messages, provider=provider)
        assert response is not None
```

## 📚 Complete Example

```python
import asyncio
from core.response_generator import generate_response

async def complete_example():
    """Complete example showing various features."""
    
    # 1. Simple question
    print("=== Simple Question ===")
    messages = [{"role": "user", "content": "What is Python?"}]
    response = await generate_response(messages)
    print(f"AI: {response[:100]}...")
    
    # 2. Conversation
    print("\n=== Conversation ===")
    conversation = [
        {"role": "system", "content": "You are a helpful coding assistant."},
        {"role": "user", "content": "How do I create a function in Python?"}
    ]
    
    response = await generate_response(conversation, provider="groq")
    conversation.append({"role": "assistant", "content": response})
    print(f"Assistant: {response[:100]}...")
    
    # 3. Follow-up
    conversation.append({"role": "user", "content": "Can you show an example?"})
    response = await generate_response(conversation)
    print(f"Assistant: {response[:100]}...")
    
    # 4. Creative writing
    print("\n=== Creative Writing ===")
    creative_prompt = [{"role": "user", "content": "Write a haiku about programming"}]
    poem = await generate_response(creative_prompt, temperature=0.8)
    print(f"Poem:\n{poem}")

if __name__ == "__main__":
    asyncio.run(complete_example())
```

## 🔗 Related Functions

- `generate_groq_response()` - Groq-specific generation
- `generate_huggingface_response()` - HF-specific generation
- `generate_streaming_response()` - Streaming responses
- `SimpleChatBot` - Easy chat interface
- `ask_ai()` - Simple question interface

---

**💡 Tip**: Start with `provider="auto"` and `temperature=0.7` for most use cases. Adjust parameters based on your specific needs.
