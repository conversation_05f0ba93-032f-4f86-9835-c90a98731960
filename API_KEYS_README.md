# 🔑 Secure API Key Management for Chat AI

Panduan lengkap untuk memuat dan mengamankan API key Groq dan Hugging Face dalam proyek Chat AI.

## 📋 Fitur Keamanan

### ✅ **Validasi API Key**
- Format validation untuk Groq (`gsk_`) dan Hugging Face (`hf_`)
- Panjang minimum dan karakter yang diizinkan
- Masking otomatis untuk logging yang aman

### ✅ **Enkripsi**
- Enkripsi API key menggunakan Fernet (cryptography)
- Penyimpanan aman dengan file permissions
- Decryption otomatis saat dibutuhkan

### ✅ **Environment Loading**
- Loading aman dari file `.env`
- Validasi format environment variables
- Fallback ke environment system

### ✅ **Client Factory**
- Inisialisasi otomatis semua AI clients
- Validasi koneksi real-time
- Error handling yang robust

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Copy template
cp .env.example .env

# Edit dengan API keys Anda
nano .env
```

### 2. Konfigurasi API Keys

```env
# .env file
GROQ_API_KEY=gsk_your_groq_api_key_here
HUGGINGFACE_API_KEY=hf_your_huggingface_token_here
```

### 3. Test API Keys

```bash
# Test semua API keys
python scripts/test_api_keys.py

# Test provider specific
python scripts/test_api_keys.py --provider groq
python scripts/test_api_keys.py --provider huggingface

# Benchmark performance
python scripts/test_api_keys.py --benchmark

# Test streaming
python scripts/test_api_keys.py --streaming
```

## 💻 Penggunaan dalam Kode

### Basic Usage

```python
from utils.client_factory import create_all_available_clients
from config.api_keys import api_key_manager

# Load API keys securely
api_keys = api_key_manager.load_api_keys_from_env()

# Create all clients
clients = await create_all_available_clients()

# Use clients
if "groq" in clients:
    response = await clients["groq"].generate_response([
        {"role": "user", "content": "Hello!"}
    ])
```

### Individual Client Creation

```python
from utils.client_factory import ClientFactory

factory = ClientFactory()

# Create Groq client
groq_client = factory.create_groq_client()
if groq_client:
    # Test connection
    is_valid = await groq_client.validate_connection()
    if is_valid:
        response = await groq_client.generate_response(conversation)

# Create Hugging Face client
hf_client = factory.create_huggingface_client()
```

### Secure Key Validation

```python
from config.api_keys import api_key_manager

# Validate Groq key
groq_validation = api_key_manager.validate_groq_api_key("gsk_...")
if groq_validation["is_valid"]:
    print(f"Valid key: {groq_validation['masked_key']}")

# Validate Hugging Face key
hf_validation = api_key_manager.validate_huggingface_api_key("hf_...")
```

### Environment Loading

```python
from utils.env_loader import SecureEnvLoader

loader = SecureEnvLoader()
env_vars = loader.load_environment()

# Get specific variables with defaults
app_name = loader.get_var("APP_NAME", "Chat AI")
debug = loader.get_bool("DEBUG", False)
port = loader.get_int("PORT", 8000)
```

### API Key Testing

```python
from utils.api_key_tester import test_all_api_keys

# Test all keys
results = await test_all_api_keys()

for provider, result in results.items():
    if result["success"]:
        print(f"{provider}: ✅ Working")
    else:
        print(f"{provider}: ❌ {result['error']}")
```

## 🔐 Fitur Keamanan Detail

### 1. **API Key Validation**

```python
# Groq API Key Validation
- Must start with 'gsk_'
- Minimum 20 characters
- Only alphanumeric, underscore, hyphen
- Automatic masking for logs

# Hugging Face API Key Validation  
- Must start with 'hf_'
- Minimum 20 characters
- Only alphanumeric after prefix
- Automatic masking for logs
```

### 2. **Encryption Storage**

```python
# Store encrypted key
api_key_manager.store_encrypted_key("my_key", "actual_api_key")

# Load encrypted key
decrypted_key = api_key_manager.load_encrypted_key("my_key")
```

### 3. **Secure Logging**

```python
# API keys are automatically masked in logs
# gsk_1234567890abcdef... becomes gsk_1234****cdef
# hf_abcdef1234567890... becomes hf_abcd****7890
```

## 🧪 Testing & Validation

### Comprehensive Testing

```bash
# Full test suite
python scripts/test_api_keys.py --verbose --benchmark --streaming --report
```

### Performance Benchmarking

```python
from utils.api_key_tester import APIKeyTester

tester = APIKeyTester()

# Benchmark response times
benchmark = await tester.benchmark_response_times(num_requests=5)

for provider, stats in benchmark.items():
    print(f"{provider}:")
    print(f"  Avg: {stats['avg_response_time']:.2f}s")
    print(f"  Success rate: {stats['success_rate']:.1f}%")
```

### Streaming Tests

```python
# Test streaming capabilities
streaming_results = await tester.test_streaming_capabilities()

for provider, result in streaming_results.items():
    if result["streaming_success"]:
        print(f"{provider}: {result['chunks_received']} chunks")
```

## 📁 File Structure

```
src/
├── config/
│   ├── api_keys.py          # Secure API key management
│   └── settings.py          # Enhanced settings with validation
├── utils/
│   ├── client_factory.py    # Client creation and management
│   ├── env_loader.py        # Secure environment loading
│   └── api_key_tester.py    # API key testing utilities
└── examples/
    └── api_key_usage.py     # Usage examples

scripts/
└── test_api_keys.py         # API key testing script
```

## 🛡️ Best Practices

### 1. **Never Hardcode API Keys**
```python
# ❌ DON'T DO THIS
api_key = "gsk_1234567890abcdef..."

# ✅ DO THIS
api_key = os.getenv("GROQ_API_KEY")
```

### 2. **Always Validate Before Use**
```python
# ✅ Validate first
validation = api_key_manager.validate_groq_api_key(api_key)
if validation["is_valid"]:
    client = GroqClient(api_key)
```

### 3. **Use Masked Logging**
```python
# ✅ Safe logging
app_logger.info(f"Using key: {validation['masked_key']}")
```

### 4. **Test Connections**
```python
# ✅ Always test
client = factory.create_groq_client()
if client and await client.validate_connection():
    # Use client
```

## 🚨 Troubleshooting

### Common Issues

1. **"No valid API keys found"**
   ```bash
   # Check .env file exists
   ls -la .env
   
   # Validate format
   python scripts/test_api_keys.py
   ```

2. **"Invalid API key format"**
   ```bash
   # Groq keys must start with 'gsk_'
   # HF keys must start with 'hf_'
   ```

3. **"Connection validation failed"**
   ```bash
   # Test network connectivity
   python scripts/test_api_keys.py --verbose
   ```

### Debug Mode

```python
# Enable debug logging
import os
os.environ["DEBUG"] = "True"
os.environ["LOG_LEVEL"] = "DEBUG"
```

## 📚 Examples

Lihat file `examples/api_key_usage.py` untuk contoh lengkap:

```bash
# Run examples
python examples/api_key_usage.py
```

## 🔗 API Key Sources

### Groq API
- **Website**: https://console.groq.com/
- **Format**: `gsk_xxxxxxxxxxxxxxxxxx`
- **Free Tier**: Available

### Hugging Face
- **Website**: https://huggingface.co/settings/tokens
- **Format**: `hf_xxxxxxxxxxxxxxxxxx`
- **Free Tier**: Available

## 📞 Support

Jika mengalami masalah:

1. Jalankan diagnostic: `python scripts/test_api_keys.py --verbose`
2. Check logs di `data/logs/app.log`
3. Pastikan API keys valid dan aktif
4. Test koneksi internet

---

**🔒 Keamanan adalah prioritas utama. Semua API keys dienkripsi dan di-mask dalam logs untuk melindungi kredensial Anda.**
