"""Hugging Face API client implementation."""

from typing import List, Dict, AsyncGenerator
import asyncio
import httpx
from transformers import <PERSON><PERSON>oken<PERSON>, AutoModelForCausalLM, pipeline
import torch

from config.logging_config import app_logger
from config.settings import get_settings
from .base_client import BaseAIClient

settings = get_settings()


class HuggingFaceClient(BaseAIClient):
    """Hugging Face API and local model client."""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, settings.huggingface_model)
        self.api_url = "https://api-inference.huggingface.co/models"
        self.local_model = None
        self.tokenizer = None
        self.use_local = False
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the Hugging Face client."""
        try:
            # Try to load local model first
            if self._load_local_model():
                self.use_local = True
                app_logger.info(f"Using local Hugging Face model: {self.model_name}")
            else:
                app_logger.info(f"Using Hugging Face API for model: {self.model_name}")
        except Exception as e:
            app_logger.error(f"Error initializing Hugging Face client: {e}")
    
    def _load_local_model(self) -> bool:
        """Try to load model locally."""
        try:
            # Check if CUDA is available
            device = "cuda" if torch.cuda.is_available() else "cpu"
            app_logger.info(f"Using device: {device}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model
            self.local_model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                device_map="auto" if device == "cuda" else None,
                low_cpu_mem_usage=True
            )
            
            if device == "cpu":
                self.local_model = self.local_model.to(device)
            
            return True
            
        except Exception as e:
            app_logger.warning(f"Could not load local model: {e}")
            return False
    
    async def generate_response(self, conversation: List[Dict[str, str]]) -> str:
        """Generate a response using Hugging Face."""
        try:
            if self.use_local:
                return await self._generate_local_response(conversation)
            else:
                return await self._generate_api_response(conversation)
        except Exception as e:
            app_logger.error(f"Hugging Face generation error: {e}")
            raise
    
    async def _generate_local_response(self, conversation: List[Dict[str, str]]) -> str:
        """Generate response using local model."""
        try:
            # Format conversation for the model
            prompt = self._format_conversation_for_model(conversation)
            
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._sync_generate_local,
                prompt
            )
            return response
        except Exception as e:
            app_logger.error(f"Local model generation error: {e}")
            raise
    
    def _sync_generate_local(self, prompt: str) -> str:
        """Synchronous local model generation."""
        try:
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors="pt")
            
            # Move to same device as model
            inputs = inputs.to(self.local_model.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.local_model.generate(
                    inputs,
                    max_new_tokens=512,
                    temperature=0.7,
                    do_sample=True,
                    top_p=0.9,
                    pad_token_id=self.tokenizer.eos_token_id,
                    attention_mask=torch.ones_like(inputs)
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the new part (after the prompt)
            response = response[len(prompt):].strip()
            
            return response
            
        except Exception as e:
            app_logger.error(f"Sync local generation error: {e}")
            raise
    
    async def _generate_api_response(self, conversation: List[Dict[str, str]]) -> str:
        """Generate response using Hugging Face API."""
        try:
            # Format conversation
            prompt = self._format_conversation_for_model(conversation)
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_new_tokens": 512,
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "do_sample": True,
                    "return_full_text": False
                }
            }
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.api_url}/{self.model_name}",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if isinstance(result, list) and len(result) > 0:
                        return result[0].get("generated_text", "").strip()
                    else:
                        return "I apologize, but I couldn't generate a response."
                else:
                    app_logger.error(f"HF API error: {response.status_code} - {response.text}")
                    raise Exception(f"API request failed: {response.status_code}")
                    
        except Exception as e:
            app_logger.error(f"API generation error: {e}")
            raise
    
    async def stream_response(self, conversation: List[Dict[str, str]]) -> AsyncGenerator[str, None]:
        """Stream a response (simplified implementation)."""
        try:
            # For simplicity, generate full response and yield in chunks
            response = await self.generate_response(conversation)
            
            # Yield response in chunks
            chunk_size = 10
            for i in range(0, len(response), chunk_size):
                chunk = response[i:i + chunk_size]
                yield chunk
                await asyncio.sleep(0.1)  # Simulate streaming delay
                
        except Exception as e:
            app_logger.error(f"Hugging Face streaming error: {e}")
            yield f"Error: {str(e)}"
    
    def _format_conversation_for_model(self, conversation: List[Dict[str, str]]) -> str:
        """Format conversation for the specific model."""
        # Different models may need different formatting
        if "DialoGPT" in self.model_name:
            return self._format_for_dialogpt(conversation)
        elif "Llama" in self.model_name:
            return self._format_for_llama(conversation)
        else:
            return self._format_generic(conversation)
    
    def _format_for_dialogpt(self, conversation: List[Dict[str, str]]) -> str:
        """Format conversation for DialoGPT models."""
        formatted_parts = []
        for msg in conversation:
            if msg["role"] == "user":
                formatted_parts.append(f"User: {msg['content']}")
            elif msg["role"] == "assistant":
                formatted_parts.append(f"Bot: {msg['content']}")
        
        formatted_parts.append("Bot:")
        return "\n".join(formatted_parts)
    
    def _format_for_llama(self, conversation: List[Dict[str, str]]) -> str:
        """Format conversation for Llama models."""
        formatted_parts = ["<s>[INST] "]
        
        for i, msg in enumerate(conversation):
            if msg["role"] == "user":
                if i > 0:
                    formatted_parts.append(" [INST] ")
                formatted_parts.append(msg["content"])
            elif msg["role"] == "assistant":
                formatted_parts.append(" [/INST] ")
                formatted_parts.append(msg["content"])
        
        if conversation[-1]["role"] == "user":
            formatted_parts.append(" [/INST]")
        
        return "".join(formatted_parts)
    
    def _format_generic(self, conversation: List[Dict[str, str]]) -> str:
        """Generic conversation formatting."""
        formatted_parts = []
        for msg in conversation:
            role = msg["role"].capitalize()
            formatted_parts.append(f"{role}: {msg['content']}")
        
        formatted_parts.append("Assistant:")
        return "\n".join(formatted_parts)
    
    async def validate_connection(self) -> bool:
        """Validate connection to Hugging Face."""
        try:
            if self.use_local:
                # Test local model
                test_conversation = [{"role": "user", "content": "Hello"}]
                response = await self.generate_response(test_conversation)
                return bool(response)
            else:
                # Test API connection
                headers = {"Authorization": f"Bearer {self.api_key}"}
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get(
                        f"{self.api_url}/{self.model_name}",
                        headers=headers
                    )
                    return response.status_code == 200
        except Exception as e:
            app_logger.error(f"Hugging Face connection validation failed: {e}")
            return False
