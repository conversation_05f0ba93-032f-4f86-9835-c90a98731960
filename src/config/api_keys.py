"""Secure API key management and validation."""

import os
import re
from typing import Optional, Dict, Any
from pathlib import Path
from cryptography.fernet import Fernet
import base64
import hashlib

from config.logging_config import app_logger


class APIKeyManager:
    """Secure API key management with encryption and validation."""
    
    def __init__(self, env_file: str = ".env"):
        self.env_file = Path(env_file)
        self._encryption_key = None
        self._load_encryption_key()
    
    def _load_encryption_key(self):
        """Load or generate encryption key for sensitive data."""
        key_file = Path("data/.key")
        key_file.parent.mkdir(parents=True, exist_ok=True)
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                self._encryption_key = f.read()
        else:
            # Generate new key
            self._encryption_key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(self._encryption_key)
            # Secure the key file
            os.chmod(key_file, 0o600)
    
    def _encrypt_value(self, value: str) -> str:
        """Encrypt a sensitive value."""
        if not value:
            return value
        
        fernet = Fernet(self._encryption_key)
        encrypted = fernet.encrypt(value.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def _decrypt_value(self, encrypted_value: str) -> str:
        """Decrypt a sensitive value."""
        if not encrypted_value:
            return encrypted_value
        
        try:
            fernet = Fernet(self._encryption_key)
            decoded = base64.urlsafe_b64decode(encrypted_value.encode())
            return fernet.decrypt(decoded).decode()
        except Exception as e:
            app_logger.error(f"Failed to decrypt value: {e}")
            return ""
    
    def validate_groq_api_key(self, api_key: str) -> Dict[str, Any]:
        """Validate Groq API key format and structure."""
        validation_result = {
            "is_valid": False,
            "errors": [],
            "warnings": [],
            "masked_key": ""
        }
        
        if not api_key or not isinstance(api_key, str):
            validation_result["errors"].append("API key must be a non-empty string")
            return validation_result
        
        api_key = api_key.strip()
        
        # Basic length validation
        if len(api_key) < 20:
            validation_result["errors"].append("Groq API key appears to be too short")
        
        # Format validation (Groq keys typically start with 'gsk_')
        if not api_key.startswith('gsk_'):
            validation_result["warnings"].append("Groq API key should start with 'gsk_'")
        
        # Character validation
        if not re.match(r'^[a-zA-Z0-9_-]+$', api_key):
            validation_result["errors"].append("API key contains invalid characters")
        
        # Create masked version
        if len(api_key) > 12:
            validation_result["masked_key"] = api_key[:8] + "*" * (len(api_key) - 12) + api_key[-4:]
        else:
            validation_result["masked_key"] = "*" * len(api_key)
        
        validation_result["is_valid"] = len(validation_result["errors"]) == 0
        return validation_result
    
    def validate_huggingface_api_key(self, api_key: str) -> Dict[str, Any]:
        """Validate Hugging Face API key format and structure."""
        validation_result = {
            "is_valid": False,
            "errors": [],
            "warnings": [],
            "masked_key": ""
        }
        
        if not api_key or not isinstance(api_key, str):
            validation_result["errors"].append("API key must be a non-empty string")
            return validation_result
        
        api_key = api_key.strip()
        
        # Basic length validation
        if len(api_key) < 20:
            validation_result["errors"].append("Hugging Face API key appears to be too short")
        
        # Format validation (HF keys start with 'hf_')
        if not api_key.startswith('hf_'):
            validation_result["errors"].append("Hugging Face API key must start with 'hf_'")
        
        # Character validation
        if not re.match(r'^hf_[a-zA-Z0-9]+$', api_key):
            validation_result["errors"].append("API key contains invalid characters")
        
        # Create masked version
        if len(api_key) > 12:
            validation_result["masked_key"] = api_key[:8] + "*" * (len(api_key) - 12) + api_key[-4:]
        else:
            validation_result["masked_key"] = "*" * len(api_key)
        
        validation_result["is_valid"] = len(validation_result["errors"]) == 0
        return validation_result
    
    def load_api_keys_from_env(self) -> Dict[str, Optional[str]]:
        """Load API keys from environment file with validation."""
        api_keys = {
            "groq_api_key": None,
            "huggingface_api_key": None
        }
        
        # Load from environment variables first
        groq_key = os.getenv("GROQ_API_KEY")
        hf_key = os.getenv("HUGGINGFACE_API_KEY")
        
        # Load from .env file if not in environment
        if not groq_key or not hf_key:
            env_vars = self._load_env_file()
            groq_key = groq_key or env_vars.get("GROQ_API_KEY")
            hf_key = hf_key or env_vars.get("HUGGINGFACE_API_KEY")
        
        # Validate and store keys
        if groq_key:
            validation = self.validate_groq_api_key(groq_key)
            if validation["is_valid"]:
                api_keys["groq_api_key"] = groq_key
                app_logger.info(f"Groq API key loaded: {validation['masked_key']}")
            else:
                app_logger.error(f"Invalid Groq API key: {', '.join(validation['errors'])}")
                if validation["warnings"]:
                    app_logger.warning(f"Groq API key warnings: {', '.join(validation['warnings'])}")
        
        if hf_key:
            validation = self.validate_huggingface_api_key(hf_key)
            if validation["is_valid"]:
                api_keys["huggingface_api_key"] = hf_key
                app_logger.info(f"Hugging Face API key loaded: {validation['masked_key']}")
            else:
                app_logger.error(f"Invalid Hugging Face API key: {', '.join(validation['errors'])}")
        
        return api_keys
    
    def _load_env_file(self) -> Dict[str, str]:
        """Load environment variables from .env file."""
        env_vars = {}
        
        if not self.env_file.exists():
            app_logger.warning(f"Environment file {self.env_file} not found")
            return env_vars
        
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    # Parse key=value pairs
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # Remove quotes if present
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        env_vars[key] = value
                    else:
                        app_logger.warning(f"Invalid line in {self.env_file}:{line_num}: {line}")
            
            app_logger.info(f"Loaded {len(env_vars)} environment variables from {self.env_file}")
            
        except Exception as e:
            app_logger.error(f"Error loading environment file {self.env_file}: {e}")
        
        return env_vars
    
    def store_encrypted_key(self, key_name: str, api_key: str) -> bool:
        """Store an encrypted API key to secure storage."""
        try:
            encrypted_key = self._encrypt_value(api_key)
            
            # Store in secure file
            secure_file = Path("data/.secure_keys")
            secure_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Load existing keys
            stored_keys = {}
            if secure_file.exists():
                with open(secure_file, 'r') as f:
                    import json
                    stored_keys = json.load(f)
            
            # Add new key
            stored_keys[key_name] = encrypted_key
            
            # Save back
            with open(secure_file, 'w') as f:
                import json
                json.dump(stored_keys, f)
            
            # Secure the file
            os.chmod(secure_file, 0o600)
            
            app_logger.info(f"Encrypted API key '{key_name}' stored securely")
            return True
            
        except Exception as e:
            app_logger.error(f"Failed to store encrypted key '{key_name}': {e}")
            return False
    
    def load_encrypted_key(self, key_name: str) -> Optional[str]:
        """Load and decrypt an API key from secure storage."""
        try:
            secure_file = Path("data/.secure_keys")
            
            if not secure_file.exists():
                return None
            
            with open(secure_file, 'r') as f:
                import json
                stored_keys = json.load(f)
            
            if key_name not in stored_keys:
                return None
            
            encrypted_key = stored_keys[key_name]
            return self._decrypt_value(encrypted_key)
            
        except Exception as e:
            app_logger.error(f"Failed to load encrypted key '{key_name}': {e}")
            return None
    
    def get_key_hash(self, api_key: str) -> str:
        """Get a hash of the API key for comparison without storing the actual key."""
        if not api_key:
            return ""
        
        return hashlib.sha256(api_key.encode()).hexdigest()[:16]


# Global instance
api_key_manager = APIKeyManager()
