"""Application settings and configuration."""

import os
from functools import lru_cache
from typing import List, Optional
from pydantic import BaseSettings, Field, validator
from pathlib import Path


class Settings(BaseSettings):
    """Application settings."""

    # Application
    app_name: str = Field(default="Chat AI", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")

    # Server
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=4, env="WORKERS")

    # API Keys - Secured with validation
    groq_api_key: Optional[str] = Field(default=None, env="GROQ_API_KEY")
    huggingface_api_key: Optional[str] = Field(default=None, env="HUGGINGFACE_API_KEY")

    @validator('groq_api_key')
    def validate_groq_api_key(cls, v):
        """Validate Groq API key format."""
        if v is not None:
            if not isinstance(v, str) or len(v.strip()) == 0:
                raise ValueError("Groq API key must be a non-empty string")
            if len(v) < 10:  # Basic length check
                raise ValueError("Groq API key appears to be too short")
            # Mask the key in logs
            cls._masked_groq_key = v[:8] + "*" * (len(v) - 12) + v[-4:] if len(v) > 12 else "*" * len(v)
        return v

    @validator('huggingface_api_key')
    def validate_huggingface_api_key(cls, v):
        """Validate Hugging Face API key format."""
        if v is not None:
            if not isinstance(v, str) or len(v.strip()) == 0:
                raise ValueError("Hugging Face API key must be a non-empty string")
            if not v.startswith('hf_'):
                raise ValueError("Hugging Face API key must start with 'hf_'")
            # Mask the key in logs
            cls._masked_hf_key = v[:8] + "*" * (len(v) - 12) + v[-4:] if len(v) > 12 else "*" * len(v)
        return v

    def get_masked_groq_key(self) -> str:
        """Get masked Groq API key for logging."""
        return getattr(self.__class__, '_masked_groq_key', 'Not set')

    def get_masked_hf_key(self) -> str:
        """Get masked Hugging Face API key for logging."""
        return getattr(self.__class__, '_masked_hf_key', 'Not set')

    def has_groq_key(self) -> bool:
        """Check if Groq API key is available."""
        return self.groq_api_key is not None and len(self.groq_api_key.strip()) > 0

    def has_huggingface_key(self) -> bool:
        """Check if Hugging Face API key is available."""
        return self.huggingface_api_key is not None and len(self.huggingface_api_key.strip()) > 0

    # Database
    database_url: str = Field(default="sqlite:///./data/chat_ai.db", env="DATABASE_URL")
    
    # Redis
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Models
    default_model: str = Field(default="groq", env="DEFAULT_MODEL")
    groq_model: str = Field(default="mixtral-8x7b-32768", env="GROQ_MODEL")
    huggingface_model: str = Field(default="microsoft/DialoGPT-medium", env="HUGGINGFACE_MODEL")
    
    # Chat Settings
    max_conversation_length: int = Field(default=50, env="MAX_CONVERSATION_LENGTH")
    max_message_length: int = Field(default=2000, env="MAX_MESSAGE_LENGTH")
    session_timeout: int = Field(default=3600, env="SESSION_TIMEOUT")
    
    # Security
    secret_key: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="ALLOWED_ORIGINS"
    )
    allowed_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE"],
        env="ALLOWED_METHODS"
    )
    allowed_headers: List[str] = Field(default=["*"], env="ALLOWED_HEADERS")
    
    # File Upload
    max_file_size: int = Field(default=10485760, env="MAX_FILE_SIZE")  # 10MB
    allowed_file_types: List[str] = Field(
        default=["txt", "pdf", "docx"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # Logging
    log_file: str = Field(default="./data/logs/app.log", env="LOG_FILE")
    log_rotation: str = Field(default="1 week", env="LOG_ROTATION")
    log_retention: str = Field(default="4 weeks", env="LOG_RETENTION")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
