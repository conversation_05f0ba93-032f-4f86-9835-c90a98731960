"""Helper utility functions."""

import json
import hashlib
import uuid
from datetime import datetime
from typing import Any, Optional, Dict, Union
from pathlib import Path

from config.logging_config import app_logger


def generate_id(prefix: str = "") -> str:
    """Generate a unique identifier."""
    unique_id = str(uuid.uuid4())
    return f"{prefix}{unique_id}" if prefix else unique_id


def format_timestamp(dt: Optional[datetime] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """Format datetime to string."""
    if dt is None:
        dt = datetime.utcnow()
    return dt.strftime(format_str)


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely load JSON string."""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError) as e:
        app_logger.warning(f"Failed to parse JSON: {e}")
        return default


def safe_json_dumps(obj: Any, default: Any = None) -> str:
    """Safely dump object to JSON string."""
    try:
        return json.dumps(obj, default=str, ensure_ascii=False)
    except (TypeError, ValueError) as e:
        app_logger.warning(f"Failed to serialize to JSON: {e}")
        return json.dumps(default) if default is not None else "{}"


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length."""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def calculate_hash(text: str, algorithm: str = "sha256") -> str:
    """Calculate hash of text."""
    hash_obj = hashlib.new(algorithm)
    hash_obj.update(text.encode('utf-8'))
    return hash_obj.hexdigest()


def ensure_directory(path: Union[str, Path]) -> Path:
    """Ensure directory exists, create if it doesn't."""
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def read_file_safe(file_path: Union[str, Path], encoding: str = "utf-8") -> Optional[str]:
    """Safely read file content."""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except (FileNotFoundError, IOError, UnicodeDecodeError) as e:
        app_logger.error(f"Failed to read file {file_path}: {e}")
        return None


def write_file_safe(file_path: Union[str, Path], content: str, encoding: str = "utf-8") -> bool:
    """Safely write content to file."""
    try:
        file_path = Path(file_path)
        ensure_directory(file_path.parent)
        
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        return True
    except (IOError, UnicodeEncodeError) as e:
        app_logger.error(f"Failed to write file {file_path}: {e}")
        return False


def get_file_size(file_path: Union[str, Path]) -> Optional[int]:
    """Get file size in bytes."""
    try:
        return Path(file_path).stat().st_size
    except (FileNotFoundError, OSError) as e:
        app_logger.error(f"Failed to get file size for {file_path}: {e}")
        return None


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """Merge multiple dictionaries."""
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """Flatten nested dictionary."""
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def chunk_list(lst: list, chunk_size: int) -> list:
    """Split list into chunks of specified size."""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def remove_duplicates(lst: list, key: Optional[callable] = None) -> list:
    """Remove duplicates from list while preserving order."""
    if key is None:
        seen = set()
        return [x for x in lst if not (x in seen or seen.add(x))]
    else:
        seen = set()
        return [x for x in lst if not (key(x) in seen or seen.add(key(x)))]


def deep_get(dictionary: Dict[str, Any], keys: str, default: Any = None) -> Any:
    """Get nested dictionary value using dot notation."""
    keys = keys.split('.')
    for key in keys:
        if isinstance(dictionary, dict) and key in dictionary:
            dictionary = dictionary[key]
        else:
            return default
    return dictionary


def deep_set(dictionary: Dict[str, Any], keys: str, value: Any) -> None:
    """Set nested dictionary value using dot notation."""
    keys = keys.split('.')
    for key in keys[:-1]:
        dictionary = dictionary.setdefault(key, {})
    dictionary[keys[-1]] = value


def is_valid_json(json_str: str) -> bool:
    """Check if string is valid JSON."""
    try:
        json.loads(json_str)
        return True
    except (json.JSONDecodeError, TypeError):
        return False


def extract_numbers(text: str) -> list:
    """Extract all numbers from text."""
    import re
    return [float(match) for match in re.findall(r'-?\d+\.?\d*', text)]


def clean_whitespace(text: str) -> str:
    """Clean excessive whitespace from text."""
    import re
    # Replace multiple whitespace with single space
    text = re.sub(r'\s+', ' ', text)
    # Remove leading/trailing whitespace
    return text.strip()


def mask_sensitive_data(text: str, mask_char: str = "*") -> str:
    """Mask sensitive data in text (emails, phone numbers, etc.)."""
    import re
    
    # Mask email addresses
    text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', 
                  lambda m: m.group()[:2] + mask_char * (len(m.group()) - 4) + m.group()[-2:], text)
    
    # Mask phone numbers (simple pattern)
    text = re.sub(r'\b\d{3}-?\d{3}-?\d{4}\b', 
                  lambda m: mask_char * len(m.group()), text)
    
    return text
