"""Client factory for AI service initialization with secure API key management."""

from typing import Optional, Dict, Any, List, Union
import asyncio
from enum import Enum

from config.settings import get_settings
from config.api_keys import api_key_manager
from config.logging_config import app_logger
from integrations.base_client import BaseAIClient
from integrations.groq_client import GroqClient
from integrations.huggingface_client import HuggingFaceClient


class AIProvider(Enum):
    """Supported AI providers."""
    GROQ = "groq"
    HUGGINGFACE = "huggingface"


class ClientFactory:
    """Factory for creating and managing AI clients with secure API key handling."""
    
    def __init__(self):
        self.settings = get_settings()
        self._clients: Dict[str, BaseAIClient] = {}
        self._api_keys: Dict[str, str] = {}
        self._load_api_keys()
    
    def _load_api_keys(self):
        """Load and validate API keys securely."""
        try:
            # Load API keys using the secure manager
            keys = api_key_manager.load_api_keys_from_env()
            
            # Store validated keys
            if keys.get("groq_api_key"):
                self._api_keys[AIProvider.GROQ.value] = keys["groq_api_key"]
                app_logger.info("Groq API key loaded and validated")
            else:
                app_logger.warning("Groq API key not available or invalid")
            
            if keys.get("huggingface_api_key"):
                self._api_keys[AIProvider.HUGGINGFACE.value] = keys["huggingface_api_key"]
                app_logger.info("Hugging Face API key loaded and validated")
            else:
                app_logger.warning("Hugging Face API key not available or invalid")
            
            if not self._api_keys:
                app_logger.error("No valid API keys found. Please check your .env file.")
                
        except Exception as e:
            app_logger.error(f"Error loading API keys: {e}")
    
    def create_groq_client(self, api_key: Optional[str] = None) -> Optional[GroqClient]:
        """Create and initialize Groq client."""
        try:
            # Use provided key or loaded key
            key = api_key or self._api_keys.get(AIProvider.GROQ.value)
            
            if not key:
                app_logger.error("Groq API key not available")
                return None
            
            # Validate key before creating client
            validation = api_key_manager.validate_groq_api_key(key)
            if not validation["is_valid"]:
                app_logger.error(f"Invalid Groq API key: {', '.join(validation['errors'])}")
                return None
            
            # Create client
            client = GroqClient(key)
            app_logger.info(f"Groq client created successfully with key: {validation['masked_key']}")
            
            return client
            
        except Exception as e:
            app_logger.error(f"Failed to create Groq client: {e}")
            return None
    
    def create_huggingface_client(self, api_key: Optional[str] = None) -> Optional[HuggingFaceClient]:
        """Create and initialize Hugging Face client."""
        try:
            # Use provided key or loaded key
            key = api_key or self._api_keys.get(AIProvider.HUGGINGFACE.value)
            
            if not key:
                app_logger.warning("Hugging Face API key not available, will try local models only")
                # HF client can work without API key for local models
                key = ""
            
            if key:
                # Validate key if provided
                validation = api_key_manager.validate_huggingface_api_key(key)
                if not validation["is_valid"]:
                    app_logger.error(f"Invalid Hugging Face API key: {', '.join(validation['errors'])}")
                    key = ""  # Fall back to local models
            
            # Create client
            client = HuggingFaceClient(key)
            
            if key:
                validation = api_key_manager.validate_huggingface_api_key(key)
                app_logger.info(f"Hugging Face client created with API key: {validation['masked_key']}")
            else:
                app_logger.info("Hugging Face client created for local models only")
            
            return client
            
        except Exception as e:
            app_logger.error(f"Failed to create Hugging Face client: {e}")
            return None
    
    async def create_client(self, provider: Union[AIProvider, str], api_key: Optional[str] = None) -> Optional[BaseAIClient]:
        """Create a client for the specified provider."""
        if isinstance(provider, str):
            try:
                provider = AIProvider(provider.lower())
            except ValueError:
                app_logger.error(f"Unsupported AI provider: {provider}")
                return None
        
        if provider == AIProvider.GROQ:
            return self.create_groq_client(api_key)
        elif provider == AIProvider.HUGGINGFACE:
            return self.create_huggingface_client(api_key)
        else:
            app_logger.error(f"Unsupported AI provider: {provider}")
            return None
    
    async def create_all_clients(self) -> Dict[str, BaseAIClient]:
        """Create all available clients based on available API keys."""
        clients = {}
        
        # Create Groq client if key is available
        if AIProvider.GROQ.value in self._api_keys:
            groq_client = self.create_groq_client()
            if groq_client:
                clients[AIProvider.GROQ.value] = groq_client
        
        # Create Hugging Face client (works with or without API key)
        hf_client = self.create_huggingface_client()
        if hf_client:
            clients[AIProvider.HUGGINGFACE.value] = hf_client
        
        app_logger.info(f"Created {len(clients)} AI clients: {list(clients.keys())}")
        return clients
    
    async def validate_client(self, client: BaseAIClient) -> bool:
        """Validate that a client is working properly."""
        try:
            is_valid = await client.validate_connection()
            if is_valid:
                app_logger.info(f"Client {client.__class__.__name__} validation successful")
            else:
                app_logger.warning(f"Client {client.__class__.__name__} validation failed")
            return is_valid
        except Exception as e:
            app_logger.error(f"Error validating client {client.__class__.__name__}: {e}")
            return False
    
    async def validate_all_clients(self, clients: Dict[str, BaseAIClient]) -> Dict[str, bool]:
        """Validate all clients and return their status."""
        validation_results = {}
        
        for provider, client in clients.items():
            validation_results[provider] = await self.validate_client(client)
        
        return validation_results
    
    def get_available_providers(self) -> List[str]:
        """Get list of providers with valid API keys."""
        return list(self._api_keys.keys())
    
    def has_provider(self, provider: Union[AIProvider, str]) -> bool:
        """Check if a provider is available."""
        if isinstance(provider, AIProvider):
            provider = provider.value
        return provider in self._api_keys
    
    def get_client_info(self, provider: Union[AIProvider, str]) -> Dict[str, Any]:
        """Get information about a client/provider."""
        if isinstance(provider, AIProvider):
            provider = provider.value
        
        info = {
            "provider": provider,
            "available": provider in self._api_keys,
            "api_key_present": provider in self._api_keys
        }
        
        if provider == AIProvider.GROQ.value:
            info.update({
                "model": self.settings.groq_model,
                "description": "Groq API for fast inference"
            })
        elif provider == AIProvider.HUGGINGFACE.value:
            info.update({
                "model": self.settings.huggingface_model,
                "description": "Hugging Face for diverse model access",
                "supports_local": True
            })
        
        return info
    
    def refresh_api_keys(self):
        """Refresh API keys from environment."""
        app_logger.info("Refreshing API keys...")
        self._api_keys.clear()
        self._load_api_keys()


# Helper functions for easy client creation
async def create_groq_client(api_key: Optional[str] = None) -> Optional[GroqClient]:
    """Helper function to create a Groq client."""
    factory = ClientFactory()
    return factory.create_groq_client(api_key)


async def create_huggingface_client(api_key: Optional[str] = None) -> Optional[HuggingFaceClient]:
    """Helper function to create a Hugging Face client."""
    factory = ClientFactory()
    return factory.create_huggingface_client(api_key)


async def create_all_available_clients() -> Dict[str, BaseAIClient]:
    """Helper function to create all available clients."""
    factory = ClientFactory()
    return await factory.create_all_clients()


async def validate_api_keys() -> Dict[str, bool]:
    """Helper function to validate all API keys by testing client connections."""
    factory = ClientFactory()
    clients = await factory.create_all_clients()
    return await factory.validate_all_clients(clients)


def get_available_providers() -> List[str]:
    """Helper function to get available AI providers."""
    factory = ClientFactory()
    return factory.get_available_providers()


# Global factory instance
client_factory = ClientFactory()
