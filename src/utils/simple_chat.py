"""
Simple chat utilities for easy AI response generation.
"""

import asyncio
from typing import List, Dict, Optional, Union, AsyncGenerator

from core.response_generator import (
    generate_response as _generate_response,
    generate_streaming_response as _generate_streaming_response,
    get_available_providers,
    validate_provider
)
from config.logging_config import app_logger


class SimpleChatBot:
    """Simple chatbot interface for easy AI interactions."""
    
    def __init__(self, provider: str = "auto", system_message: Optional[str] = None):
        """
        Initialize simple chatbot.
        
        Args:
            provider: AI provider to use ('groq', 'huggingface', or 'auto')
            system_message: Optional system message to set bot behavior
        """
        self.provider = provider
        self.conversation: List[Dict[str, str]] = []
        
        if system_message:
            self.conversation.append({
                "role": "system",
                "content": system_message
            })
    
    async def chat(self, message: str) -> str:
        """
        Send a message and get AI response.
        
        Args:
            message: User message
            
        Returns:
            AI response
        """
        # Add user message
        self.conversation.append({
            "role": "user",
            "content": message
        })
        
        try:
            # Generate response
            response = await _generate_response(
                messages=self.conversation,
                provider=self.provider
            )
            
            # Add AI response to conversation
            self.conversation.append({
                "role": "assistant",
                "content": response
            })
            
            return response
            
        except Exception as e:
            app_logger.error(f"Chat error: {e}")
            error_msg = f"Sorry, I encountered an error: {str(e)}"
            
            # Add error to conversation
            self.conversation.append({
                "role": "assistant",
                "content": error_msg
            })
            
            return error_msg
    
    async def chat_stream(self, message: str) -> AsyncGenerator[str, None]:
        """
        Send a message and get streaming AI response.
        
        Args:
            message: User message
            
        Yields:
            Response chunks
        """
        # Add user message
        self.conversation.append({
            "role": "user",
            "content": message
        })
        
        try:
            full_response = ""
            
            async for chunk in _generate_streaming_response(
                messages=self.conversation,
                provider=self.provider
            ):
                full_response += chunk
                yield chunk
            
            # Add complete response to conversation
            self.conversation.append({
                "role": "assistant",
                "content": full_response
            })
            
        except Exception as e:
            app_logger.error(f"Streaming chat error: {e}")
            error_msg = f"Error: {str(e)}"
            yield error_msg
            
            self.conversation.append({
                "role": "assistant",
                "content": error_msg
            })
    
    def clear_conversation(self):
        """Clear conversation history (keep system message if any)."""
        system_messages = [msg for msg in self.conversation if msg["role"] == "system"]
        self.conversation = system_messages
    
    def get_conversation(self) -> List[Dict[str, str]]:
        """Get current conversation history."""
        return self.conversation.copy()
    
    def get_last_response(self) -> Optional[str]:
        """Get the last AI response."""
        for msg in reversed(self.conversation):
            if msg["role"] == "assistant":
                return msg["content"]
        return None


# Simple wrapper functions for direct usage
async def generate_response(
    messages: List[Dict[str, str]],
    provider: str = "auto",
    temperature: float = 0.7,
    max_tokens: int = 1024
) -> str:
    """
    Generate AI response from message history.
    
    Args:
        messages: List of message dicts with 'role' and 'content'
        provider: AI provider ('groq', 'huggingface', 'auto')
        temperature: Response creativity (0.0-1.0)
        max_tokens: Maximum response length
    
    Returns:
        AI response text
    
    Example:
        >>> messages = [{"role": "user", "content": "Hello!"}]
        >>> response = await generate_response(messages)
        >>> print(response)
    """
    return await _generate_response(
        messages=messages,
        provider=provider,
        temperature=temperature,
        max_tokens=max_tokens
    )


async def ask_ai(question: str, provider: str = "auto") -> str:
    """
    Ask a single question to AI.
    
    Args:
        question: Question to ask
        provider: AI provider to use
    
    Returns:
        AI response
    
    Example:
        >>> response = await ask_ai("What is Python?")
        >>> print(response)
    """
    messages = [{"role": "user", "content": question}]
    return await generate_response(messages, provider=provider)


async def continue_conversation(
    conversation: List[Dict[str, str]], 
    new_message: str,
    provider: str = "auto"
) -> tuple[List[Dict[str, str]], str]:
    """
    Continue an existing conversation.
    
    Args:
        conversation: Existing conversation history
        new_message: New user message
        provider: AI provider to use
    
    Returns:
        Tuple of (updated_conversation, ai_response)
    
    Example:
        >>> conv = [{"role": "user", "content": "Hi"}]
        >>> conv, response = await continue_conversation(conv, "How are you?")
    """
    # Add new message
    updated_conversation = conversation.copy()
    updated_conversation.append({"role": "user", "content": new_message})
    
    # Generate response
    response = await generate_response(updated_conversation, provider=provider)
    
    # Add AI response
    updated_conversation.append({"role": "assistant", "content": response})
    
    return updated_conversation, response


async def chat_with_context(
    question: str,
    context: str,
    provider: str = "auto"
) -> str:
    """
    Ask a question with additional context.
    
    Args:
        question: Question to ask
        context: Additional context information
        provider: AI provider to use
    
    Returns:
        AI response
    
    Example:
        >>> context = "You are a Python programming expert."
        >>> response = await chat_with_context("How do I create a list?", context)
    """
    messages = [
        {"role": "system", "content": context},
        {"role": "user", "content": question}
    ]
    return await generate_response(messages, provider=provider)


async def streaming_chat(question: str, provider: str = "auto") -> AsyncGenerator[str, None]:
    """
    Get streaming response for a question.
    
    Args:
        question: Question to ask
        provider: AI provider to use
    
    Yields:
        Response chunks
    
    Example:
        >>> async for chunk in streaming_chat("Tell me a story"):
        ...     print(chunk, end="", flush=True)
    """
    messages = [{"role": "user", "content": question}]
    
    async for chunk in _generate_streaming_response(messages, provider=provider):
        yield chunk


# Utility functions
async def get_ai_providers() -> List[str]:
    """Get list of available AI providers."""
    return await get_available_providers()


async def test_provider(provider: str) -> bool:
    """Test if a provider is working."""
    return await validate_provider(provider)


async def quick_test() -> Dict[str, bool]:
    """Quick test of all providers."""
    providers = await get_ai_providers()
    results = {}
    
    for provider in providers:
        results[provider] = await test_provider(provider)
    
    return results


# Synchronous wrappers for simple usage
def sync_ask_ai(question: str, provider: str = "auto") -> str:
    """Synchronous version of ask_ai."""
    return asyncio.run(ask_ai(question, provider))


def sync_generate_response(
    messages: List[Dict[str, str]],
    provider: str = "auto"
) -> str:
    """Synchronous version of generate_response."""
    return asyncio.run(generate_response(messages, provider))


# Pre-configured chatbots
async def create_coding_assistant() -> SimpleChatBot:
    """Create a coding assistant chatbot."""
    system_msg = (
        "You are a helpful programming assistant. You provide clear, "
        "concise explanations and practical code examples. You focus on "
        "best practices and help users learn effectively."
    )
    return SimpleChatBot(system_message=system_msg)


async def create_general_assistant() -> SimpleChatBot:
    """Create a general purpose assistant."""
    system_msg = (
        "You are a helpful, friendly, and knowledgeable assistant. "
        "You provide accurate information and helpful responses to "
        "a wide variety of questions and tasks."
    )
    return SimpleChatBot(system_message=system_msg)


async def create_creative_writer() -> SimpleChatBot:
    """Create a creative writing assistant."""
    system_msg = (
        "You are a creative writing assistant. You help with storytelling, "
        "creative writing, poetry, and other creative endeavors. You are "
        "imaginative, inspiring, and provide detailed creative content."
    )
    return SimpleChatBot(system_message=system_msg)


# Example usage function
async def demo_simple_chat():
    """Demonstrate simple chat functionality."""
    print("🤖 Simple Chat Demo")
    print("=" * 30)
    
    # Simple question
    print("1. Simple question:")
    response = await ask_ai("What is machine learning?")
    print(f"AI: {response[:100]}...")
    
    # Chatbot conversation
    print("\n2. Chatbot conversation:")
    bot = await create_coding_assistant()
    
    response1 = await bot.chat("How do I create a Python function?")
    print(f"User: How do I create a Python function?")
    print(f"AI: {response1[:100]}...")
    
    response2 = await bot.chat("Can you show me an example?")
    print(f"User: Can you show me an example?")
    print(f"AI: {response2[:100]}...")
    
    # Test providers
    print("\n3. Provider test:")
    test_results = await quick_test()
    for provider, working in test_results.items():
        status = "✅" if working else "❌"
        print(f"{provider}: {status}")


if __name__ == "__main__":
    asyncio.run(demo_simple_chat())
