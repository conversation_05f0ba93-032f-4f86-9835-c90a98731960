"""Secure environment variable loading and validation."""

import os
import re
from typing import Dict, Optional, Any, List
from pathlib import Path
from dotenv import load_dotenv

from config.logging_config import app_logger


class SecureEnvLoader:
    """Secure environment variable loader with validation and sanitization."""
    
    def __init__(self, env_file: str = ".env", required_vars: Optional[List[str]] = None):
        self.env_file = Path(env_file)
        self.required_vars = required_vars or []
        self.loaded_vars: Dict[str, str] = {}
        self.sensitive_patterns = [
            r'.*key.*',
            r'.*token.*',
            r'.*secret.*',
            r'.*password.*',
            r'.*auth.*'
        ]
    
    def load_environment(self, override: bool = False) -> Dict[str, str]:
        """Load environment variables securely."""
        try:
            # Load from .env file if it exists
            if self.env_file.exists():
                app_logger.info(f"Loading environment from {self.env_file}")
                load_dotenv(self.env_file, override=override)
                self._validate_env_file()
            else:
                app_logger.warning(f"Environment file {self.env_file} not found")
            
            # Load all environment variables
            self.loaded_vars = dict(os.environ)
            
            # Validate required variables
            self._validate_required_vars()
            
            # Log loaded variables (with sensitive data masked)
            self._log_loaded_vars()
            
            return self.loaded_vars
            
        except Exception as e:
            app_logger.error(f"Error loading environment: {e}")
            return {}
    
    def _validate_env_file(self):
        """Validate the .env file format and content."""
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                # Check for valid key=value format
                if '=' not in line:
                    app_logger.warning(f"Invalid format in {self.env_file}:{line_num}: {line}")
                    continue
                
                key, value = line.split('=', 1)
                key = key.strip()
                
                # Validate key format
                if not re.match(r'^[A-Z_][A-Z0-9_]*$', key):
                    app_logger.warning(f"Invalid key format in {self.env_file}:{line_num}: {key}")
                
                # Check for potential security issues
                if self._is_sensitive_var(key) and not value.strip():
                    app_logger.warning(f"Sensitive variable '{key}' is empty")
                
        except Exception as e:
            app_logger.error(f"Error validating .env file: {e}")
    
    def _validate_required_vars(self):
        """Validate that all required variables are present."""
        missing_vars = []
        
        for var in self.required_vars:
            if var not in self.loaded_vars or not self.loaded_vars[var].strip():
                missing_vars.append(var)
        
        if missing_vars:
            app_logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    def _is_sensitive_var(self, var_name: str) -> bool:
        """Check if a variable name indicates sensitive data."""
        var_lower = var_name.lower()
        return any(re.match(pattern, var_lower) for pattern in self.sensitive_patterns)
    
    def _mask_sensitive_value(self, value: str) -> str:
        """Mask sensitive values for logging."""
        if not value:
            return "Not set"
        
        if len(value) <= 8:
            return "*" * len(value)
        
        return value[:4] + "*" * (len(value) - 8) + value[-4:]
    
    def _log_loaded_vars(self):
        """Log loaded variables with sensitive data masked."""
        app_logger.info("Loaded environment variables:")
        
        for key, value in sorted(self.loaded_vars.items()):
            if self._is_sensitive_var(key):
                masked_value = self._mask_sensitive_value(value)
                app_logger.info(f"  {key}: {masked_value}")
            else:
                # Only log non-sensitive variables
                if key.startswith(('APP_', 'DEBUG', 'LOG_', 'HOST', 'PORT')):
                    app_logger.info(f"  {key}: {value}")
    
    def get_var(self, key: str, default: Optional[str] = None, required: bool = False) -> Optional[str]:
        """Get an environment variable with validation."""
        value = self.loaded_vars.get(key, default)
        
        if required and not value:
            raise ValueError(f"Required environment variable '{key}' not found")
        
        return value
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get a boolean environment variable."""
        value = self.get_var(key)
        if value is None:
            return default
        
        return value.lower() in ('true', '1', 'yes', 'on')
    
    def get_int(self, key: str, default: Optional[int] = None) -> Optional[int]:
        """Get an integer environment variable."""
        value = self.get_var(key)
        if value is None:
            return default
        
        try:
            return int(value)
        except ValueError:
            app_logger.warning(f"Invalid integer value for {key}: {value}")
            return default
    
    def get_list(self, key: str, separator: str = ',', default: Optional[List[str]] = None) -> List[str]:
        """Get a list environment variable."""
        value = self.get_var(key)
        if value is None:
            return default or []
        
        return [item.strip() for item in value.split(separator) if item.strip()]
    
    def validate_api_key_format(self, key: str, expected_prefix: Optional[str] = None, min_length: int = 20) -> bool:
        """Validate API key format."""
        value = self.get_var(key)
        if not value:
            return False
        
        # Check minimum length
        if len(value) < min_length:
            app_logger.warning(f"API key {key} appears to be too short")
            return False
        
        # Check expected prefix
        if expected_prefix and not value.startswith(expected_prefix):
            app_logger.warning(f"API key {key} doesn't start with expected prefix '{expected_prefix}'")
            return False
        
        # Check for valid characters
        if not re.match(r'^[a-zA-Z0-9_-]+$', value):
            app_logger.warning(f"API key {key} contains invalid characters")
            return False
        
        return True
    
    def create_env_template(self, output_file: str = ".env.example"):
        """Create an environment template file."""
        template_vars = {
            "# Application Settings": "",
            "APP_NAME": "Chat AI",
            "APP_VERSION": "1.0.0",
            "DEBUG": "False",
            "LOG_LEVEL": "INFO",
            "": "",
            "# Server Configuration": "",
            "HOST": "0.0.0.0",
            "PORT": "8000",
            "WORKERS": "4",
            "": "",
            "# API Keys (Required)": "",
            "GROQ_API_KEY": "your_groq_api_key_here",
            "HUGGINGFACE_API_KEY": "your_huggingface_api_key_here",
            "": "",
            "# Database Configuration": "",
            "DATABASE_URL": "sqlite:///./data/chat_ai.db",
            "": "",
            "# Redis Configuration": "",
            "REDIS_URL": "redis://localhost:6379/0",
            "": "",
            "# Security": "",
            "SECRET_KEY": "your-secret-key-here-change-in-production",
            "": "",
            "# Chat Settings": "",
            "MAX_CONVERSATION_LENGTH": "50",
            "MAX_MESSAGE_LENGTH": "2000",
            "SESSION_TIMEOUT": "3600"
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for key, value in template_vars.items():
                    if key.startswith('#'):
                        f.write(f"{key}\n")
                    elif key == "":
                        f.write("\n")
                    else:
                        f.write(f"{key}={value}\n")
            
            app_logger.info(f"Environment template created: {output_file}")
            
        except Exception as e:
            app_logger.error(f"Error creating environment template: {e}")


def load_secure_environment(env_file: str = ".env", required_vars: Optional[List[str]] = None) -> Dict[str, str]:
    """Helper function to load environment securely."""
    loader = SecureEnvLoader(env_file, required_vars)
    return loader.load_environment()


def validate_api_keys_from_env() -> Dict[str, bool]:
    """Helper function to validate API keys from environment."""
    loader = SecureEnvLoader()
    loader.load_environment()
    
    validation_results = {}
    
    # Validate Groq API key
    validation_results['groq'] = loader.validate_api_key_format(
        'GROQ_API_KEY', 
        expected_prefix='gsk_',
        min_length=20
    )
    
    # Validate Hugging Face API key
    validation_results['huggingface'] = loader.validate_api_key_format(
        'HUGGINGFACE_API_KEY',
        expected_prefix='hf_',
        min_length=20
    )
    
    return validation_results


# Global loader instance
env_loader = SecureEnvLoader(required_vars=['GROQ_API_KEY', 'HUGGINGFACE_API_KEY'])
