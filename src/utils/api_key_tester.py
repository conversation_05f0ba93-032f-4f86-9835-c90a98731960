"""API key testing and validation utilities."""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

from config.logging_config import app_logger
from utils.client_factory import ClientFactory, AIProvider
from integrations.base_client import BaseAIClient


class APIKeyTester:
    """Test and validate API keys by making actual API calls."""
    
    def __init__(self):
        self.client_factory = ClientFactory()
        self.test_results: Dict[str, Dict[str, Any]] = {}
    
    async def test_groq_api_key(self, api_key: Optional[str] = None) -> Dict[str, Any]:
        """Test Groq API key by making a simple API call."""
        result = {
            "provider": "groq",
            "success": False,
            "error": None,
            "response_time": None,
            "model_info": None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            start_time = datetime.utcnow()
            
            # Create client
            client = self.client_factory.create_groq_client(api_key)
            if not client:
                result["error"] = "Failed to create Groq client"
                return result
            
            # Test connection
            is_valid = await client.validate_connection()
            if not is_valid:
                result["error"] = "API key validation failed"
                return result
            
            # Test simple generation
            test_conversation = [{"role": "user", "content": "Say 'Hello' in one word."}]
            response = await client.generate_response(test_conversation)
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            if response and len(response.strip()) > 0:
                result.update({
                    "success": True,
                    "response_time": response_time,
                    "model_info": {
                        "model_name": client.model_name,
                        "response_length": len(response),
                        "sample_response": response[:50] + "..." if len(response) > 50 else response
                    }
                })
                app_logger.info(f"Groq API key test successful (response time: {response_time:.2f}s)")
            else:
                result["error"] = "Empty or invalid response from API"
            
        except Exception as e:
            result["error"] = str(e)
            app_logger.error(f"Groq API key test failed: {e}")
        
        return result
    
    async def test_huggingface_api_key(self, api_key: Optional[str] = None) -> Dict[str, Any]:
        """Test Hugging Face API key by making a simple API call."""
        result = {
            "provider": "huggingface",
            "success": False,
            "error": None,
            "response_time": None,
            "model_info": None,
            "local_model_available": False,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            start_time = datetime.utcnow()
            
            # Create client
            client = self.client_factory.create_huggingface_client(api_key)
            if not client:
                result["error"] = "Failed to create Hugging Face client"
                return result
            
            # Check if local model is available
            result["local_model_available"] = hasattr(client, 'use_local') and client.use_local
            
            # Test connection
            is_valid = await client.validate_connection()
            if not is_valid:
                result["error"] = "API key validation failed"
                return result
            
            # Test simple generation
            test_conversation = [{"role": "user", "content": "Hello"}]
            response = await client.generate_response(test_conversation)
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            if response and len(response.strip()) > 0:
                result.update({
                    "success": True,
                    "response_time": response_time,
                    "model_info": {
                        "model_name": client.model_name,
                        "response_length": len(response),
                        "sample_response": response[:50] + "..." if len(response) > 50 else response,
                        "using_local": result["local_model_available"]
                    }
                })
                
                mode = "local model" if result["local_model_available"] else "API"
                app_logger.info(f"Hugging Face test successful using {mode} (response time: {response_time:.2f}s)")
            else:
                result["error"] = "Empty or invalid response"
            
        except Exception as e:
            result["error"] = str(e)
            app_logger.error(f"Hugging Face API key test failed: {e}")
        
        return result
    
    async def test_all_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """Test all available API keys."""
        results = {}
        
        # Test Groq
        app_logger.info("Testing Groq API key...")
        results["groq"] = await self.test_groq_api_key()
        
        # Test Hugging Face
        app_logger.info("Testing Hugging Face API key...")
        results["huggingface"] = await self.test_huggingface_api_key()
        
        # Store results
        self.test_results = results
        
        # Log summary
        self._log_test_summary(results)
        
        return results
    
    def _log_test_summary(self, results: Dict[str, Dict[str, Any]]):
        """Log a summary of test results."""
        app_logger.info("=== API Key Test Summary ===")
        
        for provider, result in results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            response_time = result.get("response_time", 0)
            
            app_logger.info(f"{provider.upper()}: {status}")
            
            if result["success"]:
                app_logger.info(f"  Response time: {response_time:.2f}s")
                if result.get("model_info"):
                    model_info = result["model_info"]
                    app_logger.info(f"  Model: {model_info.get('model_name', 'Unknown')}")
                    if provider == "huggingface" and model_info.get("using_local"):
                        app_logger.info("  Mode: Local model")
            else:
                app_logger.error(f"  Error: {result.get('error', 'Unknown error')}")
        
        successful_tests = sum(1 for r in results.values() if r["success"])
        app_logger.info(f"Tests passed: {successful_tests}/{len(results)}")
    
    async def test_streaming_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Test streaming capabilities of API keys."""
        results = {}
        
        # Test Groq streaming
        app_logger.info("Testing Groq streaming...")
        groq_result = await self._test_provider_streaming("groq")
        results["groq"] = groq_result
        
        # Test Hugging Face streaming
        app_logger.info("Testing Hugging Face streaming...")
        hf_result = await self._test_provider_streaming("huggingface")
        results["huggingface"] = hf_result
        
        return results
    
    async def _test_provider_streaming(self, provider: str) -> Dict[str, Any]:
        """Test streaming for a specific provider."""
        result = {
            "provider": provider,
            "streaming_success": False,
            "chunks_received": 0,
            "total_response": "",
            "error": None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        try:
            # Create client
            client = await self.client_factory.create_client(provider)
            if not client:
                result["error"] = f"Failed to create {provider} client"
                return result
            
            # Test streaming
            test_conversation = [{"role": "user", "content": "Count from 1 to 5, one number per line."}]
            
            chunks = []
            async for chunk in client.stream_response(test_conversation):
                chunks.append(chunk)
                if len(chunks) > 20:  # Prevent infinite loops
                    break
            
            if chunks:
                result.update({
                    "streaming_success": True,
                    "chunks_received": len(chunks),
                    "total_response": "".join(chunks),
                })
                app_logger.info(f"{provider} streaming test successful: {len(chunks)} chunks received")
            else:
                result["error"] = "No chunks received from streaming"
            
        except Exception as e:
            result["error"] = str(e)
            app_logger.error(f"{provider} streaming test failed: {e}")
        
        return result
    
    async def benchmark_response_times(self, num_requests: int = 3) -> Dict[str, Dict[str, Any]]:
        """Benchmark response times for different providers."""
        results = {}
        
        for provider in ["groq", "huggingface"]:
            app_logger.info(f"Benchmarking {provider} response times...")
            
            times = []
            errors = 0
            
            for i in range(num_requests):
                try:
                    start_time = datetime.utcnow()
                    
                    if provider == "groq":
                        test_result = await self.test_groq_api_key()
                    else:
                        test_result = await self.test_huggingface_api_key()
                    
                    if test_result["success"]:
                        response_time = test_result.get("response_time", 0)
                        times.append(response_time)
                    else:
                        errors += 1
                    
                except Exception as e:
                    app_logger.error(f"Benchmark request {i+1} failed for {provider}: {e}")
                    errors += 1
                
                # Small delay between requests
                await asyncio.sleep(1)
            
            if times:
                results[provider] = {
                    "avg_response_time": sum(times) / len(times),
                    "min_response_time": min(times),
                    "max_response_time": max(times),
                    "successful_requests": len(times),
                    "failed_requests": errors,
                    "success_rate": len(times) / num_requests * 100
                }
            else:
                results[provider] = {
                    "error": "All requests failed",
                    "failed_requests": errors
                }
        
        return results
    
    def get_last_test_results(self) -> Dict[str, Dict[str, Any]]:
        """Get the results of the last test run."""
        return self.test_results
    
    def generate_test_report(self) -> str:
        """Generate a formatted test report."""
        if not self.test_results:
            return "No test results available. Run tests first."
        
        report = ["=== API Key Test Report ===", ""]
        
        for provider, result in self.test_results.items():
            report.append(f"Provider: {provider.upper()}")
            report.append(f"Status: {'✅ PASS' if result['success'] else '❌ FAIL'}")
            
            if result["success"]:
                report.append(f"Response Time: {result.get('response_time', 0):.2f}s")
                if result.get("model_info"):
                    model_info = result["model_info"]
                    report.append(f"Model: {model_info.get('model_name', 'Unknown')}")
                    report.append(f"Sample Response: {model_info.get('sample_response', 'N/A')}")
            else:
                report.append(f"Error: {result.get('error', 'Unknown error')}")
            
            report.append("")
        
        return "\n".join(report)


# Helper functions
async def test_all_api_keys() -> Dict[str, Dict[str, Any]]:
    """Helper function to test all API keys."""
    tester = APIKeyTester()
    return await tester.test_all_api_keys()


async def quick_api_test() -> bool:
    """Quick test to check if any API keys are working."""
    try:
        results = await test_all_api_keys()
        return any(result["success"] for result in results.values())
    except Exception as e:
        app_logger.error(f"Quick API test failed: {e}")
        return False
