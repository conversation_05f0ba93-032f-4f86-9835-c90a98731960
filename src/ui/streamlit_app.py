"""Professional Streamlit UI for Chat AI application with dark/light mode and advanced features."""

import streamlit as st
import asyncio
import json
from datetime import datetime
from typing import Optional, List, Dict

from config.settings import get_settings
from config.logging_config import app_logger
from core.response_generator import generate_response, get_available_providers
from utils.simple_chat import SimpleChatBot

settings = get_settings()


class ProfessionalChatApp:
    """Professional Streamlit Chat Application with advanced features."""

    def __init__(self):
        self.setup_page()
        self.initialize_session_state()
    
    def setup_page(self):
        """Setup Streamlit page configuration."""
        st.set_page_config(
            page_title="Chat AI",
            page_icon="🤖",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        .chat-message {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
        }
        .user-message {
            background-color: #e3f2fd;
            margin-left: 2rem;
        }
        .assistant-message {
            background-color: #f5f5f5;
            margin-right: 2rem;
        }
        .sidebar-section {
            margin-bottom: 2rem;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render sidebar with controls."""
        with st.sidebar:
            st.markdown("## ⚙️ Settings")
            
            # Model selection
            available_models = self.chat_engine.get_available_models()
            if available_models:
                selected_model = st.selectbox(
                    "Select AI Model",
                    available_models,
                    index=0 if settings.default_model in available_models else 0
                )
                st.session_state.selected_model = selected_model
            else:
                st.error("No AI models available. Check your API keys.")
                return False
            
            # Session management
            st.markdown("## 💬 Session")
            
            if st.button("New Conversation", type="primary"):
                self.start_new_conversation()
            
            if st.button("Clear History"):
                self.clear_conversation()
            
            # Session info
            if "session_id" in st.session_state:
                st.info(f"Session: {st.session_state.session_id[:8]}...")
            
            # Model info
            st.markdown("## 📊 Model Info")
            st.info(f"Current Model: {st.session_state.get('selected_model', 'None')}")
            
            # Settings
            st.markdown("## 🔧 Advanced")
            st.session_state.stream_response = st.checkbox("Stream Response", value=True)
            st.session_state.show_timestamps = st.checkbox("Show Timestamps", value=False)
            
            return True
    
    def start_new_conversation(self):
        """Start a new conversation."""
        try:
            # Create new session
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            session = loop.run_until_complete(self.chat_engine.create_session())
            loop.close()
            
            st.session_state.session_id = session.session_id
            st.session_state.messages = []
            st.success("New conversation started!")
            st.rerun()
        except Exception as e:
            st.error(f"Error starting new conversation: {e}")
    
    def clear_conversation(self):
        """Clear current conversation."""
        if "session_id" in st.session_state:
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                success = loop.run_until_complete(
                    self.chat_engine.clear_conversation(st.session_state.session_id)
                )
                loop.close()
                
                if success:
                    st.session_state.messages = []
                    st.success("Conversation cleared!")
                    st.rerun()
                else:
                    st.error("Failed to clear conversation")
            except Exception as e:
                st.error(f"Error clearing conversation: {e}")
    
    def render_chat_history(self):
        """Render chat message history."""
        if "messages" not in st.session_state:
            st.session_state.messages = []
        
        # Display messages
        for message in st.session_state.messages:
            with st.container():
                if message["role"] == "user":
                    st.markdown(f"""
                    <div class="chat-message user-message">
                        <strong>You:</strong> {message["content"]}
                        {f'<small>{message.get("timestamp", "")}</small>' if st.session_state.get("show_timestamps") else ''}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="chat-message assistant-message">
                        <strong>AI:</strong> {message["content"]}
                        {f'<small>{message.get("timestamp", "")}</small>' if st.session_state.get("show_timestamps") else ''}
                    </div>
                    """, unsafe_allow_html=True)
    
    def handle_user_input(self):
        """Handle user message input."""
        # Initialize session if needed
        if "session_id" not in st.session_state:
            self.start_new_conversation()
        
        # Chat input
        user_input = st.chat_input("Type your message here...")
        
        if user_input:
            # Add user message to history
            timestamp = datetime.now().strftime("%H:%M:%S")
            st.session_state.messages.append({
                "role": "user",
                "content": user_input,
                "timestamp": timestamp
            })
            
            # Generate AI response
            try:
                with st.spinner("AI is thinking..."):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    response = loop.run_until_complete(
                        self.chat_engine.send_message(
                            st.session_state.session_id,
                            user_input,
                            st.session_state.get("selected_model"),
                            stream=False  # Simplified for Streamlit
                        )
                    )
                    loop.close()
                
                # Add AI response to history
                if response.error:
                    st.error(f"Error: {response.error}")
                else:
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": response.message,
                        "timestamp": response.timestamp.strftime("%H:%M:%S") if response.timestamp else timestamp
                    })
                
                st.rerun()
                
            except Exception as e:
                st.error(f"Error generating response: {e}")
                app_logger.error(f"Streamlit chat error: {e}")
    
    def run(self):
        """Run the Streamlit application."""
        # Header
        st.markdown('<h1 class="main-header">🤖 Chat AI</h1>', unsafe_allow_html=True)
        
        # Render sidebar
        if not self.render_sidebar():
            st.stop()
        
        # Main chat area
        col1, col2 = st.columns([3, 1])
        
        with col1:
            # Chat history
            self.render_chat_history()
            
            # User input
            self.handle_user_input()
        
        with col2:
            st.markdown("## 📈 Stats")
            if "messages" in st.session_state:
                st.metric("Messages", len(st.session_state.messages))
                user_msgs = len([m for m in st.session_state.messages if m["role"] == "user"])
                st.metric("Your Messages", user_msgs)
                st.metric("AI Responses", len(st.session_state.messages) - user_msgs)


def run_streamlit(host: str = "0.0.0.0", port: int = 8501):
    """Run the Streamlit app."""
    import subprocess
    import sys
    
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        __file__,
        "--server.address", host,
        "--server.port", str(port),
        "--server.headless", "true"
    ]
    
    subprocess.run(cmd)


def main():
    """Main function for Streamlit app."""
    app = StreamlitChatApp()
    app.run()


if __name__ == "__main__":
    main()
