{"name": "chat-ai-frontend", "version": "1.0.0", "description": "React frontend for Chat AI application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.4", "web-vitals": "^2.1.4", "socket.io-client": "^4.7.4", "react-markdown": "^8.0.5", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.4", "react-hot-toast": "^2.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/prismjs": "^1.26.0", "@types/react-syntax-highlighter": "^15.5.6", "eslint": "^8.30.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.1"}, "proxy": "http://localhost:8000"}