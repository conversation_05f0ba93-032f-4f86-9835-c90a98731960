"""
Professional Streamlit Chat UI with dark/light mode and advanced features.
"""

import streamlit as st
import asyncio
import json
from datetime import datetime
from typing import List, Dict, Optional

from core.response_generator import generate_response, get_available_providers
from utils.simple_chat import SimpleChatBot


class ProfessionalChatApp:
    """Professional chat application with advanced features."""
    
    def __init__(self):
        self.setup_page_config()
        self.initialize_session_state()
        self.load_custom_css()
    
    def setup_page_config(self):
        """Configure Streamlit page settings."""
        st.set_page_config(
            page_title="AI Chat Pro",
            page_icon="🤖",
            layout="wide",
            initial_sidebar_state="expanded",
            menu_items={
                'Get Help': 'https://github.com/your-repo',
                'Report a bug': 'https://github.com/your-repo/issues',
                'About': "Professional AI Chat Interface"
            }
        )
    
    def initialize_session_state(self):
        """Initialize session state variables."""
        if 'messages' not in st.session_state:
            st.session_state.messages = []
        
        if 'dark_mode' not in st.session_state:
            st.session_state.dark_mode = True
        
        if 'selected_provider' not in st.session_state:
            st.session_state.selected_provider = "auto"
        
        if 'chat_bot' not in st.session_state:
            st.session_state.chat_bot = None
        
        if 'conversation_title' not in st.session_state:
            st.session_state.conversation_title = "New Conversation"
        
        if 'sidebar_function' not in st.session_state:
            st.session_state.sidebar_function = None
    
    def load_custom_css(self):
        """Load custom CSS for professional styling."""
        dark_theme = """
        <style>
        /* Dark Theme Variables */
        :root {
            --bg-primary: #0e1117;
            --bg-secondary: #262730;
            --bg-tertiary: #1e1e2e;
            --text-primary: #fafafa;
            --text-secondary: #a0a0a0;
            --accent-primary: #00d4ff;
            --accent-secondary: #ff6b6b;
            --border-color: #333;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        
        /* Light Theme Variables */
        .light-theme {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent-primary: #007bff;
            --accent-secondary: #dc3545;
            --border-color: #dee2e6;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* Main App Styling */
        .main-header {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            padding: 1rem 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            box-shadow: var(--shadow);
        }
        
        /* Chat Container */
        .chat-container {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 1rem;
            margin: 1rem 0;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }
        
        /* Message Styling */
        .user-message {
            background: linear-gradient(135deg, var(--accent-primary), #0099cc);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 20px 20px 5px 20px;
            margin: 0.5rem 0 0.5rem 2rem;
            box-shadow: var(--shadow);
            position: relative;
        }
        
        .assistant-message {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 1rem 1.5rem;
            border-radius: 20px 20px 20px 5px;
            margin: 0.5rem 2rem 0.5rem 0;
            box-shadow: var(--shadow);
            border-left: 4px solid var(--accent-primary);
        }
        
        /* Sidebar Styling */
        .sidebar-section {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            border: 1px solid var(--border-color);
        }
        
        .function-button {
            background: linear-gradient(135deg, var(--accent-primary), #0099cc);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin: 0.25rem;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .function-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 212, 255, 0.3);
        }
        
        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 1.2rem;
            box-shadow: var(--shadow);
        }
        
        /* Input Styling */
        .stTextInput > div > div > input {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 2px solid var(--border-color);
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
        }
        
        .stTextInput > div > div > input:focus {
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }
        
        /* Metrics Styling */
        .metric-card {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            border: 1px solid var(--border-color);
            margin: 0.5rem 0;
        }
        
        /* Animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .main-header {
                font-size: 1.5rem;
                padding: 0.75rem 1rem;
            }
            
            .user-message, .assistant-message {
                margin-left: 0.5rem;
                margin-right: 0.5rem;
            }
        }
        </style>
        """
        
        light_theme = """
        <style>
        .light-theme {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent-primary: #007bff;
            --accent-secondary: #dc3545;
            --border-color: #dee2e6;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        </style>
        """
        
        if st.session_state.dark_mode:
            st.markdown(dark_theme, unsafe_allow_html=True)
        else:
            st.markdown(dark_theme + light_theme, unsafe_allow_html=True)
            st.markdown('<div class="light-theme">', unsafe_allow_html=True)
    
    def render_header(self):
        """Render the main header."""
        theme_icon = "🌙" if st.session_state.dark_mode else "☀️"
        
        col1, col2, col3 = st.columns([1, 3, 1])
        
        with col1:
            if st.button(theme_icon, help="Toggle theme"):
                st.session_state.dark_mode = not st.session_state.dark_mode
                st.rerun()
        
        with col2:
            st.markdown(
                '<div class="main-header">🤖 AI Chat Pro</div>',
                unsafe_allow_html=True
            )
        
        with col3:
            st.write("")  # Spacer
    
    def render_sidebar(self):
        """Render the professional sidebar."""
        with st.sidebar:
            st.markdown("## ⚙️ Chat Controls")
            
            # Provider Selection
            with st.container():
                st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
                st.subheader("🔧 AI Provider")
                
                providers = ["auto", "groq", "huggingface"]  # Default options
                try:
                    # Try to get actual providers
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    actual_providers = loop.run_until_complete(get_available_providers())
                    loop.close()
                    if actual_providers:
                        providers = ["auto"] + actual_providers
                except:
                    pass
                
                st.session_state.selected_provider = st.selectbox(
                    "Select Provider",
                    providers,
                    index=0
                )
                st.markdown('</div>', unsafe_allow_html=True)
            
            # AI Functions
            st.markdown("## 🚀 AI Functions")
            
            functions = [
                ("📝 Summarize", "summarize", "Summarize the conversation"),
                ("🌐 Translate", "translate", "Translate messages"),
                ("💡 Brainstorm", "brainstorm", "Generate creative ideas"),
                ("📊 Analyze", "analyze", "Analyze conversation tone"),
                ("✨ Improve", "improve", "Improve writing style")
            ]
            
            for icon_name, func_key, description in functions:
                if st.button(
                    icon_name,
                    key=f"btn_{func_key}",
                    help=description,
                    use_container_width=True
                ):
                    st.session_state.sidebar_function = func_key
                    self.execute_ai_function(func_key)
            
            # Conversation Management
            st.markdown("## 💬 Conversation")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🗑️ Clear", use_container_width=True):
                    st.session_state.messages = []
                    st.session_state.conversation_title = "New Conversation"
                    st.rerun()
            
            with col2:
                if st.button("💾 Save", use_container_width=True):
                    self.save_conversation()
            
            # Statistics
            st.markdown("## 📊 Statistics")
            
            total_messages = len(st.session_state.messages)
            user_messages = len([m for m in st.session_state.messages if m["role"] == "user"])
            ai_messages = total_messages - user_messages
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total", total_messages)
            with col2:
                st.metric("AI Responses", ai_messages)
    
    def execute_ai_function(self, function_type: str):
        """Execute AI function based on type."""
        if not st.session_state.messages:
            st.warning("No conversation to process!")
            return
        
        # Get conversation text
        conversation_text = "\n".join([
            f"{msg['role'].title()}: {msg['content']}"
            for msg in st.session_state.messages[-10:]  # Last 10 messages
        ])
        
        prompts = {
            "summarize": f"Please provide a concise summary of this conversation:\n\n{conversation_text}",
            "translate": f"Please translate the last user message to Spanish, French, and German:\n\n{conversation_text}",
            "brainstorm": f"Based on this conversation, generate 5 creative ideas or suggestions:\n\n{conversation_text}",
            "analyze": f"Analyze the tone and sentiment of this conversation:\n\n{conversation_text}",
            "improve": f"Suggest improvements for the writing style in this conversation:\n\n{conversation_text}"
        }
        
        if function_type in prompts:
            # Add function request as a system message
            function_message = {
                "role": "assistant",
                "content": f"🔄 Processing {function_type} request...",
                "function": function_type
            }
            st.session_state.messages.append(function_message)
            
            # Generate AI response
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                response = loop.run_until_complete(
                    generate_response(
                        [{"role": "user", "content": prompts[function_type]}],
                        provider=st.session_state.selected_provider
                    )
                )
                loop.close()
                
                # Update the message with the response
                st.session_state.messages[-1]["content"] = response
                st.rerun()
                
            except Exception as e:
                st.session_state.messages[-1]["content"] = f"❌ Error: {str(e)}"
                st.rerun()
    
    def save_conversation(self):
        """Save conversation to JSON file."""
        if st.session_state.messages:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_{timestamp}.json"
            
            conversation_data = {
                "title": st.session_state.conversation_title,
                "timestamp": timestamp,
                "messages": st.session_state.messages,
                "provider": st.session_state.selected_provider
            }
            
            # In a real app, you'd save to file system or database
            st.success(f"💾 Conversation saved as {filename}")
    
    def render_chat_messages(self):
        """Render chat messages with professional styling."""
        st.markdown('<div class="chat-container">', unsafe_allow_html=True)
        
        for i, message in enumerate(st.session_state.messages):
            role = message["role"]
            content = message["content"]
            
            if role == "user":
                st.markdown(
                    f'<div class="user-message fade-in">'
                    f'<strong>You:</strong><br>{content}'
                    f'</div>',
                    unsafe_allow_html=True
                )
            else:
                # Check if it's a function response
                function_type = message.get("function")
                icon = "🤖"
                if function_type:
                    icons = {
                        "summarize": "📝",
                        "translate": "🌐", 
                        "brainstorm": "💡",
                        "analyze": "📊",
                        "improve": "✨"
                    }
                    icon = icons.get(function_type, "🤖")
                
                st.markdown(
                    f'<div class="assistant-message fade-in">'
                    f'<strong>{icon} AI Assistant:</strong><br>{content}'
                    f'</div>',
                    unsafe_allow_html=True
                )
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    def handle_user_input(self):
        """Handle user message input."""
        # Chat input
        user_input = st.chat_input("Type your message here... 💬")
        
        if user_input:
            # Add user message
            st.session_state.messages.append({
                "role": "user",
                "content": user_input
            })
            
            # Generate title if first message
            if len(st.session_state.messages) == 1:
                st.session_state.conversation_title = user_input[:50] + "..." if len(user_input) > 50 else user_input
            
            # Generate AI response
            with st.spinner("🤖 AI is thinking..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    # Prepare conversation for AI
                    conversation = [
                        {"role": msg["role"], "content": msg["content"]}
                        for msg in st.session_state.messages
                        if msg["role"] in ["user", "assistant"] and not msg.get("function")
                    ]
                    
                    response = loop.run_until_complete(
                        generate_response(
                            conversation,
                            provider=st.session_state.selected_provider
                        )
                    )
                    loop.close()
                    
                    # Add AI response
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": response
                    })
                    
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"❌ Error: {str(e)}")
    
    def run(self):
        """Run the main application."""
        self.render_header()
        
        # Main layout
        col1, col2 = st.columns([3, 1])
        
        with col1:
            # Chat area
            if st.session_state.messages:
                self.render_chat_messages()
            else:
                st.markdown(
                    """
                    <div style="text-align: center; padding: 3rem; color: var(--text-secondary);">
                        <h2>👋 Welcome to AI Chat Pro!</h2>
                        <p>Start a conversation by typing a message below.</p>
                        <p>Use the sidebar functions for advanced AI capabilities.</p>
                    </div>
                    """,
                    unsafe_allow_html=True
                )
            
            # User input
            self.handle_user_input()
        
        with col2:
            # Sidebar
            self.render_sidebar()


def main():
    """Main function for the professional Streamlit app."""
    app = ProfessionalChatApp()
    app.run()


if __name__ == "__main__":
    main()
