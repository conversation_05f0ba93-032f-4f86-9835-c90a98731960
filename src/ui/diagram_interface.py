"""
Streamlit interface for Architecture Diagram Generator.
"""

import streamlit as st
import asyncio
import base64
from datetime import datetime
from pathlib import Path

from core.diagram_generator import DiagramGenerator, generate_architecture_diagram, create_mermaid_diagram
from config.logging_config import app_logger


class DiagramInterface:
    """Streamlit interface for diagram generation."""
    
    def __init__(self):
        self.diagram_generator = DiagramGenerator()
        self.setup_page()
        self.initialize_session_state()
    
    def setup_page(self):
        """Setup Streamlit page configuration."""
        st.set_page_config(
            page_title="Architecture Diagram Generator",
            page_icon="🏗️",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS
        st.markdown("""
        <style>
        .diagram-container {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .component-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .connection-item {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        
        .mermaid-container {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
        }
        
        .stTabs [data-baseweb="tab"] {
            height: 60px;
            padding: 0 24px;
            background-color: #f0f2f6;
            border-radius: 8px 8px 0 0;
        }
        
        .stTabs [aria-selected="true"] {
            background-color: #ffffff;
            border-bottom: 2px solid #1f77b4;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_session_state(self):
        """Initialize session state variables."""
        if 'generated_diagrams' not in st.session_state:
            st.session_state.generated_diagrams = []
        
        if 'diagram_history' not in st.session_state:
            st.session_state.diagram_history = []
    
    def render_header(self):
        """Render the main header."""
        st.markdown("""
        <div style="text-align: center; padding: 2rem 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; margin-bottom: 2rem;">
            <h1 style="color: white; margin: 0; font-size: 2.5rem;">🏗️ Architecture Diagram Generator</h1>
            <p style="color: rgba(255,255,255,0.9); font-size: 1.2rem; margin: 0.5rem 0 0 0;">
                Generate professional architecture diagrams from text descriptions using AI
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_diagram_generator_tab(self):
        """Render the main diagram generation tab."""
        st.header("📝 Generate Architecture Diagram")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Main input
            description = st.text_area(
                "Describe your system architecture:",
                placeholder="""Example: 
Create a microservices architecture for an e-commerce platform with:
- React frontend
- Node.js API gateway
- User service, Product service, Order service
- PostgreSQL database
- Redis cache
- Message queue for async processing
- Load balancer and CDN""",
                height=150,
                help="Provide a detailed description of your system architecture"
            )
            
            # Advanced options
            with st.expander("⚙️ Advanced Options"):
                col_a, col_b, col_c = st.columns(3)
                
                with col_a:
                    diagram_type = st.selectbox(
                        "Diagram Type",
                        ["system", "network", "deployment", "data_flow"],
                        help="Choose the type of architecture diagram"
                    )
                
                with col_b:
                    style = st.selectbox(
                        "Visual Style",
                        ["modern", "minimal", "detailed"],
                        help="Choose the visual style for the diagram"
                    )
                
                with col_c:
                    provider = st.selectbox(
                        "AI Provider",
                        ["auto", "groq", "huggingface"],
                        help="Choose which AI provider to use"
                    )
                
                # Output options
                st.subheader("Output Options")
                col_d, col_e, col_f = st.columns(3)
                
                with col_d:
                    generate_mermaid = st.checkbox("Generate Mermaid Code", value=True)
                
                with col_e:
                    generate_image = st.checkbox("Generate Visual Diagram", value=True)
                
                with col_f:
                    use_ai_image = st.checkbox("Use AI Image Generation", value=False, 
                                             help="Experimental: Use external AI for image generation")
        
        with col2:
            # Quick templates
            st.subheader("🚀 Quick Templates")
            
            templates = {
                "Microservices E-commerce": """
E-commerce platform with microservices:
- React frontend with Next.js
- API Gateway (Kong/Nginx)
- User Management Service
- Product Catalog Service  
- Order Processing Service
- Payment Service
- PostgreSQL databases
- Redis cache
- RabbitMQ message queue
- Docker containers
- Kubernetes orchestration
""",
                "Web Application Stack": """
Modern web application:
- React/Vue.js frontend
- Node.js/Express backend
- REST API endpoints
- MongoDB database
- Redis session store
- CDN for static assets
- Load balancer
- SSL termination
""",
                "Data Pipeline": """
Data processing pipeline:
- Data ingestion layer
- Apache Kafka streaming
- Apache Spark processing
- Data warehouse (Snowflake)
- ETL processes
- Analytics dashboard
- Monitoring and alerting
- Data lake storage
""",
                "Mobile App Backend": """
Mobile application backend:
- Mobile apps (iOS/Android)
- API Gateway
- Authentication service
- Push notification service
- File storage service
- Analytics service
- PostgreSQL database
- Redis cache
- CDN
"""
            }
            
            for template_name, template_desc in templates.items():
                if st.button(template_name, key=f"template_{template_name}", use_container_width=True):
                    st.session_state.template_description = template_desc
        
        # Use template if selected
        if 'template_description' in st.session_state:
            description = st.session_state.template_description
            del st.session_state.template_description
        
        # Generate button
        if st.button("🎯 Generate Diagram", type="primary", disabled=not description, use_container_width=True):
            with st.spinner("🤖 Generating architecture diagram..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    if use_ai_image:
                        # Use AI image generation
                        result = loop.run_until_complete(
                            self.diagram_generator.generate_diagram_with_ai_image(
                                description=description,
                                provider=provider
                            )
                        )
                    else:
                        # Use local generation
                        diagram = loop.run_until_complete(
                            self.diagram_generator.generate_diagram_from_text(
                                description=description,
                                diagram_type=diagram_type,
                                style=style,
                                provider=provider
                            )
                        )
                        result = {
                            "diagram": diagram,
                            "mermaid_code": diagram.mermaid_code,
                            "local_image_path": diagram.generated_image_path
                        }
                    
                    loop.close()
                    
                    # Add to session state
                    st.session_state.generated_diagrams.append({
                        'result': result,
                        'timestamp': datetime.now(),
                        'description': description,
                        'options': {
                            'diagram_type': diagram_type,
                            'style': style,
                            'provider': provider
                        }
                    })
                    
                    st.success("✅ Diagram generated successfully!")
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"❌ Error generating diagram: {str(e)}")
        
        # Display generated diagrams
        if st.session_state.generated_diagrams:
            st.subheader("📊 Generated Diagrams")
            
            for i, item in enumerate(reversed(st.session_state.generated_diagrams)):
                result = item['result']
                timestamp = item['timestamp']
                desc = item['description']
                options = item['options']
                
                with st.expander(f"🏗️ {desc[:60]}... ({timestamp.strftime('%H:%M:%S')})", expanded=(i == 0)):
                    # Display diagram info
                    diagram = result.get('diagram')
                    if diagram:
                        st.markdown(f"**Title:** {diagram.title}")
                        st.markdown(f"**Description:** {diagram.description}")
                        st.markdown(f"**Type:** {options['diagram_type']} | **Style:** {options['style']} | **Provider:** {options['provider']}")
                    
                    # Create tabs for different outputs
                    tab1, tab2, tab3, tab4 = st.tabs(["🖼️ Visual Diagram", "📊 Mermaid", "🔧 Components", "📋 Details"])
                    
                    with tab1:
                        # Display visual diagram
                        local_image = result.get('local_image_path')
                        ai_image = result.get('ai_generated_image')
                        
                        if ai_image and ai_image.get('image_url'):
                            st.markdown("### AI-Generated Diagram")
                            st.image(ai_image['image_url'], caption="AI-Generated Architecture Diagram")
                        
                        if local_image and Path(local_image).exists():
                            st.markdown("### Generated Diagram")
                            st.image(local_image, caption="Programmatically Generated Diagram")
                        
                        if not local_image and not (ai_image and ai_image.get('image_url')):
                            st.info("No visual diagram generated")
                    
                    with tab2:
                        # Display Mermaid diagram
                        mermaid_code = result.get('mermaid_code')
                        if mermaid_code:
                            st.markdown("### Interactive Mermaid Diagram")
                            st.code(mermaid_code, language="mermaid")
                            
                            # Try to render Mermaid (experimental)
                            try:
                                st.markdown(f"""
                                <div class="mermaid-container">
                                    <div class="mermaid">
{mermaid_code}
                                    </div>
                                </div>
                                <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
                                <script>mermaid.initialize({{startOnLoad: true}});</script>
                                """, unsafe_allow_html=True)
                            except:
                                st.info("Mermaid rendering not available in this environment")
                        else:
                            st.info("No Mermaid code generated")
                    
                    with tab3:
                        # Display components
                        if diagram and diagram.components:
                            st.markdown("### Architecture Components")
                            
                            for component in diagram.components:
                                st.markdown(f"""
                                <div class="component-card">
                                    <h4 style="margin: 0 0 10px 0;">{component.name}</h4>
                                    <p style="margin: 0; opacity: 0.9;"><strong>Type:</strong> {component.type}</p>
                                    <p style="margin: 5px 0 0 0; opacity: 0.8;">{component.description}</p>
                                </div>
                                """, unsafe_allow_html=True)
                            
                            # Display connections
                            if diagram.connections:
                                st.markdown("### Component Connections")
                                for connection in diagram.connections:
                                    label = f" - {connection.label}" if connection.label else ""
                                    st.markdown(f"""
                                    <div class="connection-item">
                                        <strong>{connection.from_component}</strong> → <strong>{connection.to_component}</strong>{label}
                                    </div>
                                    """, unsafe_allow_html=True)
                        else:
                            st.info("No component details available")
                    
                    with tab4:
                        # Display raw details
                        st.markdown("### Generation Details")
                        st.json({
                            "timestamp": timestamp.isoformat(),
                            "options": options,
                            "components_count": len(diagram.components) if diagram else 0,
                            "connections_count": len(diagram.connections) if diagram else 0,
                            "has_mermaid": bool(result.get('mermaid_code')),
                            "has_local_image": bool(result.get('local_image_path')),
                            "has_ai_image": bool(result.get('ai_generated_image'))
                        })
                    
                    # Action buttons
                    col_a, col_b, col_c, col_d = st.columns(4)
                    
                    with col_a:
                        if st.button("📋 Copy Mermaid", key=f"copy_mermaid_{i}"):
                            if result.get('mermaid_code'):
                                st.code(result['mermaid_code'])
                                st.success("Mermaid code displayed above!")
                    
                    with col_b:
                        if st.button("💾 Download Image", key=f"download_{i}"):
                            local_image = result.get('local_image_path')
                            if local_image and Path(local_image).exists():
                                with open(local_image, 'rb') as f:
                                    st.download_button(
                                        "📥 Download PNG",
                                        f.read(),
                                        file_name=f"architecture_diagram_{timestamp.strftime('%Y%m%d_%H%M%S')}.png",
                                        mime="image/png",
                                        key=f"download_btn_{i}"
                                    )
                            else:
                                st.error("No image file available")
                    
                    with col_c:
                        if st.button("🔄 Regenerate", key=f"regen_{i}"):
                            st.session_state.regen_description = desc
                            st.rerun()
                    
                    with col_d:
                        if st.button("🗑️ Delete", key=f"delete_{i}"):
                            # Remove from session state
                            actual_index = len(st.session_state.generated_diagrams) - 1 - i
                            del st.session_state.generated_diagrams[actual_index]
                            st.success("Diagram deleted!")
                            st.rerun()
    
    def render_sidebar(self):
        """Render the sidebar with tools and options."""
        with st.sidebar:
            st.header("🛠️ Tools & Settings")
            
            # Statistics
            st.subheader("📊 Statistics")
            st.metric("Generated Diagrams", len(st.session_state.generated_diagrams))
            
            # Clear history
            if st.button("🗑️ Clear All Diagrams"):
                st.session_state.generated_diagrams = []
                st.session_state.diagram_history = []
                st.success("All diagrams cleared!")
                st.rerun()
            
            # Export options
            st.subheader("💾 Export Options")
            
            if st.session_state.generated_diagrams:
                if st.button("📄 Export All Mermaid"):
                    export_data = ""
                    for item in st.session_state.generated_diagrams:
                        result = item['result']
                        timestamp = item['timestamp']
                        mermaid_code = result.get('mermaid_code', '')
                        
                        if mermaid_code:
                            export_data += f"# Generated: {timestamp}\n"
                            export_data += f"# Description: {item['description'][:100]}...\n\n"
                            export_data += mermaid_code + "\n\n"
                            export_data += "=" * 50 + "\n\n"
                    
                    if export_data:
                        st.download_button(
                            "📥 Download All Mermaid",
                            export_data,
                            file_name=f"architecture_diagrams_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                            mime="text/markdown"
                        )
            
            # Help section
            st.subheader("❓ Help & Tips")
            
            with st.expander("💡 Writing Good Descriptions"):
                st.markdown("""
                **Tips for better diagrams:**
                - Be specific about technologies
                - Mention data flow directions
                - Include external dependencies
                - Specify deployment environment
                - Describe user interactions
                
                **Example:**
                "Web app with React frontend, Node.js API, PostgreSQL database, Redis cache, deployed on AWS with load balancer"
                """)
            
            with st.expander("🎨 Diagram Types"):
                st.markdown("""
                - **System**: High-level system overview
                - **Network**: Network topology and connections
                - **Deployment**: Infrastructure and deployment
                - **Data Flow**: Data movement and processing
                """)
    
    def run(self):
        """Run the main application."""
        self.render_header()
        
        # Main content
        self.render_diagram_generator_tab()
        
        # Sidebar
        self.render_sidebar()


def main():
    """Main function for the Diagram Generator interface."""
    app = DiagramInterface()
    app.run()


if __name__ == "__main__":
    main()
