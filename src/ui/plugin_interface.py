"""
Streamlit interface for External Plugin Manager.
"""

import streamlit as st
import asyncio
from datetime import datetime
from typing import Dict, Any

from core.plugin_manager import (
    ExternalPluginManager, 
    process_message_with_plugins,
    search_gifs,
    generate_music,
    get_weather,
    search_photos,
    create_plugin_response
)
from config.logging_config import app_logger


class PluginInterface:
    """Streamlit interface for plugin management and testing."""
    
    def __init__(self):
        self.plugin_manager = ExternalPluginManager()
        self.setup_page()
        self.initialize_session_state()
    
    def setup_page(self):
        """Setup Streamlit page configuration."""
        st.set_page_config(
            page_title="External Plugin Manager",
            page_icon="🔌",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS
        st.markdown("""
        <style>
        .plugin-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .plugin-result {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .keyword-tag {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        
        .status-enabled {
            color: #4caf50;
            font-weight: bold;
        }
        
        .status-disabled {
            color: #f44336;
            font-weight: bold;
        }
        
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
        }
        
        .stTabs [data-baseweb="tab"] {
            height: 60px;
            padding: 0 24px;
            background-color: #f0f2f6;
            border-radius: 8px 8px 0 0;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_session_state(self):
        """Initialize session state variables."""
        if 'plugin_results' not in st.session_state:
            st.session_state.plugin_results = []
        
        if 'plugin_history' not in st.session_state:
            st.session_state.plugin_history = []
    
    def render_header(self):
        """Render the main header."""
        st.markdown("""
        <div style="text-align: center; padding: 2rem 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; margin-bottom: 2rem;">
            <h1 style="color: white; margin: 0; font-size: 2.5rem;">🔌 External Plugin Manager</h1>
            <p style="color: rgba(255,255,255,0.9); font-size: 1.2rem; margin: 0.5rem 0 0 0;">
                Call external APIs and embed multimedia content based on user keywords
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_plugin_overview_tab(self):
        """Render plugin overview and management."""
        st.header("🔧 Available Plugins")
        
        plugins = self.plugin_manager.get_available_plugins()
        
        # Plugin statistics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Plugins", len(plugins))
        
        with col2:
            enabled_count = sum(1 for p in plugins if p["enabled"])
            st.metric("Enabled", enabled_count)
        
        with col3:
            api_key_count = sum(1 for p in plugins if p["api_key_required"])
            st.metric("Require API Key", api_key_count)
        
        with col4:
            content_types = set(p["content_type"] for p in plugins)
            st.metric("Content Types", len(content_types))
        
        # Plugin cards
        st.subheader("📋 Plugin Details")
        
        for plugin in plugins:
            with st.expander(f"🔌 {plugin['name'].title()} - {plugin['description']}", expanded=False):
                col_a, col_b = st.columns([2, 1])
                
                with col_a:
                    st.markdown(f"**Description:** {plugin['description']}")
                    st.markdown(f"**Content Type:** {plugin['content_type']}")
                    st.markdown(f"**API Key Required:** {'Yes' if plugin['api_key_required'] else 'No'}")
                    
                    # Keywords
                    st.markdown("**Keywords:**")
                    keywords_html = ""
                    for keyword in plugin['keywords'][:10]:  # Show first 10 keywords
                        keywords_html += f'<span class="keyword-tag">{keyword}</span>'
                    st.markdown(keywords_html, unsafe_allow_html=True)
                
                with col_b:
                    # Plugin status
                    status = "Enabled" if plugin['enabled'] else "Disabled"
                    status_class = "status-enabled" if plugin['enabled'] else "status-disabled"
                    st.markdown(f"**Status:** <span class='{status_class}'>{status}</span>", unsafe_allow_html=True)
                    
                    # Toggle button
                    if st.button(f"{'Disable' if plugin['enabled'] else 'Enable'}", key=f"toggle_{plugin['name']}"):
                        if plugin['enabled']:
                            self.plugin_manager.disable_plugin(plugin['name'])
                            st.success(f"Disabled {plugin['name']}")
                        else:
                            self.plugin_manager.enable_plugin(plugin['name'])
                            st.success(f"Enabled {plugin['name']}")
                        st.rerun()
                    
                    # Test button
                    if st.button(f"Test Plugin", key=f"test_{plugin['name']}"):
                        st.session_state.test_plugin = plugin['name']
                        st.rerun()
    
    def render_message_processor_tab(self):
        """Render message processing with automatic plugin detection."""
        st.header("💬 Message Processing")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Message input
            message = st.text_area(
                "Enter your message:",
                placeholder="""Examples:
- "Find me some funny cat GIFs"
- "Generate relaxing music"
- "Show me photos of mountains"
- "What's the weather in Tokyo?"
- "Get latest tech news"
- "Tell me a joke"
""",
                height=120
            )
            
            # Processing options
            with st.expander("⚙️ Processing Options"):
                col_a, col_b = st.columns(2)
                
                with col_a:
                    provider = st.selectbox(
                        "AI Provider",
                        ["auto", "groq", "huggingface"],
                        help="Provider for intent analysis"
                    )
                
                with col_b:
                    max_results = st.slider(
                        "Max Results",
                        min_value=1,
                        max_value=10,
                        value=5,
                        help="Maximum number of results from plugins"
                    )
        
        with col2:
            # Quick actions
            st.subheader("⚡ Quick Actions")
            
            quick_actions = {
                "🎭 Random GIF": "show me a funny gif",
                "🎵 Generate Music": "create happy music",
                "📷 Nature Photos": "show beautiful nature photos",
                "🌤️ Weather": "what's the weather like",
                "📰 Tech News": "latest technology news",
                "😄 Random Joke": "tell me a joke",
                "💭 Inspiration": "give me an inspirational quote",
                "🐱 Cat Fact": "tell me a cat fact"
            }
            
            for action_name, action_message in quick_actions.items():
                if st.button(action_name, key=f"quick_{action_name}", use_container_width=True):
                    st.session_state.quick_message = action_message
        
        # Use quick action if selected
        if 'quick_message' in st.session_state:
            message = st.session_state.quick_message
            del st.session_state.quick_message
        
        # Process button
        if st.button("🚀 Process Message", type="primary", disabled=not message, use_container_width=True):
            with st.spinner("🤖 Processing message and detecting plugins..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    result = loop.run_until_complete(
                        process_message_with_plugins(
                            message=message,
                            provider=provider,
                            max_results=max_results
                        )
                    )
                    loop.close()
                    
                    # Add to session state
                    st.session_state.plugin_results.append({
                        'result': result,
                        'timestamp': datetime.now(),
                        'message': message,
                        'options': {
                            'provider': provider,
                            'max_results': max_results
                        }
                    })
                    
                    if result.get('has_plugin_result'):
                        st.success("✅ Plugin executed successfully!")
                    else:
                        st.info("ℹ️ No plugin needed for this message")
                    
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"❌ Error processing message: {str(e)}")
        
        # Display results
        if st.session_state.plugin_results:
            st.subheader("📊 Processing Results")
            
            for i, item in enumerate(reversed(st.session_state.plugin_results)):
                result = item['result']
                timestamp = item['timestamp']
                message = item['message']
                
                with st.expander(f"💬 {message[:50]}... ({timestamp.strftime('%H:%M:%S')})", expanded=(i == 0)):
                    if result.get('has_plugin_result'):
                        plugin_result = result['plugin_result']
                        intent = result['intent']
                        
                        # Show intent detection
                        st.markdown("**🎯 Plugin Intent Detected:**")
                        col_a, col_b, col_c = st.columns(3)
                        
                        with col_a:
                            st.metric("Plugin", intent['plugin_name'])
                        
                        with col_b:
                            st.metric("Confidence", f"{intent['confidence']:.0%}")
                        
                        with col_c:
                            st.metric("Query", intent['search_query'] or "N/A")
                        
                        # Show plugin result
                        if plugin_result.success:
                            st.markdown("**✅ Plugin Result:**")
                            
                            # Display embed HTML if available
                            if plugin_result.embed_html:
                                st.markdown(plugin_result.embed_html, unsafe_allow_html=True)
                            
                            # Show metadata
                            if plugin_result.metadata:
                                with st.expander("📋 Result Metadata"):
                                    st.json(plugin_result.metadata)
                        else:
                            st.error(f"❌ Plugin Error: {plugin_result.error_message}")
                    else:
                        st.info("No plugin was triggered for this message")
                    
                    # Action buttons
                    col_x, col_y, col_z = st.columns(3)
                    
                    with col_x:
                        if st.button("🔄 Retry", key=f"retry_{i}"):
                            st.session_state.retry_message = message
                            st.rerun()
                    
                    with col_y:
                        if st.button("📋 Copy", key=f"copy_{i}"):
                            st.code(message)
                            st.success("Message displayed above!")
                    
                    with col_z:
                        if st.button("🗑️ Delete", key=f"delete_{i}"):
                            actual_index = len(st.session_state.plugin_results) - 1 - i
                            del st.session_state.plugin_results[actual_index]
                            st.success("Result deleted!")
                            st.rerun()
    
    def render_plugin_tester_tab(self):
        """Render individual plugin testing interface."""
        st.header("🧪 Plugin Tester")
        
        # Plugin selection
        plugins = self.plugin_manager.get_available_plugins()
        enabled_plugins = [p for p in plugins if p['enabled']]
        
        if not enabled_plugins:
            st.warning("No enabled plugins available. Please enable plugins in the Overview tab.")
            return
        
        plugin_names = [p['name'] for p in enabled_plugins]
        selected_plugin = st.selectbox("Select Plugin to Test:", plugin_names)
        
        if selected_plugin:
            plugin_info = next(p for p in enabled_plugins if p['name'] == selected_plugin)
            
            # Show plugin info
            st.markdown(f"**Description:** {plugin_info['description']}")
            st.markdown(f"**Content Type:** {plugin_info['content_type']}")
            
            # Plugin-specific inputs
            if selected_plugin in ["giphy", "tenor", "unsplash", "youtube_search", "news"]:
                query = st.text_input("Search Query:", placeholder="Enter search terms")
                max_results = st.slider("Max Results:", 1, 10, 3)
                
                if st.button(f"Test {selected_plugin.title()}", disabled=not query):
                    with st.spinner(f"Testing {selected_plugin}..."):
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            
                            if selected_plugin == "giphy":
                                result = loop.run_until_complete(search_gifs(query, max_results))
                            elif selected_plugin == "unsplash":
                                result = loop.run_until_complete(search_photos(query, max_results))
                            # Add other plugin tests here
                            
                            loop.close()
                            
                            if result.success:
                                st.success("✅ Plugin test successful!")
                                if result.embed_html:
                                    st.markdown(result.embed_html, unsafe_allow_html=True)
                            else:
                                st.error(f"❌ Plugin test failed: {result.error_message}")
                                
                        except Exception as e:
                            st.error(f"❌ Test error: {str(e)}")
            
            elif selected_plugin == "huggingface_audio":
                prompt = st.text_input("Music Prompt:", placeholder="happy upbeat music")
                
                if st.button("Generate Music", disabled=not prompt):
                    with st.spinner("Generating music..."):
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            
                            result = loop.run_until_complete(generate_music(prompt))
                            loop.close()
                            
                            if result.success:
                                st.success("✅ Music generated!")
                                if result.embed_html:
                                    st.markdown(result.embed_html, unsafe_allow_html=True)
                            else:
                                st.error(f"❌ Generation failed: {result.error_message}")
                                
                        except Exception as e:
                            st.error(f"❌ Generation error: {str(e)}")
            
            elif selected_plugin == "weather":
                location = st.text_input("Location:", placeholder="London, UK")
                
                if st.button("Get Weather", disabled=not location):
                    with st.spinner("Getting weather..."):
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            
                            result = loop.run_until_complete(get_weather(location))
                            loop.close()
                            
                            if result.success:
                                st.success("✅ Weather retrieved!")
                                if result.embed_html:
                                    st.markdown(result.embed_html, unsafe_allow_html=True)
                            else:
                                st.error(f"❌ Weather error: {result.error_message}")
                                
                        except Exception as e:
                            st.error(f"❌ Weather error: {str(e)}")
            
            elif selected_plugin in ["cat_facts", "jokes", "quotes"]:
                if st.button(f"Get {selected_plugin.replace('_', ' ').title()}"):
                    with st.spinner(f"Getting {selected_plugin}..."):
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            
                            intent = {
                                "plugin_name": selected_plugin,
                                "plugin_config": self.plugin_manager.plugins[selected_plugin],
                                "search_query": "",
                                "confidence": 1.0
                            }
                            
                            result = loop.run_until_complete(
                                self.plugin_manager.execute_plugin(intent, 1)
                            )
                            loop.close()
                            
                            if result.success:
                                st.success("✅ Content retrieved!")
                                if result.embed_html:
                                    st.markdown(result.embed_html, unsafe_allow_html=True)
                            else:
                                st.error(f"❌ Error: {result.error_message}")
                                
                        except Exception as e:
                            st.error(f"❌ Error: {str(e)}")
    
    def render_sidebar(self):
        """Render the sidebar with tools and settings."""
        with st.sidebar:
            st.header("🛠️ Tools & Settings")
            
            # Plugin status overview
            st.subheader("📊 Plugin Status")
            status = self.plugin_manager.get_plugin_status()
            
            for plugin_name, enabled in status.items():
                status_icon = "✅" if enabled else "❌"
                st.markdown(f"{status_icon} {plugin_name.replace('_', ' ').title()}")
            
            # Clear history
            if st.button("🗑️ Clear All Results"):
                st.session_state.plugin_results = []
                st.session_state.plugin_history = []
                st.success("Results cleared!")
                st.rerun()
            
            # Statistics
            st.subheader("📈 Statistics")
            st.metric("Total Results", len(st.session_state.plugin_results))
            
            # Help
            st.subheader("❓ Help")
            
            with st.expander("💡 Plugin Keywords"):
                st.markdown("""
                **Music/Audio:** music, audio, sound, song, generate music
                **GIFs:** gif, animated, reaction, meme, funny
                **Photos:** photo, image, picture, photography
                **Videos:** video, youtube, watch, tutorial
                **Weather:** weather, temperature, forecast, climate
                **News:** news, article, headlines, breaking news
                **Fun:** joke, funny, quote, inspiration, cat fact
                """)
            
            with st.expander("🔧 API Keys"):
                st.markdown("""
                Configure API keys in your environment:
                - HUGGINGFACE_API_KEY
                - GIPHY_API_KEY  
                - TENOR_API_KEY
                - UNSPLASH_API_KEY
                - YOUTUBE_API_KEY
                - OPENWEATHER_API_KEY
                - NEWS_API_KEY
                """)
    
    def run(self):
        """Run the main application."""
        self.render_header()
        
        # Main tabs
        tab1, tab2, tab3 = st.tabs(["🔧 Plugin Overview", "💬 Message Processor", "🧪 Plugin Tester"])
        
        with tab1:
            self.render_plugin_overview_tab()
        
        with tab2:
            self.render_message_processor_tab()
        
        with tab3:
            self.render_plugin_tester_tab()
        
        # Sidebar
        self.render_sidebar()


def main():
    """Main function for the Plugin Interface."""
    app = PluginInterface()
    app.run()


if __name__ == "__main__":
    main()
