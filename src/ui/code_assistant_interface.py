"""
Streamlit interface for Code Assistant with snippet generation and auto-debug.
"""

import streamlit as st
import asyncio
import traceback
from datetime import datetime
from typing import Optional

from core.code_assistant import CodeAssistant, CodeSnippet, DebugAnalysis
from config.logging_config import app_logger


class CodeAssistantInterface:
    """Streamlit interface for the Code Assistant."""
    
    def __init__(self):
        self.code_assistant = CodeAssistant()
        self.setup_page()
        self.initialize_session_state()
    
    def setup_page(self):
        """Setup Streamlit page configuration."""
        st.set_page_config(
            page_title="AI Code Assistant",
            page_icon="🐍",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS
        st.markdown("""
        <style>
        .code-container {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .error-container {
            background-color: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-container {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .stTabs [data-baseweb="tab-list"] {
            gap: 2px;
        }
        
        .stTabs [data-baseweb="tab"] {
            height: 50px;
            padding-left: 20px;
            padding-right: 20px;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_session_state(self):
        """Initialize session state variables."""
        if 'generated_snippets' not in st.session_state:
            st.session_state.generated_snippets = []
        
        if 'debug_analyses' not in st.session_state:
            st.session_state.debug_analyses = []
        
        if 'code_history' not in st.session_state:
            st.session_state.code_history = []
    
    def render_header(self):
        """Render the main header."""
        st.markdown("""
        <div style="text-align: center; padding: 2rem 0;">
            <h1>🐍 AI Code Assistant</h1>
            <p style="font-size: 1.2rem; color: #666;">
                Generate Python code snippets and debug errors with AI
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_code_generator_tab(self):
        """Render the code generation tab."""
        st.header("📝 Code Generator")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Main input
            description = st.text_area(
                "Describe what you want to code:",
                placeholder="Example: Create a function that reads a CSV file and calculates the average of a specific column",
                height=100
            )
            
            # Advanced options
            with st.expander("⚙️ Advanced Options"):
                col_a, col_b, col_c = st.columns(3)
                
                with col_a:
                    category = st.selectbox(
                        "Category",
                        ["general", "web", "data", "ml", "automation", "api"],
                        help="Choose the category that best fits your code"
                    )
                
                with col_b:
                    complexity = st.selectbox(
                        "Complexity",
                        ["simple", "medium", "complex"],
                        index=1,
                        help="Choose the desired complexity level"
                    )
                
                with col_c:
                    include_tests = st.checkbox(
                        "Include Tests",
                        value=True,
                        help="Generate unit tests for the code"
                    )
                
                provider = st.selectbox(
                    "AI Provider",
                    ["auto", "groq", "huggingface"],
                    help="Choose which AI provider to use"
                )
        
        with col2:
            # Quick templates
            st.subheader("🚀 Quick Templates")
            
            templates = {
                "API Request": "Create a function that makes HTTP requests to a REST API with error handling",
                "File Processing": "Create a function that reads and processes text files",
                "Data Analysis": "Create a function that analyzes data from a CSV file",
                "Web Scraping": "Create a function that scrapes data from a website",
                "Database Query": "Create a function that connects to a database and executes queries"
            }
            
            for template_name, template_desc in templates.items():
                if st.button(template_name, key=f"template_{template_name}"):
                    st.session_state.template_description = template_desc
        
        # Use template if selected
        if 'template_description' in st.session_state:
            description = st.session_state.template_description
            del st.session_state.template_description
        
        # Generate button
        if st.button("🎯 Generate Code", type="primary", disabled=not description):
            with st.spinner("🤖 Generating code..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    snippet = loop.run_until_complete(
                        self.code_assistant.generate_code_snippet(
                            description=description,
                            category=category,
                            complexity=complexity,
                            include_tests=include_tests,
                            provider=provider
                        )
                    )
                    loop.close()
                    
                    # Add to session state
                    st.session_state.generated_snippets.append({
                        'snippet': snippet,
                        'timestamp': datetime.now(),
                        'description': description
                    })
                    
                    st.success("✅ Code generated successfully!")
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"❌ Error generating code: {str(e)}")
        
        # Display generated snippets
        if st.session_state.generated_snippets:
            st.subheader("📋 Generated Code Snippets")
            
            for i, item in enumerate(reversed(st.session_state.generated_snippets)):
                snippet = item['snippet']
                timestamp = item['timestamp']
                desc = item['description']
                
                with st.expander(f"🐍 {desc[:50]}... ({timestamp.strftime('%H:%M:%S')})", expanded=(i == 0)):
                    # Description
                    st.markdown(f"**Description:** {desc}")
                    
                    # Dependencies
                    if snippet.dependencies:
                        st.markdown("**Dependencies:**")
                        for dep in snippet.dependencies:
                            st.code(f"pip install {dep}")
                    
                    # Main code
                    st.markdown("**Generated Code:**")
                    st.code(snippet.code, language="python")
                    
                    # Test code
                    if snippet.test_code:
                        st.markdown("**Test Code:**")
                        st.code(snippet.test_code, language="python")
                    
                    # Action buttons
                    col_a, col_b, col_c = st.columns(3)
                    
                    with col_a:
                        if st.button("📋 Copy Code", key=f"copy_{i}"):
                            st.code(snippet.code)
                            st.success("Code copied to display!")
                    
                    with col_b:
                        if st.button("🧪 Test Code", key=f"test_{i}"):
                            with st.spinner("Testing code..."):
                                result = self.code_assistant.run_code_safely(snippet.code)
                                
                                if result["success"]:
                                    st.success(f"✅ Code executed successfully in {result['execution_time']:.2f}s")
                                    if result["output"]:
                                        st.text("Output:")
                                        st.code(result["output"])
                                else:
                                    st.error("❌ Code execution failed")
                                    st.code(result["error"])
                    
                    with col_c:
                        if st.button("🔧 Debug", key=f"debug_{i}"):
                            # Switch to debug tab with this code
                            st.session_state.debug_code = snippet.code
                            st.session_state.active_tab = "Debug Assistant"
                            st.rerun()
    
    def render_debug_assistant_tab(self):
        """Render the debug assistant tab."""
        st.header("🔍 Debug Assistant")
        
        # Input methods
        input_method = st.radio(
            "How would you like to provide the error?",
            ["Paste Error Log", "Upload Error File", "Run Code and Debug"],
            horizontal=True
        )
        
        error_log = ""
        code_context = ""
        
        if input_method == "Paste Error Log":
            col1, col2 = st.columns(2)
            
            with col1:
                error_log = st.text_area(
                    "Paste your error log or traceback:",
                    height=200,
                    placeholder="""Traceback (most recent call last):
  File "example.py", line 10, in <module>
    result = my_function(data)
  File "example.py", line 5, in my_function
    return data.split()
AttributeError: 'int' object has no attribute 'split'"""
                )
            
            with col2:
                code_context = st.text_area(
                    "Code context (optional):",
                    height=200,
                    placeholder="def my_function(data):\n    return data.split()\n\ndata = 123\nresult = my_function(data)"
                )
        
        elif input_method == "Upload Error File":
            uploaded_file = st.file_uploader(
                "Upload error log file",
                type=['txt', 'log', 'py'],
                help="Upload a text file containing the error log"
            )
            
            if uploaded_file:
                error_log = str(uploaded_file.read(), "utf-8")
                st.text_area("Error log content:", value=error_log, height=200, disabled=True)
        
        elif input_method == "Run Code and Debug":
            code_to_run = st.text_area(
                "Enter Python code to run and debug:",
                height=200,
                value=st.session_state.get('debug_code', ''),
                placeholder="def my_function():\n    # Your code here\n    pass\n\nmy_function()"
            )
            
            if st.button("🏃 Run Code"):
                with st.spinner("Running code..."):
                    result = self.code_assistant.run_code_safely(code_to_run)
                    
                    if result["success"]:
                        st.success(f"✅ Code executed successfully in {result['execution_time']:.2f}s")
                        if result["output"]:
                            st.text("Output:")
                            st.code(result["output"])
                    else:
                        st.error("❌ Code execution failed")
                        error_log = result["error"]
                        code_context = code_to_run
                        st.text("Error log:")
                        st.code(error_log)
        
        # Provider selection
        provider = st.selectbox(
            "AI Provider for Analysis",
            ["auto", "groq", "huggingface"],
            help="Choose which AI provider to use for error analysis"
        )
        
        # Analyze button
        if st.button("🔍 Analyze Error", type="primary", disabled=not error_log):
            with st.spinner("🤖 Analyzing error..."):
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    analysis = loop.run_until_complete(
                        self.code_assistant.analyze_error_log(
                            error_log=error_log,
                            code_context=code_context,
                            provider=provider
                        )
                    )
                    loop.close()
                    
                    # Add to session state
                    st.session_state.debug_analyses.append({
                        'analysis': analysis,
                        'timestamp': datetime.now(),
                        'error_log': error_log,
                        'code_context': code_context
                    })
                    
                    st.success("✅ Error analysis completed!")
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"❌ Error during analysis: {str(e)}")
        
        # Display analyses
        if st.session_state.debug_analyses:
            st.subheader("📊 Debug Analyses")
            
            for i, item in enumerate(reversed(st.session_state.debug_analyses)):
                analysis = item['analysis']
                timestamp = item['timestamp']
                
                with st.expander(f"🔍 {analysis.error_type}: {analysis.error_message[:50]}... ({timestamp.strftime('%H:%M:%S')})", expanded=(i == 0)):
                    # Error info
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Error Type", analysis.error_type)
                    
                    with col2:
                        st.metric("Severity", analysis.severity.title())
                    
                    with col3:
                        st.metric("Confidence", f"{analysis.confidence:.0%}")
                    
                    # Error message
                    st.markdown("**Error Message:**")
                    st.code(analysis.error_message)
                    
                    # Suggested fixes
                    if analysis.suggested_fixes:
                        st.markdown("**🔧 Suggested Fixes:**")
                        for j, fix in enumerate(analysis.suggested_fixes, 1):
                            st.markdown(f"{j}. {fix}")
                    
                    # Code suggestions
                    if analysis.code_suggestions:
                        st.markdown("**💡 Code Suggestions:**")
                        for suggestion in analysis.code_suggestions:
                            st.code(suggestion, language="python")
                    
                    # Related documentation
                    if analysis.related_docs:
                        st.markdown("**📚 Related Documentation:**")
                        for doc in analysis.related_docs:
                            st.markdown(f"- {doc}")
    
    def render_sidebar(self):
        """Render the sidebar with additional tools."""
        with st.sidebar:
            st.header("🛠️ Tools")
            
            # Clear history
            if st.button("🗑️ Clear All History"):
                st.session_state.generated_snippets = []
                st.session_state.debug_analyses = []
                st.session_state.code_history = []
                st.success("History cleared!")
                st.rerun()
            
            # Statistics
            st.subheader("📊 Statistics")
            st.metric("Generated Snippets", len(st.session_state.generated_snippets))
            st.metric("Debug Analyses", len(st.session_state.debug_analyses))
            
            # Quick actions
            st.subheader("⚡ Quick Actions")
            
            if st.button("📖 Python Docs"):
                st.markdown("[Python Documentation](https://docs.python.org/3/)")
            
            if st.button("🐛 Common Errors"):
                st.markdown("""
                **Common Python Errors:**
                - SyntaxError: Check syntax and indentation
                - NameError: Variable not defined
                - TypeError: Wrong data type
                - ImportError: Module not found
                - AttributeError: Attribute doesn't exist
                """)
            
            # Export options
            st.subheader("💾 Export")
            
            if st.button("📄 Export Snippets"):
                if st.session_state.generated_snippets:
                    export_data = ""
                    for item in st.session_state.generated_snippets:
                        snippet = item['snippet']
                        export_data += f"# {item['description']}\n"
                        export_data += f"# Generated: {item['timestamp']}\n\n"
                        export_data += snippet.code + "\n\n"
                        export_data += "=" * 50 + "\n\n"
                    
                    st.download_button(
                        "📥 Download Snippets",
                        export_data,
                        file_name=f"code_snippets_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py",
                        mime="text/plain"
                    )
                else:
                    st.info("No snippets to export")
    
    def run(self):
        """Run the main application."""
        self.render_header()
        
        # Main tabs
        tab1, tab2 = st.tabs(["📝 Code Generator", "🔍 Debug Assistant"])
        
        with tab1:
            self.render_code_generator_tab()
        
        with tab2:
            self.render_debug_assistant_tab()
        
        # Sidebar
        self.render_sidebar()


def main():
    """Main function for the Code Assistant interface."""
    app = CodeAssistantInterface()
    app.run()


if __name__ == "__main__":
    main()
