"""Message processing and validation."""

import re
from typing import List, Dict, Any, Optional
from datetime import datetime

from config.logging_config import app_logger
from config.settings import get_settings
from models.chat_models import ChatMessage

settings = get_settings()


class MessageHandler:
    """Handles message processing, validation, and formatting."""
    
    def __init__(self):
        self.settings = settings
        self._setup_filters()
    
    def _setup_filters(self):
        """Setup content filters and validators."""
        # Basic profanity filter (extend as needed)
        self.profanity_patterns = [
            r'\b(spam|scam|phishing)\b',
            # Add more patterns as needed
        ]
        
        # Compile regex patterns
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE) for pattern in self.profanity_patterns
        ]
    
    def validate_message(self, content: str) -> Dict[str, Any]:
        """Validate message content."""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check length
        if len(content) > self.settings.max_message_length:
            validation_result["is_valid"] = False
            validation_result["errors"].append(
                f"Message too long. Maximum length: {self.settings.max_message_length}"
            )
        
        # Check if empty
        if not content.strip():
            validation_result["is_valid"] = False
            validation_result["errors"].append("Message cannot be empty")
        
        # Check for suspicious content
        for pattern in self.compiled_patterns:
            if pattern.search(content):
                validation_result["warnings"].append("Message contains potentially inappropriate content")
                break
        
        # Check for excessive repetition
        if self._has_excessive_repetition(content):
            validation_result["warnings"].append("Message contains excessive repetition")
        
        return validation_result
    
    def _has_excessive_repetition(self, content: str, threshold: float = 0.7) -> bool:
        """Check if message has excessive character repetition."""
        if len(content) < 10:
            return False
        
        char_counts = {}
        for char in content.lower():
            if char.isalnum():
                char_counts[char] = char_counts.get(char, 0) + 1
        
        if not char_counts:
            return False
        
        max_count = max(char_counts.values())
        repetition_ratio = max_count / len(content)
        
        return repetition_ratio > threshold
    
    def sanitize_message(self, content: str) -> str:
        """Sanitize message content."""
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content.strip())
        
        # Remove potentially harmful characters
        content = re.sub(r'[^\w\s\.,!?;:()\-\'\"@#$%&*+=<>/\\|`~\[\]{}]', '', content)
        
        # Limit consecutive punctuation
        content = re.sub(r'([.!?]){3,}', r'\1\1\1', content)
        
        return content
    
    def format_message_for_ai(self, message: ChatMessage) -> Dict[str, str]:
        """Format message for AI model consumption."""
        return {
            "role": message.role,
            "content": message.content
        }
    
    def format_conversation_for_ai(self, messages: List[ChatMessage]) -> List[Dict[str, str]]:
        """Format conversation history for AI model."""
        return [self.format_message_for_ai(msg) for msg in messages]
    
    def extract_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata from message content."""
        metadata = {
            "word_count": len(content.split()),
            "character_count": len(content),
            "has_questions": bool(re.search(r'\?', content)),
            "has_urls": bool(re.search(r'https?://\S+', content)),
            "has_emails": bool(re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', content)),
            "language_hints": self._detect_language_hints(content),
            "sentiment_indicators": self._extract_sentiment_indicators(content)
        }
        
        return metadata
    
    def _detect_language_hints(self, content: str) -> List[str]:
        """Detect potential language hints in content."""
        hints = []
        
        # Simple language detection based on common words
        language_patterns = {
            "spanish": [r'\b(hola|gracias|por favor|sí|no)\b'],
            "french": [r'\b(bonjour|merci|s\'il vous plaît|oui|non)\b'],
            "german": [r'\b(hallo|danke|bitte|ja|nein)\b'],
            "italian": [r'\b(ciao|grazie|prego|sì|no)\b'],
        }
        
        for language, patterns in language_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    hints.append(language)
                    break
        
        return hints
    
    def _extract_sentiment_indicators(self, content: str) -> Dict[str, int]:
        """Extract basic sentiment indicators."""
        positive_words = [
            "good", "great", "excellent", "amazing", "wonderful", "fantastic",
            "love", "like", "enjoy", "happy", "pleased", "satisfied"
        ]
        
        negative_words = [
            "bad", "terrible", "awful", "horrible", "hate", "dislike",
            "angry", "frustrated", "disappointed", "sad", "upset"
        ]
        
        content_lower = content.lower()
        
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)
        
        return {
            "positive_indicators": positive_count,
            "negative_indicators": negative_count
        }
    
    def create_system_message(self, content: str) -> ChatMessage:
        """Create a system message."""
        return ChatMessage(
            role="system",
            content=content,
            timestamp=datetime.utcnow(),
            metadata={"type": "system"}
        )
    
    def create_user_message(self, content: str, user_id: Optional[str] = None) -> ChatMessage:
        """Create a user message with validation and metadata."""
        # Validate content
        validation = self.validate_message(content)
        if not validation["is_valid"]:
            raise ValueError(f"Invalid message: {', '.join(validation['errors'])}")
        
        # Sanitize content
        sanitized_content = self.sanitize_message(content)
        
        # Extract metadata
        metadata = self.extract_metadata(sanitized_content)
        if user_id:
            metadata["user_id"] = user_id
        
        # Add validation warnings to metadata
        if validation["warnings"]:
            metadata["warnings"] = validation["warnings"]
        
        return ChatMessage(
            role="user",
            content=sanitized_content,
            timestamp=datetime.utcnow(),
            metadata=metadata
        )
    
    def create_assistant_message(self, content: str, model: Optional[str] = None) -> ChatMessage:
        """Create an assistant message."""
        metadata = self.extract_metadata(content)
        if model:
            metadata["model"] = model
        
        return ChatMessage(
            role="assistant",
            content=content,
            timestamp=datetime.utcnow(),
            metadata=metadata
        )
    
    def truncate_conversation(self, messages: List[ChatMessage], max_length: Optional[int] = None) -> List[ChatMessage]:
        """Truncate conversation to fit within limits."""
        max_length = max_length or self.settings.max_conversation_length
        
        if len(messages) <= max_length:
            return messages
        
        # Keep the most recent messages
        truncated = messages[-max_length:]
        
        app_logger.info(f"Truncated conversation from {len(messages)} to {len(truncated)} messages")
        
        return truncated
