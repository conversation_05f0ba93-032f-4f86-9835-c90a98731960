"""
Response generation module with support for multiple AI providers.
"""

import asyncio
from typing import List, Dict, Optional, Union, Any
from enum import Enum

from config.logging_config import app_logger
from config.settings import get_settings
from utils.client_factory import ClientFactory, AIProvider
from integrations.base_client import BaseAIClient


class ResponseProvider(Enum):
    """Available response providers."""
    GROQ = "groq"
    HUGGINGFACE = "huggingface"
    AUTO = "auto"  # Automatically select best available


class ResponseGenerator:
    """Main response generator with multiple AI provider support."""
    
    def __init__(self):
        self.settings = get_settings()
        self.client_factory = ClientFactory()
        self._clients: Dict[str, BaseAIClient] = {}
        self._initialized = False
    
    async def _initialize_clients(self):
        """Initialize AI clients if not already done."""
        if not self._initialized:
            self._clients = await self.client_factory.create_all_clients()
            self._initialized = True
            app_logger.info(f"Initialized {len(self._clients)} AI clients")
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        provider: Union[str, ResponseProvider] = ResponseProvider.AUTO,
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        **kwargs
    ) -> str:
        """
        Generate response from chat history using specified AI provider.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            provider: AI provider to use ('groq', 'huggingface', or 'auto')
            model: Specific model to use (optional)
            temperature: Response randomness (0.0-1.0)
            max_tokens: Maximum response length
            **kwargs: Additional provider-specific parameters
        
        Returns:
            Generated response text
        
        Raises:
            ValueError: If no valid provider is available or messages are invalid
            Exception: If generation fails
        """
        try:
            # Validate input
            if not messages or not isinstance(messages, list):
                raise ValueError("Messages must be a non-empty list")
            
            self._validate_messages(messages)
            
            # Initialize clients if needed
            await self._initialize_clients()
            
            if not self._clients:
                raise ValueError("No AI clients available. Check your API keys.")
            
            # Select provider
            selected_provider = self._select_provider(provider)
            if not selected_provider:
                raise ValueError(f"Provider '{provider}' not available")
            
            # Get client
            client = self._clients[selected_provider]
            
            # Prepare messages for the specific provider
            formatted_messages = self._format_messages_for_provider(
                messages, selected_provider
            )
            
            # Generate response
            app_logger.info(f"Generating response using {selected_provider}")
            response = await self._generate_with_client(
                client, formatted_messages, temperature, max_tokens, **kwargs
            )
            
            if not response or not response.strip():
                raise Exception("Empty response from AI provider")
            
            app_logger.info(f"Generated response: {len(response)} characters")
            return response.strip()
            
        except Exception as e:
            app_logger.error(f"Response generation failed: {e}")
            raise
    
    def _validate_messages(self, messages: List[Dict[str, str]]):
        """Validate message format."""
        for i, message in enumerate(messages):
            if not isinstance(message, dict):
                raise ValueError(f"Message {i} must be a dictionary")
            
            if 'role' not in message or 'content' not in message:
                raise ValueError(f"Message {i} must have 'role' and 'content' keys")
            
            if message['role'] not in ['user', 'assistant', 'system']:
                raise ValueError(f"Message {i} role must be 'user', 'assistant', or 'system'")
            
            if not isinstance(message['content'], str) or not message['content'].strip():
                raise ValueError(f"Message {i} content must be a non-empty string")
    
    def _select_provider(self, provider: Union[str, ResponseProvider]) -> Optional[str]:
        """Select the best available provider."""
        if isinstance(provider, ResponseProvider):
            provider = provider.value
        
        if provider == "auto":
            # Auto-select based on availability and preference
            if "groq" in self._clients:
                return "groq"  # Prefer Groq for speed
            elif "huggingface" in self._clients:
                return "huggingface"
            else:
                return None
        else:
            # Use specific provider if available
            return provider if provider in self._clients else None
    
    def _format_messages_for_provider(
        self, 
        messages: List[Dict[str, str]], 
        provider: str
    ) -> List[Dict[str, str]]:
        """Format messages for specific provider requirements."""
        # Most providers use the same format, but we can customize here
        formatted = []
        
        for message in messages:
            formatted_message = {
                "role": message["role"],
                "content": message["content"]
            }
            formatted.append(formatted_message)
        
        # Provider-specific formatting
        if provider == "huggingface":
            # Some HF models might need special formatting
            # This is handled in the HF client itself
            pass
        elif provider == "groq":
            # Groq uses standard OpenAI format
            pass
        
        return formatted
    
    async def _generate_with_client(
        self,
        client: BaseAIClient,
        messages: List[Dict[str, str]],
        temperature: float,
        max_tokens: int,
        **kwargs
    ) -> str:
        """Generate response using specific client."""
        try:
            # For now, use the client's generate_response method
            # In the future, we can pass temperature and max_tokens to the client
            response = await client.generate_response(messages)
            return response
        except Exception as e:
            app_logger.error(f"Client generation failed: {e}")
            raise
    
    async def generate_streaming_response(
        self,
        messages: List[Dict[str, str]],
        provider: Union[str, ResponseProvider] = ResponseProvider.AUTO,
        **kwargs
    ):
        """
        Generate streaming response from chat history.
        
        Args:
            messages: List of message dictionaries
            provider: AI provider to use
            **kwargs: Additional parameters
        
        Yields:
            Response chunks as they are generated
        """
        try:
            # Validate and initialize
            self._validate_messages(messages)
            await self._initialize_clients()
            
            if not self._clients:
                raise ValueError("No AI clients available")
            
            # Select provider
            selected_provider = self._select_provider(provider)
            if not selected_provider:
                raise ValueError(f"Provider '{provider}' not available")
            
            client = self._clients[selected_provider]
            formatted_messages = self._format_messages_for_provider(
                messages, selected_provider
            )
            
            app_logger.info(f"Starting streaming response with {selected_provider}")
            
            # Stream response
            async for chunk in client.stream_response(formatted_messages):
                yield chunk
                
        except Exception as e:
            app_logger.error(f"Streaming generation failed: {e}")
            yield f"Error: {str(e)}"
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers."""
        return list(self._clients.keys())
    
    async def validate_provider(self, provider: str) -> bool:
        """Validate that a provider is working."""
        await self._initialize_clients()
        
        if provider not in self._clients:
            return False
        
        try:
            client = self._clients[provider]
            return await client.validate_connection()
        except Exception:
            return False


# Global response generator instance
_response_generator = None


async def get_response_generator() -> ResponseGenerator:
    """Get or create global response generator instance."""
    global _response_generator
    if _response_generator is None:
        _response_generator = ResponseGenerator()
    return _response_generator


# Main function as requested
async def generate_response(
    messages: List[Dict[str, str]],
    provider: str = "auto",
    model: Optional[str] = None,
    temperature: float = 0.7,
    max_tokens: int = 1024,
    **kwargs
) -> str:
    """
    Generate response from chat history using specified AI provider.
    
    Args:
        messages: List of message dictionaries with 'role' and 'content' keys
                 Example: [{"role": "user", "content": "Hello!"}]
        provider: AI provider to use ('groq', 'huggingface', or 'auto')
        model: Specific model to use (optional)
        temperature: Response randomness (0.0-1.0)
        max_tokens: Maximum response length
        **kwargs: Additional provider-specific parameters
    
    Returns:
        Generated response text
    
    Example:
        >>> messages = [
        ...     {"role": "user", "content": "What is Python?"},
        ...     {"role": "assistant", "content": "Python is a programming language."},
        ...     {"role": "user", "content": "Tell me more about it."}
        ... ]
        >>> response = await generate_response(messages, provider="groq")
        >>> print(response)
    """
    generator = await get_response_generator()
    return await generator.generate_response(
        messages=messages,
        provider=provider,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        **kwargs
    )


# Convenience functions for specific providers
async def generate_groq_response(
    messages: List[Dict[str, str]],
    temperature: float = 0.7,
    max_tokens: int = 1024,
    **kwargs
) -> str:
    """Generate response using Groq API."""
    return await generate_response(
        messages=messages,
        provider="groq",
        temperature=temperature,
        max_tokens=max_tokens,
        **kwargs
    )


async def generate_huggingface_response(
    messages: List[Dict[str, str]],
    temperature: float = 0.7,
    max_tokens: int = 1024,
    **kwargs
) -> str:
    """Generate response using Hugging Face."""
    return await generate_response(
        messages=messages,
        provider="huggingface",
        temperature=temperature,
        max_tokens=max_tokens,
        **kwargs
    )


# Streaming versions
async def generate_streaming_response(
    messages: List[Dict[str, str]],
    provider: str = "auto",
    **kwargs
):
    """
    Generate streaming response from chat history.
    
    Args:
        messages: List of message dictionaries
        provider: AI provider to use
        **kwargs: Additional parameters
    
    Yields:
        Response chunks as they are generated
    
    Example:
        >>> async for chunk in generate_streaming_response(messages):
        ...     print(chunk, end="", flush=True)
    """
    generator = await get_response_generator()
    async for chunk in generator.generate_streaming_response(
        messages=messages,
        provider=provider,
        **kwargs
    ):
        yield chunk


# Utility functions
async def get_available_providers() -> List[str]:
    """Get list of available AI providers."""
    generator = await get_response_generator()
    await generator._initialize_clients()
    return generator.get_available_providers()


async def validate_provider(provider: str) -> bool:
    """Check if a provider is available and working."""
    generator = await get_response_generator()
    return await generator.validate_provider(provider)
