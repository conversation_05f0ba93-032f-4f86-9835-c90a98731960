"""
Enhanced chat system with diagram generation capabilities.
"""

import re
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from core.response_generator import generate_response
from core.diagram_generator import DiagramGenerator, generate_architecture_diagram
from config.logging_config import app_logger


class DiagramChatAssistant:
    """Chat assistant with diagram generation capabilities."""
    
    def __init__(self):
        self.diagram_generator = DiagramGenerator()
        self.diagram_keywords = [
            "architecture", "diagram", "design", "system", "structure",
            "flow", "topology", "deployment", "infrastructure", "components",
            "microservices", "database", "api", "frontend", "backend"
        ]
    
    async def process_message(
        self,
        message: str,
        conversation_history: List[Dict[str, str]] = None,
        provider: str = "auto"
    ) -> Dict[str, Any]:
        """
        Process user message and determine if diagram generation is needed.
        
        Args:
            message: User message
            conversation_history: Previous conversation
            provider: AI provider to use
        
        Returns:
            Dictionary with response and optional diagram data
        """
        try:
            # Check if message requests diagram generation
            needs_diagram = self._should_generate_diagram(message)
            
            if needs_diagram:
                return await self._handle_diagram_request(message, conversation_history, provider)
            else:
                return await self._handle_regular_message(message, conversation_history, provider)
                
        except Exception as e:
            app_logger.error(f"Error processing message: {e}")
            return {
                "response": f"Sorry, I encountered an error: {str(e)}",
                "has_diagram": False,
                "error": str(e)
            }
    
    def _should_generate_diagram(self, message: str) -> bool:
        """Determine if the message requests diagram generation."""
        message_lower = message.lower()
        
        # Direct diagram requests
        direct_requests = [
            "create diagram", "generate diagram", "draw diagram",
            "show architecture", "design system", "create architecture",
            "visualize", "diagram of", "architecture for"
        ]
        
        for request in direct_requests:
            if request in message_lower:
                return True
        
        # Check for architecture-related keywords
        keyword_count = sum(1 for keyword in self.diagram_keywords if keyword in message_lower)
        
        # If multiple architecture keywords and mentions system/design
        if keyword_count >= 2 and any(word in message_lower for word in ["system", "design", "build", "create"]):
            return True
        
        return False
    
    async def _handle_diagram_request(
        self,
        message: str,
        conversation_history: List[Dict[str, str]],
        provider: str
    ) -> Dict[str, Any]:
        """Handle message that requests diagram generation."""
        
        # Extract architecture description from message
        architecture_description = self._extract_architecture_description(message, conversation_history)
        
        # Generate diagram
        app_logger.info(f"Generating diagram for: {architecture_description[:100]}...")
        
        try:
            diagram_result = await self.diagram_generator.generate_diagram_with_ai_image(
                description=architecture_description,
                provider=provider
            )
            
            # Generate text response
            text_response = await self._generate_diagram_explanation(
                architecture_description, diagram_result, provider
            )
            
            # Create formatted response with diagram
            formatted_response = self._format_diagram_response(text_response, diagram_result)
            
            return {
                "response": formatted_response,
                "has_diagram": True,
                "diagram_result": diagram_result,
                "architecture_description": architecture_description,
                "diagram_type": "architecture"
            }
            
        except Exception as e:
            app_logger.error(f"Diagram generation failed: {e}")
            
            # Fallback to text-only response
            fallback_response = await generate_response(
                [{"role": "user", "content": message}],
                provider=provider
            )
            
            return {
                "response": fallback_response + "\n\n*Note: Diagram generation failed, but I can still help with architecture advice.*",
                "has_diagram": False,
                "error": str(e)
            }
    
    async def _handle_regular_message(
        self,
        message: str,
        conversation_history: List[Dict[str, str]],
        provider: str
    ) -> Dict[str, Any]:
        """Handle regular chat message."""
        
        # Prepare conversation for AI
        messages = conversation_history or []
        messages.append({"role": "user", "content": message})
        
        # Generate response
        response = await generate_response(messages, provider=provider)
        
        return {
            "response": response,
            "has_diagram": False
        }
    
    def _extract_architecture_description(
        self,
        message: str,
        conversation_history: List[Dict[str, str]]
    ) -> str:
        """Extract architecture description from message and context."""
        
        # Start with the current message
        description = message
        
        # Add relevant context from conversation history
        if conversation_history:
            # Look for recent architecture-related messages
            relevant_context = []
            
            for msg in conversation_history[-5:]:  # Last 5 messages
                content = msg.get("content", "")
                if any(keyword in content.lower() for keyword in self.diagram_keywords):
                    relevant_context.append(content)
            
            if relevant_context:
                context = " ".join(relevant_context)
                description = f"{context}\n\n{message}"
        
        return description
    
    async def _generate_diagram_explanation(
        self,
        architecture_description: str,
        diagram_result: Dict[str, Any],
        provider: str
    ) -> str:
        """Generate explanation text for the diagram."""
        
        diagram = diagram_result.get("diagram")
        
        explanation_prompt = f"""
Based on this architecture description and the generated diagram, provide a clear explanation of the system:

Architecture Description:
{architecture_description}

Generated Components:
{[comp.name + " (" + comp.type + ")" for comp in diagram.components] if diagram else "None"}

Please explain:
1. The overall system architecture
2. Key components and their roles
3. How data flows through the system
4. Benefits of this architecture
5. Potential considerations or improvements

Keep the explanation clear and professional.
"""
        
        explanation = await generate_response(
            [{"role": "user", "content": explanation_prompt}],
            provider=provider,
            temperature=0.4
        )
        
        return explanation
    
    def _format_diagram_response(self, text_response: str, diagram_result: Dict[str, Any]) -> str:
        """Format response with diagram and text."""
        
        diagram = diagram_result.get("diagram")
        mermaid_code = diagram_result.get("mermaid_code")
        local_image = diagram_result.get("local_image_path")
        ai_image = diagram_result.get("ai_generated_image")
        
        # Start with the text explanation
        formatted_response = text_response
        
        # Add diagram title
        if diagram:
            formatted_response += f"\n\n## 🏗️ {diagram.title}\n"
            formatted_response += f"{diagram.description}\n"
        
        # Add AI-generated image if available
        if ai_image and ai_image.get("image_url"):
            formatted_response += f"\n### 🎨 AI-Generated Diagram\n"
            formatted_response += f"![Architecture Diagram]({ai_image['image_url']})\n"
            formatted_response += f"*Generated using AI image generation*\n"
        
        # Add local image
        if local_image:
            formatted_response += f"\n### 📊 Generated Diagram\n"
            formatted_response += f"*Diagram saved to: {local_image}*\n"
        
        # Add interactive Mermaid diagram
        if mermaid_code:
            formatted_response += f"\n### 🔄 Interactive Diagram (Mermaid)\n"
            formatted_response += f"```mermaid\n{mermaid_code}\n```\n"
        
        # Add component summary
        if diagram and diagram.components:
            formatted_response += f"\n### 🔧 System Components\n"
            for component in diagram.components:
                formatted_response += f"- **{component.name}** ({component.type}): {component.description}\n"
        
        # Add connections
        if diagram and diagram.connections:
            formatted_response += f"\n### 🔗 Component Connections\n"
            for connection in diagram.connections:
                label = f" - {connection.label}" if connection.label else ""
                formatted_response += f"- {connection.from_component} → {connection.to_component}{label}\n"
        
        return formatted_response
    
    def create_chat_image_html(self, image_path: str, title: str = "") -> str:
        """Create HTML for displaying image in chat interface."""
        return self.diagram_generator.create_image_html(image_path, title)
    
    def create_chat_mermaid_html(self, mermaid_code: str, title: str = "") -> str:
        """Create HTML for displaying Mermaid diagram in chat."""
        return self.diagram_generator.create_mermaid_html(mermaid_code, title)


# Helper functions for easy integration
async def process_chat_message_with_diagrams(
    message: str,
    conversation_history: List[Dict[str, str]] = None,
    provider: str = "auto"
) -> Dict[str, Any]:
    """
    Process chat message with automatic diagram generation.
    
    Args:
        message: User message
        conversation_history: Previous conversation
        provider: AI provider to use
    
    Returns:
        Dictionary with response and optional diagram data
    """
    assistant = DiagramChatAssistant()
    return await assistant.process_message(message, conversation_history, provider)


async def generate_diagram_from_chat(
    description: str,
    provider: str = "auto"
) -> str:
    """
    Generate diagram from chat description and return formatted response.
    
    Args:
        description: Architecture description
        provider: AI provider to use
    
    Returns:
        Formatted response with diagram
    """
    return await generate_architecture_diagram(description, provider=provider)


# Chat integration prompts
DIAGRAM_SYSTEM_PROMPT = """
You are an expert system architect and chat assistant. You can help users with:

1. **Architecture Design**: Design system architectures and create diagrams
2. **Technical Advice**: Provide technical guidance and best practices
3. **Diagram Generation**: Create visual diagrams from text descriptions

When users ask about system architecture, design, or request diagrams, you should:
- Analyze their requirements thoroughly
- Suggest appropriate architectural patterns
- Generate visual diagrams when helpful
- Explain the benefits and trade-offs

You can generate diagrams for:
- System architecture
- Network topology
- Deployment diagrams
- Data flow diagrams
- Microservices architecture
- Database design

Always provide clear explanations along with any diagrams you generate.
"""


def create_diagram_chat_prompt(user_message: str, conversation_context: str = "") -> str:
    """Create enhanced prompt for diagram-aware chat."""
    
    prompt = f"""
{DIAGRAM_SYSTEM_PROMPT}

Conversation Context:
{conversation_context}

User Message:
{user_message}

If the user is asking about system architecture or requesting a diagram, provide:
1. A clear explanation of the architecture
2. Recommendations for technologies and patterns
3. A detailed description suitable for diagram generation

If generating a diagram would be helpful, indicate this in your response.
"""
    
    return prompt


# Example usage functions
async def example_diagram_chat():
    """Example of using diagram chat assistant."""
    assistant = DiagramChatAssistant()
    
    # Example conversation
    messages = [
        "I need to design a microservices architecture for an e-commerce platform",
        "Create a diagram showing the system architecture",
        "How would you handle user authentication in this system?"
    ]
    
    conversation = []
    
    for message in messages:
        print(f"\nUser: {message}")
        
        result = await assistant.process_message(message, conversation, "auto")
        
        print(f"Assistant: {result['response'][:200]}...")
        
        if result.get('has_diagram'):
            print("📊 Diagram generated!")
            diagram_result = result.get('diagram_result')
            if diagram_result:
                diagram = diagram_result.get('diagram')
                if diagram:
                    print(f"  Title: {diagram.title}")
                    print(f"  Components: {len(diagram.components)}")
                    print(f"  Connections: {len(diagram.connections)}")
        
        # Add to conversation history
        conversation.append({"role": "user", "content": message})
        conversation.append({"role": "assistant", "content": result['response']})


if __name__ == "__main__":
    asyncio.run(example_diagram_chat())
