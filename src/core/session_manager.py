"""Session management for chat conversations."""

import json
import asyncio
from typing import Dict, Optional, List
from pathlib import Path
from datetime import datetime, timedelta

from config.logging_config import app_logger
from config.settings import get_settings
from models.chat_models import ChatSession

settings = get_settings()


class SessionManager:
    """Manages chat sessions and persistence."""
    
    def __init__(self):
        self.sessions: Dict[str, ChatSession] = {}
        self.storage_path = Path("data/conversations")
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start background task for session cleanup."""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def _periodic_cleanup(self):
        """Periodically clean up expired sessions."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_expired_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                app_logger.error(f"Error in session cleanup: {e}")
    
    async def _cleanup_expired_sessions(self):
        """Remove expired sessions from memory."""
        cutoff_time = datetime.utcnow() - timedelta(seconds=settings.session_timeout)
        expired_sessions = [
            session_id for session_id, session in self.sessions.items()
            if session.updated_at < cutoff_time
        ]
        
        for session_id in expired_sessions:
            await self._persist_session(self.sessions[session_id])
            del self.sessions[session_id]
            app_logger.info(f"Cleaned up expired session: {session_id}")
    
    async def save_session(self, session: ChatSession) -> bool:
        """Save a session to memory and optionally persist to disk."""
        try:
            session.updated_at = datetime.utcnow()
            self.sessions[session.session_id] = session
            
            # Persist to disk for important sessions
            if len(session.messages) > 0:
                await self._persist_session(session)
            
            return True
        except Exception as e:
            app_logger.error(f"Error saving session {session.session_id}: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a session from memory or load from disk."""
        # Check memory first
        if session_id in self.sessions:
            return self.sessions[session_id]
        
        # Try to load from disk
        session = await self._load_session(session_id)
        if session:
            self.sessions[session_id] = session
            return session
        
        return None
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session from memory and disk."""
        try:
            # Remove from memory
            if session_id in self.sessions:
                del self.sessions[session_id]
            
            # Remove from disk
            session_file = self.storage_path / f"{session_id}.json"
            if session_file.exists():
                session_file.unlink()
            
            app_logger.info(f"Deleted session: {session_id}")
            return True
        except Exception as e:
            app_logger.error(f"Error deleting session {session_id}: {e}")
            return False
    
    async def list_sessions(self, user_id: Optional[str] = None) -> List[ChatSession]:
        """List all sessions, optionally filtered by user."""
        sessions = []
        
        # Get from memory
        for session in self.sessions.values():
            if user_id is None or session.user_id == user_id:
                sessions.append(session)
        
        # Load from disk if needed
        for session_file in self.storage_path.glob("*.json"):
            session_id = session_file.stem
            if session_id not in self.sessions:
                session = await self._load_session(session_id)
                if session and (user_id is None or session.user_id == user_id):
                    sessions.append(session)
        
        return sorted(sessions, key=lambda s: s.updated_at, reverse=True)
    
    async def _persist_session(self, session: ChatSession) -> bool:
        """Persist a session to disk."""
        try:
            session_file = self.storage_path / f"{session.session_id}.json"
            session_data = session.dict()
            
            # Convert datetime objects to ISO format
            session_data["created_at"] = session.created_at.isoformat()
            session_data["updated_at"] = session.updated_at.isoformat()
            
            for message in session_data["messages"]:
                message["timestamp"] = datetime.fromisoformat(
                    message["timestamp"].replace("Z", "+00:00")
                ).isoformat()
            
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            app_logger.error(f"Error persisting session {session.session_id}: {e}")
            return False
    
    async def _load_session(self, session_id: str) -> Optional[ChatSession]:
        """Load a session from disk."""
        try:
            session_file = self.storage_path / f"{session_id}.json"
            if not session_file.exists():
                return None
            
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            # Convert ISO format back to datetime objects
            session_data["created_at"] = datetime.fromisoformat(session_data["created_at"])
            session_data["updated_at"] = datetime.fromisoformat(session_data["updated_at"])
            
            for message in session_data["messages"]:
                message["timestamp"] = datetime.fromisoformat(message["timestamp"])
            
            return ChatSession(**session_data)
        except Exception as e:
            app_logger.error(f"Error loading session {session_id}: {e}")
            return None
    
    async def get_session_stats(self) -> Dict[str, int]:
        """Get statistics about sessions."""
        total_sessions = len(self.sessions)
        total_messages = sum(len(session.messages) for session in self.sessions.values())
        active_sessions = sum(1 for session in self.sessions.values() if session.is_active)
        
        # Count persisted sessions
        persisted_sessions = len(list(self.storage_path.glob("*.json")))
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_messages": total_messages,
            "persisted_sessions": persisted_sessions
        }
    
    async def cleanup(self):
        """Cleanup resources."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Persist all sessions before shutdown
        for session in self.sessions.values():
            await self._persist_session(session)
        
        app_logger.info("Session manager cleanup completed")
