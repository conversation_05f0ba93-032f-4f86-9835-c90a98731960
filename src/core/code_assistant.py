"""
Advanced Code Assistant with snippet generation and auto-debug capabilities.
"""

import re
import ast
import traceback
import subprocess
import sys
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass

from core.response_generator import generate_response
from config.logging_config import app_logger


@dataclass
class CodeSnippet:
    """Represents a generated code snippet."""
    code: str
    description: str
    language: str = "python"
    dependencies: List[str] = None
    complexity: str = "medium"  # simple, medium, complex
    category: str = "general"  # web, data, ml, automation, etc.
    test_code: Optional[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class DebugAnalysis:
    """Represents debug analysis results."""
    error_type: str
    error_message: str
    suggested_fixes: List[str]
    code_suggestions: List[str]
    severity: str  # low, medium, high, critical
    confidence: float  # 0.0 to 1.0
    related_docs: List[str] = None
    
    def __post_init__(self):
        if self.related_docs is None:
            self.related_docs = []


class CodeAssistant:
    """Advanced code assistant with generation and debugging capabilities."""
    
    def __init__(self):
        self.code_templates = self._load_code_templates()
        self.error_patterns = self._load_error_patterns()
        self.common_fixes = self._load_common_fixes()
    
    def _load_code_templates(self) -> Dict[str, str]:
        """Load common code templates."""
        return {
            "function": """def {function_name}({parameters}):
    \"\"\"
    {description}
    
    Args:
        {args_doc}
    
    Returns:
        {return_doc}
    \"\"\"
    {body}
    return {return_value}""",
            
            "class": """class {class_name}:
    \"\"\"
    {description}
    \"\"\"
    
    def __init__(self{init_params}):
        {init_body}
    
    {methods}""",
            
            "api_request": """import requests
import json

def {function_name}(url, data=None, headers=None):
    \"\"\"
    {description}
    \"\"\"
    try:
        if data:
            response = requests.post(url, json=data, headers=headers)
        else:
            response = requests.get(url, headers=headers)
        
        response.raise_for_status()
        return response.json()
    
    except requests.exceptions.RequestException as e:
        print(f"API request failed: {{e}}")
        return None""",
            
            "file_processing": """import os
from pathlib import Path

def {function_name}(file_path):
    \"\"\"
    {description}
    \"\"\"
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            {processing_code}
        
        return result
    
    except FileNotFoundError:
        print(f"File not found: {{file_path}}")
        return None
    except Exception as e:
        print(f"Error processing file: {{e}}")
        return None""",
            
            "data_analysis": """import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def {function_name}(data_path):
    \"\"\"
    {description}
    \"\"\"
    # Load data
    df = pd.read_csv(data_path)
    
    # Data exploration
    print("Data shape:", df.shape)
    print("\\nData info:")
    print(df.info())
    
    # Analysis
    {analysis_code}
    
    return df""",
        }
    
    def _load_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load common error patterns and their solutions."""
        return {
            "SyntaxError": {
                "patterns": [
                    r"invalid syntax",
                    r"unexpected EOF",
                    r"unmatched",
                    r"invalid character"
                ],
                "common_causes": [
                    "Missing parentheses, brackets, or quotes",
                    "Incorrect indentation",
                    "Invalid characters in code",
                    "Incomplete statements"
                ],
                "fixes": [
                    "Check for missing closing parentheses, brackets, or quotes",
                    "Verify proper indentation (use 4 spaces)",
                    "Remove any non-ASCII characters",
                    "Complete incomplete statements"
                ]
            },
            "NameError": {
                "patterns": [
                    r"name '(.+)' is not defined",
                    r"global name '(.+)' is not defined"
                ],
                "common_causes": [
                    "Variable used before definition",
                    "Typo in variable name",
                    "Missing import statement",
                    "Variable out of scope"
                ],
                "fixes": [
                    "Define the variable before using it",
                    "Check for typos in variable names",
                    "Add necessary import statements",
                    "Move variable definition to proper scope"
                ]
            },
            "TypeError": {
                "patterns": [
                    r"unsupported operand type",
                    r"'(.+)' object is not callable",
                    r"takes (.+) positional arguments but (.+) were given"
                ],
                "common_causes": [
                    "Incorrect data types in operations",
                    "Calling non-callable objects",
                    "Wrong number of function arguments",
                    "Missing type conversion"
                ],
                "fixes": [
                    "Check data types and convert if necessary",
                    "Verify object is callable (function/method)",
                    "Check function signature and arguments",
                    "Add proper type conversion (int(), str(), etc.)"
                ]
            },
            "ImportError": {
                "patterns": [
                    r"No module named '(.+)'",
                    r"cannot import name '(.+)'"
                ],
                "common_causes": [
                    "Module not installed",
                    "Incorrect module name",
                    "Module not in Python path",
                    "Circular import"
                ],
                "fixes": [
                    "Install module with pip: pip install module_name",
                    "Check correct module name and spelling",
                    "Add module path to sys.path",
                    "Restructure code to avoid circular imports"
                ]
            },
            "AttributeError": {
                "patterns": [
                    r"'(.+)' object has no attribute '(.+)'",
                    r"module '(.+)' has no attribute '(.+)'"
                ],
                "common_causes": [
                    "Typo in attribute name",
                    "Attribute doesn't exist",
                    "Wrong object type",
                    "Module version mismatch"
                ],
                "fixes": [
                    "Check attribute name spelling",
                    "Verify attribute exists in documentation",
                    "Check object type and available methods",
                    "Update module to correct version"
                ]
            }
        }
    
    def _load_common_fixes(self) -> Dict[str, List[str]]:
        """Load common fixes for frequent issues."""
        return {
            "indentation": [
                "Use 4 spaces for indentation (not tabs)",
                "Ensure consistent indentation throughout the file",
                "Check for mixed tabs and spaces",
                "Use your editor's 'show whitespace' feature"
            ],
            "imports": [
                "Place all imports at the top of the file",
                "Use absolute imports when possible",
                "Group imports: standard library, third-party, local",
                "Use 'from module import specific_function' for clarity"
            ],
            "variables": [
                "Use descriptive variable names",
                "Follow snake_case naming convention",
                "Initialize variables before use",
                "Avoid using reserved keywords as variable names"
            ],
            "functions": [
                "Use descriptive function names",
                "Add docstrings to explain function purpose",
                "Keep functions focused on single responsibility",
                "Use type hints for better code clarity"
            ]
        }
    
    async def generate_code_snippet(
        self,
        description: str,
        category: str = "general",
        complexity: str = "medium",
        include_tests: bool = True,
        provider: str = "auto"
    ) -> CodeSnippet:
        """
        Generate Python code snippet based on description.
        
        Args:
            description: Natural language description of desired code
            category: Code category (web, data, ml, automation, etc.)
            complexity: Desired complexity level (simple, medium, complex)
            include_tests: Whether to include test code
            provider: AI provider to use
        
        Returns:
            CodeSnippet object with generated code
        """
        try:
            # Create specialized prompt based on category and complexity
            prompt = self._create_code_generation_prompt(
                description, category, complexity, include_tests
            )
            
            # Generate code using AI
            response = await generate_response(
                [{"role": "user", "content": prompt}],
                provider=provider,
                temperature=0.3  # Lower temperature for more consistent code
            )
            
            # Parse the response to extract code components
            parsed_code = self._parse_code_response(response)
            
            # Create code snippet object
            snippet = CodeSnippet(
                code=parsed_code["main_code"],
                description=description,
                dependencies=parsed_code.get("dependencies", []),
                complexity=complexity,
                category=category,
                test_code=parsed_code.get("test_code") if include_tests else None
            )
            
            # Validate the generated code
            validation_result = self._validate_code(snippet.code)
            if not validation_result["is_valid"]:
                app_logger.warning(f"Generated code has issues: {validation_result['errors']}")
            
            return snippet
            
        except Exception as e:
            app_logger.error(f"Code generation failed: {e}")
            raise
    
    def _create_code_generation_prompt(
        self,
        description: str,
        category: str,
        complexity: str,
        include_tests: bool
    ) -> str:
        """Create specialized prompt for code generation."""
        
        base_prompt = f"""
You are an expert Python developer. Generate clean, efficient, and well-documented Python code based on the following requirements:

**Description**: {description}
**Category**: {category}
**Complexity**: {complexity}

**Requirements**:
1. Write clean, readable Python code following PEP 8 standards
2. Include comprehensive docstrings with type hints
3. Add error handling where appropriate
4. Use meaningful variable and function names
5. Include comments for complex logic
6. Specify any required dependencies

**Code Structure**:
```python
# Dependencies (if any)
# import statements

# Main code implementation
# Your code here

# Usage example
if __name__ == "__main__":
    # Example usage
    pass
```
"""
        
        # Add category-specific instructions
        category_instructions = {
            "web": """
**Web Development Focus**:
- Use requests for HTTP calls
- Include proper error handling for network requests
- Add input validation
- Consider security best practices
""",
            "data": """
**Data Analysis Focus**:
- Use pandas, numpy for data manipulation
- Include data validation and cleaning
- Add visualization with matplotlib/seaborn if relevant
- Handle missing data appropriately
""",
            "ml": """
**Machine Learning Focus**:
- Use scikit-learn, pandas, numpy
- Include data preprocessing steps
- Add model evaluation metrics
- Include train/test split
""",
            "automation": """
**Automation Focus**:
- Include robust error handling
- Add logging for debugging
- Make scripts configurable
- Include progress indicators for long operations
""",
            "api": """
**API Development Focus**:
- Use FastAPI or Flask
- Include input validation
- Add proper HTTP status codes
- Include API documentation
"""
        }
        
        if category in category_instructions:
            base_prompt += category_instructions[category]
        
        # Add complexity-specific instructions
        complexity_instructions = {
            "simple": """
**Complexity: Simple**
- Keep code straightforward and minimal
- Focus on core functionality
- Minimal dependencies
- Clear and simple logic
""",
            "medium": """
**Complexity: Medium**
- Include proper error handling
- Add configuration options
- Use appropriate design patterns
- Include logging where helpful
""",
            "complex": """
**Complexity: Complex**
- Use advanced Python features appropriately
- Include comprehensive error handling
- Add configuration management
- Consider performance optimization
- Include detailed logging and monitoring
"""
        }
        
        base_prompt += complexity_instructions.get(complexity, "")
        
        # Add test requirements
        if include_tests:
            base_prompt += """

**Testing Requirements**:
Also provide unit tests using pytest:

```python
# test_code.py
import pytest
# Your test code here

def test_main_functionality():
    # Test the main functionality
    pass

def test_edge_cases():
    # Test edge cases and error conditions
    pass
```
"""
        
        base_prompt += """

**Output Format**:
Provide the code in the following format:

DEPENDENCIES:
List any required pip packages

MAIN_CODE:
```python
# Your main code here
```

USAGE_EXAMPLE:
```python
# Example of how to use the code
```
"""
        
        if include_tests:
            base_prompt += """
TEST_CODE:
```python
# Your test code here
```
"""
        
        return base_prompt
    
    def _parse_code_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response to extract code components."""
        parsed = {
            "main_code": "",
            "dependencies": [],
            "test_code": None,
            "usage_example": ""
        }
        
        # Extract dependencies
        deps_match = re.search(r"DEPENDENCIES:\s*\n(.*?)(?=\n[A-Z_]+:|$)", response, re.DOTALL)
        if deps_match:
            deps_text = deps_match.group(1).strip()
            # Parse dependencies from text
            parsed["dependencies"] = [
                line.strip() for line in deps_text.split('\n') 
                if line.strip() and not line.startswith('#')
            ]
        
        # Extract main code
        main_code_match = re.search(r"MAIN_CODE:\s*\n```python\s*\n(.*?)\n```", response, re.DOTALL)
        if main_code_match:
            parsed["main_code"] = main_code_match.group(1).strip()
        
        # Extract test code
        test_code_match = re.search(r"TEST_CODE:\s*\n```python\s*\n(.*?)\n```", response, re.DOTALL)
        if test_code_match:
            parsed["test_code"] = test_code_match.group(1).strip()
        
        # Extract usage example
        usage_match = re.search(r"USAGE_EXAMPLE:\s*\n```python\s*\n(.*?)\n```", response, re.DOTALL)
        if usage_match:
            parsed["usage_example"] = usage_match.group(1).strip()
        
        # Fallback: if no structured format, try to extract any Python code
        if not parsed["main_code"]:
            code_blocks = re.findall(r"```python\s*\n(.*?)\n```", response, re.DOTALL)
            if code_blocks:
                parsed["main_code"] = code_blocks[0].strip()
        
        return parsed
    
    def _validate_code(self, code: str) -> Dict[str, Any]:
        """Validate Python code syntax."""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Parse the code to check syntax
            ast.parse(code)
        except SyntaxError as e:
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"Syntax Error: {e.msg} at line {e.lineno}")
        except Exception as e:
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"Parse Error: {str(e)}")
        
        # Additional checks
        lines = code.split('\n')
        
        # Check for common issues
        for i, line in enumerate(lines, 1):
            # Check for mixed tabs and spaces
            if '\t' in line and '    ' in line:
                validation_result["warnings"].append(f"Mixed tabs and spaces at line {i}")
            
            # Check for very long lines
            if len(line) > 120:
                validation_result["warnings"].append(f"Long line ({len(line)} chars) at line {i}")
        
        return validation_result

    async def analyze_error_log(
        self,
        error_log: str,
        code_context: Optional[str] = None,
        provider: str = "auto"
    ) -> DebugAnalysis:
        """
        Analyze error log and suggest fixes.

        Args:
            error_log: The error log or traceback
            code_context: Optional code context where error occurred
            provider: AI provider to use

        Returns:
            DebugAnalysis object with suggested fixes
        """
        try:
            # Parse error log to extract key information
            error_info = self._parse_error_log(error_log)

            # Check against known error patterns
            pattern_analysis = self._analyze_error_patterns(error_info)

            # Generate AI-powered analysis
            ai_analysis = await self._generate_ai_debug_analysis(
                error_log, code_context, error_info, provider
            )

            # Combine analyses
            debug_analysis = self._combine_debug_analyses(
                error_info, pattern_analysis, ai_analysis
            )

            return debug_analysis

        except Exception as e:
            app_logger.error(f"Error analysis failed: {e}")
            raise

    def _parse_error_log(self, error_log: str) -> Dict[str, Any]:
        """Parse error log to extract structured information."""
        error_info = {
            "error_type": "Unknown",
            "error_message": "",
            "file_path": None,
            "line_number": None,
            "function_name": None,
            "traceback_lines": [],
            "full_traceback": error_log
        }

        lines = error_log.strip().split('\n')

        # Find the main error line (usually the last line)
        for line in reversed(lines):
            if ':' in line and any(error_type in line for error_type in self.error_patterns.keys()):
                parts = line.split(':', 1)
                if len(parts) == 2:
                    error_info["error_type"] = parts[0].strip()
                    error_info["error_message"] = parts[1].strip()
                break

        # Extract file path and line number
        file_line_pattern = r'File "([^"]+)", line (\d+)'
        for line in lines:
            match = re.search(file_line_pattern, line)
            if match:
                error_info["file_path"] = match.group(1)
                error_info["line_number"] = int(match.group(2))
                break

        # Extract function name
        function_pattern = r'in (\w+)'
        for line in lines:
            if 'File "' in line:
                match = re.search(function_pattern, line)
                if match:
                    error_info["function_name"] = match.group(1)
                    break

        # Extract traceback lines
        error_info["traceback_lines"] = [
            line.strip() for line in lines
            if line.strip() and not line.startswith('Traceback')
        ]

        return error_info

    def _analyze_error_patterns(self, error_info: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze error against known patterns."""
        error_type = error_info["error_type"]
        error_message = error_info["error_message"]

        analysis = {
            "matched_patterns": [],
            "suggested_fixes": [],
            "confidence": 0.0
        }

        if error_type in self.error_patterns:
            pattern_info = self.error_patterns[error_type]

            # Check if error message matches known patterns
            for pattern in pattern_info["patterns"]:
                if re.search(pattern, error_message, re.IGNORECASE):
                    analysis["matched_patterns"].append(pattern)
                    analysis["confidence"] += 0.3

            # Add common fixes for this error type
            analysis["suggested_fixes"].extend(pattern_info["fixes"])

            # Increase confidence if we found pattern matches
            if analysis["matched_patterns"]:
                analysis["confidence"] = min(analysis["confidence"], 0.9)
            else:
                analysis["confidence"] = 0.5  # Generic fixes for error type

        return analysis

    async def _generate_ai_debug_analysis(
        self,
        error_log: str,
        code_context: Optional[str],
        error_info: Dict[str, Any],
        provider: str
    ) -> Dict[str, Any]:
        """Generate AI-powered debug analysis."""

        prompt = self._create_debug_analysis_prompt(error_log, code_context, error_info)

        response = await generate_response(
            [{"role": "user", "content": prompt}],
            provider=provider,
            temperature=0.2  # Low temperature for consistent analysis
        )

        # Parse AI response
        ai_analysis = self._parse_debug_response(response)

        return ai_analysis

    def _create_debug_analysis_prompt(
        self,
        error_log: str,
        code_context: Optional[str],
        error_info: Dict[str, Any]
    ) -> str:
        """Create prompt for AI debug analysis."""

        prompt = f"""
You are an expert Python debugger. Analyze the following error and provide detailed debugging assistance.

**ERROR LOG:**
```
{error_log}
```

**ERROR INFORMATION:**
- Error Type: {error_info['error_type']}
- Error Message: {error_info['error_message']}
- File: {error_info.get('file_path', 'Unknown')}
- Line: {error_info.get('line_number', 'Unknown')}
- Function: {error_info.get('function_name', 'Unknown')}
"""

        if code_context:
            prompt += f"""
**CODE CONTEXT:**
```python
{code_context}
```
"""

        prompt += """
**ANALYSIS REQUIREMENTS:**
1. Identify the root cause of the error
2. Explain why this error occurred
3. Provide specific, actionable fixes
4. Suggest code improvements to prevent similar errors
5. Rate the severity of the issue (low/medium/high/critical)
6. Provide confidence level (0-100%) for your analysis

**OUTPUT FORMAT:**
ROOT_CAUSE:
[Explain the fundamental reason for the error]

EXPLANATION:
[Detailed explanation of why this error occurred]

IMMEDIATE_FIXES:
1. [Specific fix #1]
2. [Specific fix #2]
3. [Additional fixes if needed]

CODE_SUGGESTIONS:
```python
# Fixed code example
[Provide corrected code if applicable]
```

PREVENTION_TIPS:
1. [How to prevent this error in the future]
2. [Best practices related to this issue]
3. [Additional recommendations]

SEVERITY: [low/medium/high/critical]
CONFIDENCE: [0-100]%

RELATED_CONCEPTS:
- [Related Python concepts or documentation]
- [Useful resources for learning more]
"""

        return prompt

    def _parse_debug_response(self, response: str) -> Dict[str, Any]:
        """Parse AI debug response."""
        parsed = {
            "root_cause": "",
            "explanation": "",
            "immediate_fixes": [],
            "code_suggestions": [],
            "prevention_tips": [],
            "severity": "medium",
            "confidence": 0.5,
            "related_concepts": []
        }

        # Extract root cause
        root_cause_match = re.search(r"ROOT_CAUSE:\s*\n(.*?)(?=\n[A-Z_]+:|$)", response, re.DOTALL)
        if root_cause_match:
            parsed["root_cause"] = root_cause_match.group(1).strip()

        # Extract explanation
        explanation_match = re.search(r"EXPLANATION:\s*\n(.*?)(?=\n[A-Z_]+:|$)", response, re.DOTALL)
        if explanation_match:
            parsed["explanation"] = explanation_match.group(1).strip()

        # Extract immediate fixes
        fixes_match = re.search(r"IMMEDIATE_FIXES:\s*\n(.*?)(?=\n[A-Z_]+:|$)", response, re.DOTALL)
        if fixes_match:
            fixes_text = fixes_match.group(1).strip()
            parsed["immediate_fixes"] = [
                line.strip().lstrip('1234567890. ')
                for line in fixes_text.split('\n')
                if line.strip() and re.match(r'^\d+\.', line.strip())
            ]

        # Extract code suggestions
        code_match = re.search(r"CODE_SUGGESTIONS:\s*\n```python\s*\n(.*?)\n```", response, re.DOTALL)
        if code_match:
            parsed["code_suggestions"] = [code_match.group(1).strip()]

        # Extract prevention tips
        prevention_match = re.search(r"PREVENTION_TIPS:\s*\n(.*?)(?=\n[A-Z_]+:|$)", response, re.DOTALL)
        if prevention_match:
            prevention_text = prevention_match.group(1).strip()
            parsed["prevention_tips"] = [
                line.strip().lstrip('1234567890. ')
                for line in prevention_text.split('\n')
                if line.strip() and re.match(r'^\d+\.', line.strip())
            ]

        # Extract severity
        severity_match = re.search(r"SEVERITY:\s*(\w+)", response, re.IGNORECASE)
        if severity_match:
            parsed["severity"] = severity_match.group(1).lower()

        # Extract confidence
        confidence_match = re.search(r"CONFIDENCE:\s*(\d+)", response)
        if confidence_match:
            parsed["confidence"] = int(confidence_match.group(1)) / 100.0

        # Extract related concepts
        concepts_match = re.search(r"RELATED_CONCEPTS:\s*\n(.*?)(?=\n[A-Z_]+:|$)", response, re.DOTALL)
        if concepts_match:
            concepts_text = concepts_match.group(1).strip()
            parsed["related_concepts"] = [
                line.strip().lstrip('- ')
                for line in concepts_text.split('\n')
                if line.strip() and line.strip().startswith('-')
            ]

        return parsed

    def _combine_debug_analyses(
        self,
        error_info: Dict[str, Any],
        pattern_analysis: Dict[str, Any],
        ai_analysis: Dict[str, Any]
    ) -> DebugAnalysis:
        """Combine different analyses into final debug analysis."""

        # Combine suggested fixes
        all_fixes = []
        all_fixes.extend(pattern_analysis.get("suggested_fixes", []))
        all_fixes.extend(ai_analysis.get("immediate_fixes", []))

        # Remove duplicates while preserving order
        unique_fixes = []
        seen = set()
        for fix in all_fixes:
            if fix.lower() not in seen:
                unique_fixes.append(fix)
                seen.add(fix.lower())

        # Combine code suggestions
        code_suggestions = ai_analysis.get("code_suggestions", [])

        # Calculate combined confidence
        pattern_confidence = pattern_analysis.get("confidence", 0.0)
        ai_confidence = ai_analysis.get("confidence", 0.5)
        combined_confidence = (pattern_confidence + ai_confidence) / 2

        # Create final analysis
        debug_analysis = DebugAnalysis(
            error_type=error_info["error_type"],
            error_message=error_info["error_message"],
            suggested_fixes=unique_fixes,
            code_suggestions=code_suggestions,
            severity=ai_analysis.get("severity", "medium"),
            confidence=combined_confidence,
            related_docs=ai_analysis.get("related_concepts", [])
        )

        return debug_analysis

    def run_code_safely(self, code: str, timeout: int = 10) -> Dict[str, Any]:
        """
        Run Python code safely and capture output/errors.

        Args:
            code: Python code to execute
            timeout: Timeout in seconds

        Returns:
            Dictionary with execution results
        """
        result = {
            "success": False,
            "output": "",
            "error": "",
            "execution_time": 0.0
        }

        try:
            import tempfile
            import time

            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name

            start_time = time.time()

            # Run code in subprocess
            process = subprocess.run(
                [sys.executable, temp_file],
                capture_output=True,
                text=True,
                timeout=timeout
            )

            end_time = time.time()
            result["execution_time"] = end_time - start_time

            if process.returncode == 0:
                result["success"] = True
                result["output"] = process.stdout
            else:
                result["error"] = process.stderr

            # Clean up
            import os
            os.unlink(temp_file)

        except subprocess.TimeoutExpired:
            result["error"] = f"Code execution timed out after {timeout} seconds"
        except Exception as e:
            result["error"] = f"Execution error: {str(e)}"

        return result
