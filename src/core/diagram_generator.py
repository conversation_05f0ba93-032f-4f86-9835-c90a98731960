"""
Diagram Generator for creating architecture diagrams from text descriptions.
"""

import re
import json
import base64
import hashlib
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass
from pathlib import Path
import requests
from PIL import Image, ImageDraw, ImageFont
import io

from core.response_generator import generate_response
from config.settings import get_settings
from config.logging_config import app_logger


@dataclass
class DiagramComponent:
    """Represents a component in the architecture diagram."""
    name: str
    type: str  # service, database, api, frontend, backend, etc.
    description: str
    position: Tuple[int, int] = (0, 0)
    size: Tuple[int, int] = (120, 80)
    color: str = "#4A90E2"
    connections: List[str] = None
    
    def __post_init__(self):
        if self.connections is None:
            self.connections = []


@dataclass
class DiagramConnection:
    """Represents a connection between components."""
    from_component: str
    to_component: str
    label: str = ""
    connection_type: str = "arrow"  # arrow, bidirectional, dashed
    color: str = "#666666"


@dataclass
class ArchitectureDiagram:
    """Represents a complete architecture diagram."""
    title: str
    description: str
    components: List[DiagramComponent]
    connections: List[DiagramConnection]
    diagram_type: str = "system"  # system, network, deployment, data_flow
    style: str = "modern"  # modern, minimal, detailed
    generated_image_path: Optional[str] = None
    mermaid_code: Optional[str] = None


class DiagramGenerator:
    """Generates architecture diagrams from text descriptions."""
    
    def __init__(self):
        self.settings = get_settings()
        self.component_colors = {
            "frontend": "#FF6B6B",
            "backend": "#4ECDC4", 
            "database": "#45B7D1",
            "api": "#96CEB4",
            "service": "#FFEAA7",
            "cache": "#DDA0DD",
            "queue": "#98D8C8",
            "storage": "#F7DC6F",
            "security": "#F1948A",
            "monitoring": "#BB8FCE",
            "network": "#85C1E9",
            "external": "#D5DBDB"
        }
    
    async def generate_diagram_from_text(
        self,
        description: str,
        diagram_type: str = "system",
        style: str = "modern",
        provider: str = "auto"
    ) -> ArchitectureDiagram:
        """
        Generate architecture diagram from text description.
        
        Args:
            description: Text description of the architecture
            diagram_type: Type of diagram (system, network, deployment, data_flow)
            style: Visual style (modern, minimal, detailed)
            provider: AI provider to use
        
        Returns:
            ArchitectureDiagram object with components and connections
        """
        try:
            # Generate structured diagram data using AI
            diagram_data = await self._generate_diagram_structure(
                description, diagram_type, style, provider
            )
            
            # Parse the structured data
            diagram = self._parse_diagram_data(diagram_data, description)
            
            # Generate Mermaid code
            diagram.mermaid_code = self._generate_mermaid_code(diagram)
            
            # Generate visual diagram
            image_path = await self._generate_visual_diagram(diagram)
            diagram.generated_image_path = image_path
            
            app_logger.info(f"Generated diagram: {diagram.title}")
            return diagram
            
        except Exception as e:
            app_logger.error(f"Diagram generation failed: {e}")
            raise
    
    async def _generate_diagram_structure(
        self,
        description: str,
        diagram_type: str,
        style: str,
        provider: str
    ) -> Dict[str, Any]:
        """Generate structured diagram data using AI."""
        
        prompt = self._create_diagram_prompt(description, diagram_type, style)
        
        response = await generate_response(
            [{"role": "user", "content": prompt}],
            provider=provider,
            temperature=0.3  # Lower temperature for consistent structure
        )
        
        # Parse the AI response
        return self._parse_ai_response(response)
    
    def _create_diagram_prompt(self, description: str, diagram_type: str, style: str) -> str:
        """Create specialized prompt for diagram generation."""
        
        prompt = f"""
You are an expert system architect. Create a detailed architecture diagram specification based on the following description.

**Project Description:**
{description}

**Diagram Type:** {diagram_type}
**Style:** {style}

**Requirements:**
1. Identify all major components and their types
2. Define connections and data flows between components
3. Specify component positioning for optimal layout
4. Include appropriate labels and descriptions
5. Consider scalability and best practices

**Component Types Available:**
- frontend (web interfaces, mobile apps)
- backend (application servers, APIs)
- database (SQL, NoSQL, cache)
- api (REST, GraphQL, microservices)
- service (business logic, workers)
- cache (Redis, Memcached)
- queue (message queues, event streams)
- storage (file storage, CDN)
- security (auth, firewall, encryption)
- monitoring (logging, metrics, alerts)
- network (load balancer, proxy, CDN)
- external (third-party services, APIs)

**Output Format (JSON):**
```json
{{
  "title": "Architecture Diagram Title",
  "description": "Brief description of the architecture",
  "components": [
    {{
      "name": "Component Name",
      "type": "component_type",
      "description": "Component description",
      "position": [x, y],
      "connections": ["connected_component_1", "connected_component_2"]
    }}
  ],
  "connections": [
    {{
      "from": "source_component",
      "to": "target_component", 
      "label": "Connection description",
      "type": "arrow"
    }}
  ],
  "layers": [
    {{
      "name": "Layer Name",
      "components": ["component1", "component2"],
      "description": "Layer description"
    }}
  ]
}}
```

**Layout Guidelines:**
- Position components in logical layers (presentation, business, data)
- Use coordinates from 0-1000 for positioning
- Group related components together
- Ensure clear data flow visualization
- Consider user interaction patterns

**Connection Types:**
- "arrow": One-way data flow
- "bidirectional": Two-way communication
- "dashed": Optional or async connections
- "thick": High-volume data flow

Generate a comprehensive architecture diagram specification that clearly shows the system structure and component relationships.
"""
        
        return prompt
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response to extract diagram data."""
        try:
            # Try to extract JSON from the response
            json_match = re.search(r'```json\s*\n(.*?)\n```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
            
            # Try to find JSON without code blocks
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            
            # Fallback: parse structured text
            return self._parse_structured_text(response)
            
        except json.JSONDecodeError as e:
            app_logger.warning(f"Failed to parse JSON response: {e}")
            return self._parse_structured_text(response)
    
    def _parse_structured_text(self, response: str) -> Dict[str, Any]:
        """Parse structured text response as fallback."""
        # Basic parsing for when JSON fails
        lines = response.split('\n')
        
        diagram_data = {
            "title": "Generated Architecture",
            "description": "Architecture diagram from text description",
            "components": [],
            "connections": [],
            "layers": []
        }
        
        # Extract title
        for line in lines:
            if 'title' in line.lower() and ':' in line:
                diagram_data["title"] = line.split(':', 1)[1].strip()
                break
        
        # Extract components (basic parsing)
        component_section = False
        for line in lines:
            line = line.strip()
            if 'component' in line.lower() and ':' in line:
                component_section = True
                continue
            
            if component_section and line and not line.startswith('#'):
                if '-' in line or '•' in line:
                    component_name = line.replace('-', '').replace('•', '').strip()
                    if component_name:
                        diagram_data["components"].append({
                            "name": component_name,
                            "type": "service",
                            "description": component_name,
                            "position": [100, 100],
                            "connections": []
                        })
        
        return diagram_data
    
    def _parse_diagram_data(self, data: Dict[str, Any], original_description: str) -> ArchitectureDiagram:
        """Parse diagram data into ArchitectureDiagram object."""
        
        # Create components
        components = []
        for comp_data in data.get("components", []):
            component = DiagramComponent(
                name=comp_data.get("name", "Unknown"),
                type=comp_data.get("type", "service"),
                description=comp_data.get("description", ""),
                position=tuple(comp_data.get("position", [100, 100])),
                connections=comp_data.get("connections", []),
                color=self.component_colors.get(comp_data.get("type", "service"), "#4A90E2")
            )
            components.append(component)
        
        # Create connections
        connections = []
        for conn_data in data.get("connections", []):
            connection = DiagramConnection(
                from_component=conn_data.get("from", ""),
                to_component=conn_data.get("to", ""),
                label=conn_data.get("label", ""),
                connection_type=conn_data.get("type", "arrow")
            )
            connections.append(connection)
        
        # Create diagram
        diagram = ArchitectureDiagram(
            title=data.get("title", "Architecture Diagram"),
            description=data.get("description", original_description),
            components=components,
            connections=connections,
            diagram_type=data.get("diagram_type", "system"),
            style=data.get("style", "modern")
        )
        
        return diagram
    
    def _generate_mermaid_code(self, diagram: ArchitectureDiagram) -> str:
        """Generate Mermaid diagram code."""
        
        mermaid_code = f"""
graph TD
    %% {diagram.title}
    %% {diagram.description}
    
"""
        
        # Add components
        for component in diagram.components:
            # Sanitize component name for Mermaid
            comp_id = re.sub(r'[^a-zA-Z0-9]', '_', component.name)
            
            # Choose shape based on component type
            if component.type == "database":
                shape = f"{comp_id}[({component.name})]"
            elif component.type == "external":
                shape = f"{comp_id}[/{component.name}/]"
            elif component.type == "frontend":
                shape = f"{comp_id}[{component.name}]"
            else:
                shape = f"{comp_id}[{component.name}]"
            
            mermaid_code += f"    {shape}\n"
        
        mermaid_code += "\n"
        
        # Add connections
        for connection in diagram.connections:
            from_id = re.sub(r'[^a-zA-Z0-9]', '_', connection.from_component)
            to_id = re.sub(r'[^a-zA-Z0-9]', '_', connection.to_component)
            
            if connection.connection_type == "bidirectional":
                arrow = "<-->"
            elif connection.connection_type == "dashed":
                arrow = "-..->"
            else:
                arrow = "-->"
            
            if connection.label:
                mermaid_code += f"    {from_id} {arrow}|{connection.label}| {to_id}\n"
            else:
                mermaid_code += f"    {from_id} {arrow} {to_id}\n"
        
        # Add styling
        mermaid_code += "\n    %% Styling\n"
        for i, component in enumerate(diagram.components):
            comp_id = re.sub(r'[^a-zA-Z0-9]', '_', component.name)
            mermaid_code += f"    classDef class{i} fill:{component.color},stroke:#333,stroke-width:2px\n"
            mermaid_code += f"    class {comp_id} class{i}\n"
        
        return mermaid_code.strip()
    
    async def _generate_visual_diagram(self, diagram: ArchitectureDiagram) -> str:
        """Generate visual diagram image."""
        try:
            # Create image using PIL
            width, height = 1200, 800
            image = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(image)
            
            # Try to load a font
            try:
                font = ImageFont.truetype("arial.ttf", 12)
                title_font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
                title_font = ImageFont.load_default()
            
            # Draw title
            title_bbox = draw.textbbox((0, 0), diagram.title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            draw.text(((width - title_width) // 2, 20), diagram.title, fill='black', font=title_font)
            
            # Calculate component positions
            self._calculate_component_positions(diagram, width, height)
            
            # Draw connections first (so they appear behind components)
            self._draw_connections(draw, diagram)
            
            # Draw components
            self._draw_components(draw, diagram, font)
            
            # Save image
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"architecture_diagram_{timestamp}.png"
            output_dir = Path("data/diagrams")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            image_path = output_dir / filename
            image.save(image_path)
            
            app_logger.info(f"Diagram saved to {image_path}")
            return str(image_path)
            
        except Exception as e:
            app_logger.error(f"Error generating visual diagram: {e}")
            return ""
    
    def _calculate_component_positions(self, diagram: ArchitectureDiagram, width: int, height: int):
        """Calculate optimal positions for components."""
        # Simple grid layout for now
        margin = 100
        available_width = width - 2 * margin
        available_height = height - 150  # Account for title and margins
        
        num_components = len(diagram.components)
        if num_components == 0:
            return
        
        # Calculate grid dimensions
        cols = int((num_components ** 0.5)) + 1
        rows = (num_components + cols - 1) // cols
        
        cell_width = available_width // cols
        cell_height = available_height // rows
        
        for i, component in enumerate(diagram.components):
            row = i // cols
            col = i % cols
            
            x = margin + col * cell_width + cell_width // 2
            y = 100 + row * cell_height + cell_height // 2
            
            component.position = (x, y)
    
    def _draw_components(self, draw: ImageDraw.Draw, diagram: ArchitectureDiagram, font):
        """Draw components on the diagram."""
        for component in diagram.components:
            x, y = component.position
            w, h = component.size
            
            # Draw component rectangle
            left = x - w // 2
            top = y - h // 2
            right = x + w // 2
            bottom = y + h // 2
            
            draw.rectangle([left, top, right, bottom], 
                         fill=component.color, outline='black', width=2)
            
            # Draw component name
            text_bbox = draw.textbbox((0, 0), component.name, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            text_x = x - text_width // 2
            text_y = y - text_height // 2
            
            draw.text((text_x, text_y), component.name, fill='white', font=font)
            
            # Draw component type
            type_text = f"({component.type})"
            type_bbox = draw.textbbox((0, 0), type_text, font=font)
            type_width = type_bbox[2] - type_bbox[0]
            
            type_x = x - type_width // 2
            type_y = y + text_height // 2 + 5
            
            draw.text((type_x, type_y), type_text, fill='white', font=font)
    
    def _draw_connections(self, draw: ImageDraw.Draw, diagram: ArchitectureDiagram):
        """Draw connections between components."""
        # Create a mapping of component names to positions
        component_positions = {comp.name: comp.position for comp in diagram.components}
        
        for connection in diagram.connections:
            from_pos = component_positions.get(connection.from_component)
            to_pos = component_positions.get(connection.to_component)
            
            if from_pos and to_pos:
                # Draw arrow line
                draw.line([from_pos, to_pos], fill=connection.color, width=2)
                
                # Draw arrowhead
                self._draw_arrowhead(draw, from_pos, to_pos, connection.color)
    
    def _draw_arrowhead(self, draw: ImageDraw.Draw, from_pos: Tuple[int, int], 
                       to_pos: Tuple[int, int], color: str):
        """Draw an arrowhead at the end of a connection."""
        import math
        
        # Calculate arrow direction
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        length = math.sqrt(dx*dx + dy*dy)
        
        if length == 0:
            return
        
        # Normalize direction
        dx /= length
        dy /= length
        
        # Arrow parameters
        arrow_length = 15
        arrow_width = 8
        
        # Calculate arrowhead points
        end_x, end_y = to_pos
        
        # Move back from the end point
        back_x = end_x - dx * arrow_length
        back_y = end_y - dy * arrow_length
        
        # Calculate perpendicular direction
        perp_x = -dy
        perp_y = dx
        
        # Calculate arrowhead triangle points
        point1 = (back_x + perp_x * arrow_width, back_y + perp_y * arrow_width)
        point2 = (back_x - perp_x * arrow_width, back_y - perp_y * arrow_width)
        
        # Draw arrowhead
        draw.polygon([to_pos, point1, point2], fill=color)

    async def generate_diagram_with_ai_image(
        self,
        description: str,
        use_external_api: bool = True,
        provider: str = "auto"
    ) -> Dict[str, Any]:
        """
        Generate diagram using external AI image generation API.

        Args:
            description: Text description of the architecture
            use_external_api: Whether to use external image generation API
            provider: AI provider for text analysis

        Returns:
            Dictionary with diagram data and image information
        """
        try:
            # First, generate structured description for image generation
            image_prompt = await self._create_image_generation_prompt(description, provider)

            # Generate image using external API (placeholder for now)
            image_result = await self._generate_image_with_api(image_prompt)

            # Also generate our structured diagram
            diagram = await self.generate_diagram_from_text(description, provider=provider)

            return {
                "diagram": diagram,
                "ai_generated_image": image_result,
                "image_prompt": image_prompt,
                "mermaid_code": diagram.mermaid_code,
                "local_image_path": diagram.generated_image_path
            }

        except Exception as e:
            app_logger.error(f"AI image generation failed: {e}")
            # Fallback to local generation
            diagram = await self.generate_diagram_from_text(description, provider=provider)
            return {
                "diagram": diagram,
                "ai_generated_image": None,
                "image_prompt": None,
                "mermaid_code": diagram.mermaid_code,
                "local_image_path": diagram.generated_image_path
            }

    async def _create_image_generation_prompt(self, description: str, provider: str) -> str:
        """Create optimized prompt for AI image generation."""

        prompt = f"""
Create a detailed prompt for generating a professional architecture diagram image based on this description:

{description}

The prompt should be optimized for AI image generation and include:
1. Clear visual style specifications (modern, clean, professional)
2. Specific component types and their visual representations
3. Layout and positioning guidelines
4. Color scheme and styling preferences
5. Technical diagram conventions

Generate a comprehensive image generation prompt that will produce a high-quality architecture diagram.
"""

        response = await generate_response(
            [{"role": "user", "content": prompt}],
            provider=provider,
            temperature=0.4
        )

        # Extract and optimize the prompt
        optimized_prompt = self._optimize_image_prompt(response, description)

        return optimized_prompt

    def _optimize_image_prompt(self, ai_response: str, original_description: str) -> str:
        """Optimize the AI-generated prompt for image generation."""

        # Base prompt structure for architecture diagrams
        base_prompt = """
Professional software architecture diagram, clean modern style,
technical illustration, high quality, detailed components,
clear connections, labeled elements, enterprise architecture,
system design diagram, blue and white color scheme,
minimalist design, vector-style illustration
"""

        # Extract key elements from AI response
        key_elements = self._extract_key_elements(ai_response)

        # Combine base prompt with extracted elements
        optimized_prompt = f"{base_prompt}, {key_elements}, based on: {original_description[:200]}"

        return optimized_prompt

    def _extract_key_elements(self, text: str) -> str:
        """Extract key visual elements from text."""
        # Simple keyword extraction for image prompts
        keywords = []

        # Component types
        component_types = ["frontend", "backend", "database", "api", "microservice",
                          "server", "client", "storage", "cache", "queue"]

        for comp_type in component_types:
            if comp_type in text.lower():
                keywords.append(comp_type)

        # Visual descriptors
        visual_terms = ["modern", "clean", "professional", "detailed", "minimalist",
                       "technical", "enterprise", "scalable"]

        for term in visual_terms:
            if term in text.lower():
                keywords.append(term)

        return ", ".join(keywords[:10])  # Limit to 10 keywords

    async def _generate_image_with_api(self, prompt: str) -> Optional[Dict[str, Any]]:
        """
        Generate image using external API (placeholder implementation).

        Note: This is a placeholder. In production, you would integrate with:
        - DALL-E API
        - Midjourney API
        - Stable Diffusion API
        - Or other image generation services
        """
        try:
            # Placeholder for external API call
            # In real implementation, you would call the actual API here

            app_logger.info(f"Would generate image with prompt: {prompt[:100]}...")

            # Simulate API response
            return {
                "image_url": "https://placeholder.example.com/architecture_diagram.png",
                "image_id": f"img_{hashlib.md5(prompt.encode()).hexdigest()[:8]}",
                "prompt_used": prompt,
                "generation_time": 5.2,
                "status": "success"
            }

        except Exception as e:
            app_logger.error(f"External image API failed: {e}")
            return None

    def create_image_html(self, image_path: str, title: str = "", description: str = "") -> str:
        """Create HTML for displaying image in chat."""

        # Convert to base64 for embedding
        try:
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode()

            html = f"""
            <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333;">{title}</h3>
                <img src="data:image/png;base64,{image_data}"
                     style="max-width: 100%; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                     alt="{title}">
                <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">{description}</p>
            </div>
            """

            return html

        except Exception as e:
            app_logger.error(f"Error creating image HTML: {e}")
            return f"<p>Error displaying image: {image_path}</p>"

    def create_mermaid_html(self, mermaid_code: str, title: str = "") -> str:
        """Create HTML for displaying Mermaid diagram."""

        html = f"""
        <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;">
            <h3 style="margin-top: 0; color: #333;">{title}</h3>
            <div class="mermaid">
{mermaid_code}
            </div>
            <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
            <script>
                mermaid.initialize({{startOnLoad: true}});
            </script>
        </div>
        """

        return html

    def create_diagram_response(self, diagram_result: Dict[str, Any]) -> str:
        """Create formatted response with diagram and images."""

        diagram = diagram_result.get("diagram")
        ai_image = diagram_result.get("ai_generated_image")
        local_image = diagram_result.get("local_image_path")
        mermaid_code = diagram_result.get("mermaid_code")

        response_parts = []

        # Add title and description
        if diagram:
            response_parts.append(f"# {diagram.title}")
            response_parts.append(f"\n{diagram.description}\n")

        # Add AI-generated image if available
        if ai_image and ai_image.get("image_url"):
            response_parts.append("## AI-Generated Architecture Diagram")
            response_parts.append(f"![Architecture Diagram]({ai_image['image_url']})")
            response_parts.append(f"*Generated using AI image generation*\n")

        # Add local generated image
        if local_image and Path(local_image).exists():
            image_html = self.create_image_html(
                local_image,
                "Generated Architecture Diagram",
                "Programmatically generated diagram"
            )
            response_parts.append("## Generated Diagram")
            response_parts.append(image_html)

        # Add Mermaid code
        if mermaid_code:
            response_parts.append("## Interactive Diagram (Mermaid)")
            response_parts.append("```mermaid")
            response_parts.append(mermaid_code)
            response_parts.append("```")

        # Add component details
        if diagram and diagram.components:
            response_parts.append("## Architecture Components")
            for component in diagram.components:
                response_parts.append(f"- **{component.name}** ({component.type}): {component.description}")

        # Add connections
        if diagram and diagram.connections:
            response_parts.append("\n## Component Connections")
            for connection in diagram.connections:
                label = f" - {connection.label}" if connection.label else ""
                response_parts.append(f"- {connection.from_component} → {connection.to_component}{label}")

        return "\n".join(response_parts)


# Helper functions for easy usage
async def generate_architecture_diagram(
    description: str,
    diagram_type: str = "system",
    style: str = "modern",
    provider: str = "auto"
) -> str:
    """
    Helper function to generate architecture diagram and return formatted response.

    Args:
        description: Text description of the architecture
        diagram_type: Type of diagram (system, network, deployment, data_flow)
        style: Visual style (modern, minimal, detailed)
        provider: AI provider to use

    Returns:
        Formatted response with diagram and images
    """
    generator = DiagramGenerator()

    # Generate diagram with AI image
    result = await generator.generate_diagram_with_ai_image(
        description=description,
        use_external_api=True,
        provider=provider
    )

    # Create formatted response
    response = generator.create_diagram_response(result)

    return response


async def create_mermaid_diagram(description: str, provider: str = "auto") -> str:
    """
    Helper function to create Mermaid diagram code.

    Args:
        description: Text description of the architecture
        provider: AI provider to use

    Returns:
        Mermaid diagram code
    """
    generator = DiagramGenerator()

    diagram = await generator.generate_diagram_from_text(
        description=description,
        provider=provider
    )

    return diagram.mermaid_code or ""
