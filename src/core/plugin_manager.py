"""
External Plugin Manager for calling various APIs and embedding results.
"""

import re
import json
import base64
import hashlib
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from dataclasses import dataclass
from pathlib import Path
import requests
from urllib.parse import quote, urlencode

from core.response_generator import generate_response
from config.settings import get_settings
from config.logging_config import app_logger


@dataclass
class PluginResult:
    """Represents the result from a plugin execution."""
    plugin_name: str
    success: bool
    content_type: str  # audio, image, gif, video, text, json
    content_url: Optional[str] = None
    content_data: Optional[bytes] = None
    metadata: Dict[str, Any] = None
    error_message: Optional[str] = None
    embed_html: Optional[str] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class PluginConfig:
    """Configuration for a plugin."""
    name: str
    description: str
    keywords: List[str]
    api_endpoint: str
    api_key_required: bool = False
    rate_limit: int = 10  # requests per minute
    content_type: str = "json"
    enabled: bool = True


class ExternalPluginManager:
    """Manages external plugins and API integrations."""
    
    def __init__(self):
        self.settings = get_settings()
        self.plugins = self._load_plugin_configs()
        self.rate_limits = {}  # Track rate limiting
        self.cache = {}  # Simple cache for results
    
    def _load_plugin_configs(self) -> Dict[str, PluginConfig]:
        """Load plugin configurations."""
        plugins = {}
        
        # Music/Audio Plugins
        plugins["huggingface_audio"] = PluginConfig(
            name="huggingface_audio",
            description="Generate music and audio using Hugging Face models",
            keywords=["music", "audio", "sound", "song", "melody", "beat", "generate music", "create audio"],
            api_endpoint="https://api-inference.huggingface.co/models/facebook/musicgen-small",
            api_key_required=True,
            content_type="audio"
        )
        
        plugins["freesound"] = PluginConfig(
            name="freesound",
            description="Search for sound effects and audio clips",
            keywords=["sound effect", "sfx", "audio clip", "noise", "ambient sound"],
            api_endpoint="https://freesound.org/apiv2/search/text/",
            api_key_required=True,
            content_type="audio"
        )
        
        # GIF and Image Plugins
        plugins["giphy"] = PluginConfig(
            name="giphy",
            description="Search for GIFs and animated images",
            keywords=["gif", "animated", "reaction", "meme", "funny", "animation"],
            api_endpoint="https://api.giphy.com/v1/gifs/search",
            api_key_required=True,
            content_type="gif"
        )
        
        plugins["tenor"] = PluginConfig(
            name="tenor",
            description="Search for GIFs from Tenor",
            keywords=["gif", "tenor", "animated gif", "reaction gif"],
            api_endpoint="https://tenor.googleapis.com/v2/search",
            api_key_required=True,
            content_type="gif"
        )
        
        plugins["unsplash"] = PluginConfig(
            name="unsplash",
            description="Search for high-quality photos",
            keywords=["photo", "image", "picture", "photography", "stock photo"],
            api_endpoint="https://api.unsplash.com/search/photos",
            api_key_required=True,
            content_type="image"
        )
        
        # Video Plugins
        plugins["youtube_search"] = PluginConfig(
            name="youtube_search",
            description="Search for YouTube videos",
            keywords=["video", "youtube", "watch", "tutorial", "music video"],
            api_endpoint="https://www.googleapis.com/youtube/v3/search",
            api_key_required=True,
            content_type="video"
        )
        
        # Weather and Data Plugins
        plugins["weather"] = PluginConfig(
            name="weather",
            description="Get weather information",
            keywords=["weather", "temperature", "forecast", "climate", "rain", "sunny"],
            api_endpoint="https://api.openweathermap.org/data/2.5/weather",
            api_key_required=True,
            content_type="json"
        )
        
        plugins["news"] = PluginConfig(
            name="news",
            description="Get latest news articles",
            keywords=["news", "article", "headlines", "breaking news", "current events"],
            api_endpoint="https://newsapi.org/v2/everything",
            api_key_required=True,
            content_type="json"
        )
        
        # Fun and Entertainment
        plugins["cat_facts"] = PluginConfig(
            name="cat_facts",
            description="Get random cat facts",
            keywords=["cat fact", "cat", "feline", "pet fact", "animal fact"],
            api_endpoint="https://catfact.ninja/fact",
            api_key_required=False,
            content_type="json"
        )
        
        plugins["jokes"] = PluginConfig(
            name="jokes",
            description="Get random jokes",
            keywords=["joke", "funny", "humor", "laugh", "comedy"],
            api_endpoint="https://official-joke-api.appspot.com/random_joke",
            api_key_required=False,
            content_type="json"
        )
        
        plugins["quotes"] = PluginConfig(
            name="quotes",
            description="Get inspirational quotes",
            keywords=["quote", "inspiration", "motivational", "wisdom", "saying"],
            api_endpoint="https://api.quotable.io/random",
            api_key_required=False,
            content_type="json"
        )
        
        return plugins
    
    async def detect_plugin_intent(
        self,
        message: str,
        provider: str = "auto"
    ) -> Optional[Dict[str, Any]]:
        """
        Detect if message requires plugin execution.
        
        Args:
            message: User message
            provider: AI provider for intent analysis
        
        Returns:
            Dictionary with plugin intent information
        """
        try:
            # First, check for direct keyword matches
            direct_match = self._check_direct_keywords(message)
            if direct_match:
                return direct_match
            
            # Use AI to analyze intent
            ai_intent = await self._analyze_intent_with_ai(message, provider)
            
            return ai_intent
            
        except Exception as e:
            app_logger.error(f"Plugin intent detection failed: {e}")
            return None
    
    def _check_direct_keywords(self, message: str) -> Optional[Dict[str, Any]]:
        """Check for direct keyword matches."""
        message_lower = message.lower()
        
        # Score plugins based on keyword matches
        plugin_scores = {}
        
        for plugin_name, plugin_config in self.plugins.items():
            if not plugin_config.enabled:
                continue
            
            score = 0
            matched_keywords = []
            
            for keyword in plugin_config.keywords:
                if keyword.lower() in message_lower:
                    score += len(keyword.split())  # Multi-word keywords get higher score
                    matched_keywords.append(keyword)
            
            if score > 0:
                plugin_scores[plugin_name] = {
                    "score": score,
                    "matched_keywords": matched_keywords,
                    "plugin_config": plugin_config
                }
        
        # Return highest scoring plugin
        if plugin_scores:
            best_plugin = max(plugin_scores.items(), key=lambda x: x[1]["score"])
            plugin_name, plugin_data = best_plugin
            
            # Extract search query
            search_query = self._extract_search_query(message, plugin_data["matched_keywords"])
            
            return {
                "plugin_name": plugin_name,
                "plugin_config": plugin_data["plugin_config"],
                "search_query": search_query,
                "confidence": min(plugin_data["score"] / 3.0, 1.0),  # Normalize confidence
                "matched_keywords": plugin_data["matched_keywords"]
            }
        
        return None
    
    def _extract_search_query(self, message: str, matched_keywords: List[str]) -> str:
        """Extract search query from message by removing matched keywords."""
        query = message
        
        # Remove matched keywords
        for keyword in matched_keywords:
            query = re.sub(rf'\b{re.escape(keyword)}\b', '', query, flags=re.IGNORECASE)
        
        # Clean up the query
        query = re.sub(r'\s+', ' ', query).strip()
        
        # Remove common words
        stop_words = ["find", "search", "get", "show", "play", "generate", "create", "for", "me", "a", "an", "the"]
        words = query.split()
        filtered_words = [word for word in words if word.lower() not in stop_words]
        
        return " ".join(filtered_words) if filtered_words else query
    
    async def _analyze_intent_with_ai(self, message: str, provider: str) -> Optional[Dict[str, Any]]:
        """Use AI to analyze plugin intent."""
        
        # Create list of available plugins for AI
        plugin_descriptions = []
        for name, config in self.plugins.items():
            if config.enabled:
                keywords_str = ", ".join(config.keywords[:5])  # First 5 keywords
                plugin_descriptions.append(f"- {name}: {config.description} (keywords: {keywords_str})")
        
        prompt = f"""
Analyze this user message and determine if it requires calling an external plugin/API:

Message: "{message}"

Available plugins:
{chr(10).join(plugin_descriptions)}

If the message requires a plugin, respond with JSON:
{{
  "requires_plugin": true,
  "plugin_name": "plugin_name",
  "search_query": "extracted search terms",
  "confidence": 0.8,
  "reasoning": "why this plugin was chosen"
}}

If no plugin is needed, respond with:
{{
  "requires_plugin": false,
  "reasoning": "why no plugin is needed"
}}

Focus on the user's intent and what they're trying to accomplish.
"""
        
        try:
            response = await generate_response(
                [{"role": "user", "content": prompt}],
                provider=provider,
                temperature=0.3
            )
            
            # Parse AI response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                intent_data = json.loads(json_match.group(0))
                
                if intent_data.get("requires_plugin"):
                    plugin_name = intent_data.get("plugin_name")
                    if plugin_name in self.plugins:
                        return {
                            "plugin_name": plugin_name,
                            "plugin_config": self.plugins[plugin_name],
                            "search_query": intent_data.get("search_query", ""),
                            "confidence": intent_data.get("confidence", 0.5),
                            "reasoning": intent_data.get("reasoning", "")
                        }
            
            return None
            
        except Exception as e:
            app_logger.error(f"AI intent analysis failed: {e}")
            return None
    
    async def execute_plugin(
        self,
        plugin_intent: Dict[str, Any],
        max_results: int = 5
    ) -> PluginResult:
        """
        Execute a plugin based on intent.
        
        Args:
            plugin_intent: Plugin intent information
            max_results: Maximum number of results to return
        
        Returns:
            PluginResult with the execution result
        """
        plugin_name = plugin_intent["plugin_name"]
        plugin_config = plugin_intent["plugin_config"]
        search_query = plugin_intent["search_query"]
        
        try:
            # Check rate limiting
            if not self._check_rate_limit(plugin_name):
                return PluginResult(
                    plugin_name=plugin_name,
                    success=False,
                    content_type="error",
                    error_message="Rate limit exceeded. Please try again later."
                )
            
            # Execute specific plugin
            if plugin_name == "huggingface_audio":
                return await self._execute_huggingface_audio(search_query, plugin_config)
            elif plugin_name == "giphy":
                return await self._execute_giphy(search_query, plugin_config, max_results)
            elif plugin_name == "tenor":
                return await self._execute_tenor(search_query, plugin_config, max_results)
            elif plugin_name == "unsplash":
                return await self._execute_unsplash(search_query, plugin_config, max_results)
            elif plugin_name == "youtube_search":
                return await self._execute_youtube_search(search_query, plugin_config, max_results)
            elif plugin_name == "weather":
                return await self._execute_weather(search_query, plugin_config)
            elif plugin_name == "news":
                return await self._execute_news(search_query, plugin_config, max_results)
            elif plugin_name == "cat_facts":
                return await self._execute_cat_facts(plugin_config)
            elif plugin_name == "jokes":
                return await self._execute_jokes(plugin_config)
            elif plugin_name == "quotes":
                return await self._execute_quotes(plugin_config)
            else:
                return PluginResult(
                    plugin_name=plugin_name,
                    success=False,
                    content_type="error",
                    error_message=f"Plugin {plugin_name} not implemented"
                )
                
        except Exception as e:
            app_logger.error(f"Plugin execution failed for {plugin_name}: {e}")
            return PluginResult(
                plugin_name=plugin_name,
                success=False,
                content_type="error",
                error_message=str(e)
            )
    
    def _check_rate_limit(self, plugin_name: str) -> bool:
        """Check if plugin is within rate limits."""
        now = datetime.now()
        
        if plugin_name not in self.rate_limits:
            self.rate_limits[plugin_name] = []
        
        # Remove old requests (older than 1 minute)
        self.rate_limits[plugin_name] = [
            timestamp for timestamp in self.rate_limits[plugin_name]
            if (now - timestamp).total_seconds() < 60
        ]
        
        # Check if under limit
        plugin_config = self.plugins[plugin_name]
        if len(self.rate_limits[plugin_name]) >= plugin_config.rate_limit:
            return False
        
        # Add current request
        self.rate_limits[plugin_name].append(now)
        return True
    
    async def _execute_huggingface_audio(self, query: str, config: PluginConfig) -> PluginResult:
        """Execute Hugging Face audio generation."""
        try:
            # Get API key
            api_key = self.settings.HUGGINGFACE_API_KEY
            if not api_key:
                return PluginResult(
                    plugin_name=config.name,
                    success=False,
                    content_type="error",
                    error_message="Hugging Face API key not configured"
                )
            
            headers = {"Authorization": f"Bearer {api_key}"}
            payload = {"inputs": query or "happy upbeat music"}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        audio_data = await response.read()
                        
                        # Save audio file
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"generated_audio_{timestamp}.wav"
                        audio_path = Path("data/audio") / filename
                        audio_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        with open(audio_path, 'wb') as f:
                            f.write(audio_data)
                        
                        # Create embed HTML
                        embed_html = self._create_audio_embed(str(audio_path), query)
                        
                        return PluginResult(
                            plugin_name=config.name,
                            success=True,
                            content_type="audio",
                            content_url=str(audio_path),
                            content_data=audio_data,
                            metadata={"query": query, "filename": filename},
                            embed_html=embed_html
                        )
                    else:
                        error_text = await response.text()
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"API error: {response.status} - {error_text}"
                        )
                        
        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_giphy(self, query: str, config: PluginConfig, max_results: int) -> PluginResult:
        """Execute Giphy GIF search."""
        try:
            api_key = self.settings.GIPHY_API_KEY or "demo_api_key"  # Giphy has demo key

            params = {
                "api_key": api_key,
                "q": query or "funny",
                "limit": max_results,
                "rating": "pg-13"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        gifs = data.get("data", [])

                        if gifs:
                            # Create embed HTML for multiple GIFs
                            embed_html = self._create_gif_gallery_embed(gifs, query)

                            return PluginResult(
                                plugin_name=config.name,
                                success=True,
                                content_type="gif",
                                metadata={
                                    "query": query,
                                    "count": len(gifs),
                                    "gifs": [{"url": gif["images"]["fixed_height"]["url"],
                                            "title": gif.get("title", "")} for gif in gifs]
                                },
                                embed_html=embed_html
                            )
                        else:
                            return PluginResult(
                                plugin_name=config.name,
                                success=False,
                                content_type="error",
                                error_message=f"No GIFs found for '{query}'"
                            )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"Giphy API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_tenor(self, query: str, config: PluginConfig, max_results: int) -> PluginResult:
        """Execute Tenor GIF search."""
        try:
            api_key = self.settings.TENOR_API_KEY
            if not api_key:
                return PluginResult(
                    plugin_name=config.name,
                    success=False,
                    content_type="error",
                    error_message="Tenor API key not configured"
                )

            params = {
                "key": api_key,
                "q": query or "happy",
                "limit": max_results,
                "contentfilter": "medium"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        gifs = data.get("results", [])

                        if gifs:
                            embed_html = self._create_tenor_embed(gifs, query)

                            return PluginResult(
                                plugin_name=config.name,
                                success=True,
                                content_type="gif",
                                metadata={
                                    "query": query,
                                    "count": len(gifs),
                                    "gifs": [{"url": gif["media_formats"]["gif"]["url"],
                                            "title": gif.get("content_description", "")} for gif in gifs]
                                },
                                embed_html=embed_html
                            )
                        else:
                            return PluginResult(
                                plugin_name=config.name,
                                success=False,
                                content_type="error",
                                error_message=f"No GIFs found for '{query}'"
                            )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"Tenor API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_unsplash(self, query: str, config: PluginConfig, max_results: int) -> PluginResult:
        """Execute Unsplash photo search."""
        try:
            api_key = self.settings.UNSPLASH_API_KEY
            if not api_key:
                return PluginResult(
                    plugin_name=config.name,
                    success=False,
                    content_type="error",
                    error_message="Unsplash API key not configured"
                )

            headers = {"Authorization": f"Client-ID {api_key}"}
            params = {
                "query": query or "nature",
                "per_page": max_results,
                "orientation": "landscape"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        photos = data.get("results", [])

                        if photos:
                            embed_html = self._create_photo_gallery_embed(photos, query)

                            return PluginResult(
                                plugin_name=config.name,
                                success=True,
                                content_type="image",
                                metadata={
                                    "query": query,
                                    "count": len(photos),
                                    "photos": [{"url": photo["urls"]["regular"],
                                              "thumb": photo["urls"]["thumb"],
                                              "description": photo.get("description", ""),
                                              "photographer": photo["user"]["name"]} for photo in photos]
                                },
                                embed_html=embed_html
                            )
                        else:
                            return PluginResult(
                                plugin_name=config.name,
                                success=False,
                                content_type="error",
                                error_message=f"No photos found for '{query}'"
                            )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"Unsplash API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_youtube_search(self, query: str, config: PluginConfig, max_results: int) -> PluginResult:
        """Execute YouTube video search."""
        try:
            api_key = self.settings.YOUTUBE_API_KEY
            if not api_key:
                return PluginResult(
                    plugin_name=config.name,
                    success=False,
                    content_type="error",
                    error_message="YouTube API key not configured"
                )

            params = {
                "key": api_key,
                "q": query or "music",
                "part": "snippet",
                "type": "video",
                "maxResults": max_results,
                "safeSearch": "moderate"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        videos = data.get("items", [])

                        if videos:
                            embed_html = self._create_youtube_embed(videos, query)

                            return PluginResult(
                                plugin_name=config.name,
                                success=True,
                                content_type="video",
                                metadata={
                                    "query": query,
                                    "count": len(videos),
                                    "videos": [{"id": video["id"]["videoId"],
                                              "title": video["snippet"]["title"],
                                              "description": video["snippet"]["description"],
                                              "thumbnail": video["snippet"]["thumbnails"]["medium"]["url"]} for video in videos]
                                },
                                embed_html=embed_html
                            )
                        else:
                            return PluginResult(
                                plugin_name=config.name,
                                success=False,
                                content_type="error",
                                error_message=f"No videos found for '{query}'"
                            )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"YouTube API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_weather(self, query: str, config: PluginConfig) -> PluginResult:
        """Execute weather API."""
        try:
            api_key = self.settings.OPENWEATHER_API_KEY
            if not api_key:
                return PluginResult(
                    plugin_name=config.name,
                    success=False,
                    content_type="error",
                    error_message="OpenWeather API key not configured"
                )

            params = {
                "q": query or "London",
                "appid": api_key,
                "units": "metric"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        embed_html = self._create_weather_embed(data, query)

                        return PluginResult(
                            plugin_name=config.name,
                            success=True,
                            content_type="json",
                            metadata=data,
                            embed_html=embed_html
                        )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"Weather API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_news(self, query: str, config: PluginConfig, max_results: int) -> PluginResult:
        """Execute news API."""
        try:
            api_key = self.settings.NEWS_API_KEY
            if not api_key:
                return PluginResult(
                    plugin_name=config.name,
                    success=False,
                    content_type="error",
                    error_message="News API key not configured"
                )

            params = {
                "q": query or "technology",
                "apiKey": api_key,
                "pageSize": max_results,
                "sortBy": "publishedAt"
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        articles = data.get("articles", [])

                        if articles:
                            embed_html = self._create_news_embed(articles, query)

                            return PluginResult(
                                plugin_name=config.name,
                                success=True,
                                content_type="json",
                                metadata={"articles": articles, "query": query},
                                embed_html=embed_html
                            )
                        else:
                            return PluginResult(
                                plugin_name=config.name,
                                success=False,
                                content_type="error",
                                error_message=f"No news found for '{query}'"
                            )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"News API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_cat_facts(self, config: PluginConfig) -> PluginResult:
        """Execute cat facts API."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint) as response:
                    if response.status == 200:
                        data = await response.json()
                        embed_html = self._create_fact_embed(data.get("fact", ""), "Cat Fact")

                        return PluginResult(
                            plugin_name=config.name,
                            success=True,
                            content_type="json",
                            metadata=data,
                            embed_html=embed_html
                        )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"Cat Facts API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_jokes(self, config: PluginConfig) -> PluginResult:
        """Execute jokes API."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint) as response:
                    if response.status == 200:
                        data = await response.json()
                        joke_text = f"{data.get('setup', '')} {data.get('punchline', '')}"
                        embed_html = self._create_joke_embed(data)

                        return PluginResult(
                            plugin_name=config.name,
                            success=True,
                            content_type="json",
                            metadata=data,
                            embed_html=embed_html
                        )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"Jokes API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    async def _execute_quotes(self, config: PluginConfig) -> PluginResult:
        """Execute quotes API."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(config.api_endpoint) as response:
                    if response.status == 200:
                        data = await response.json()
                        embed_html = self._create_quote_embed(data)

                        return PluginResult(
                            plugin_name=config.name,
                            success=True,
                            content_type="json",
                            metadata=data,
                            embed_html=embed_html
                        )
                    else:
                        return PluginResult(
                            plugin_name=config.name,
                            success=False,
                            content_type="error",
                            error_message=f"Quotes API error: {response.status}"
                        )

        except Exception as e:
            return PluginResult(
                plugin_name=config.name,
                success=False,
                content_type="error",
                error_message=str(e)
            )

    # Embed HTML creation methods
    def _create_audio_embed(self, audio_path: str, query: str) -> str:
        """Create HTML embed for audio."""
        # Convert to base64 for embedding
        try:
            with open(audio_path, 'rb') as f:
                audio_data = base64.b64encode(f.read()).decode()

            return f"""
            <div style="margin: 20px 0; padding: 20px; border: 2px solid #4CAF50; border-radius: 12px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                <h3 style="margin-top: 0; color: #2E7D32; display: flex; align-items: center;">
                    🎵 Generated Audio: {query}
                </h3>
                <audio controls style="width: 100%; margin: 10px 0;">
                    <source src="data:audio/wav;base64,{audio_data}" type="audio/wav">
                    Your browser does not support the audio element.
                </audio>
                <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">
                    Generated using Hugging Face MusicGen
                </p>
            </div>
            """
        except Exception as e:
            return f"""
            <div style="margin: 20px 0; padding: 15px; border: 1px solid #f44336; border-radius: 8px; background: #ffebee;">
                <p style="color: #d32f2f; margin: 0;">🎵 Audio file: {audio_path}</p>
                <p style="color: #666; margin: 5px 0 0 0; font-size: 0.9em;">Error loading audio: {e}</p>
            </div>
            """

    def _create_gif_gallery_embed(self, gifs: List[Dict], query: str) -> str:
        """Create HTML embed for GIF gallery."""
        gif_html = ""
        for i, gif in enumerate(gifs[:3]):  # Show first 3 GIFs
            gif_url = gif["images"]["fixed_height"]["url"]
            title = gif.get("title", f"GIF {i+1}")

            gif_html += f"""
                <div style="margin: 10px 0; text-align: center;">
                    <img src="{gif_url}" alt="{title}" style="max-width: 100%; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9em;">{title}</p>
                </div>
            """

        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FF9800; border-radius: 12px; background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);">
            <h3 style="margin-top: 0; color: #E65100; display: flex; align-items: center;">
                🎭 GIFs for "{query}"
            </h3>
            {gif_html}
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">
                Powered by Giphy • Showing {len(gifs[:3])} of {len(gifs)} results
            </p>
        </div>
        """

    def _create_tenor_embed(self, gifs: List[Dict], query: str) -> str:
        """Create HTML embed for Tenor GIFs."""
        gif_html = ""
        for i, gif in enumerate(gifs[:3]):
            gif_url = gif["media_formats"]["gif"]["url"]
            title = gif.get("content_description", f"GIF {i+1}")

            gif_html += f"""
                <div style="margin: 10px 0; text-align: center;">
                    <img src="{gif_url}" alt="{title}" style="max-width: 100%; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9em;">{title}</p>
                </div>
            """

        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #9C27B0; border-radius: 12px; background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);">
            <h3 style="margin-top: 0; color: #6A1B9A; display: flex; align-items: center;">
                🎬 Tenor GIFs for "{query}"
            </h3>
            {gif_html}
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">
                Powered by Tenor • Showing {len(gifs[:3])} of {len(gifs)} results
            </p>
        </div>
        """

    def _create_photo_gallery_embed(self, photos: List[Dict], query: str) -> str:
        """Create HTML embed for photo gallery."""
        photo_html = ""
        for photo in photos[:3]:  # Show first 3 photos
            photo_url = photo["urls"]["regular"]
            thumb_url = photo["urls"]["thumb"]
            description = photo.get("description", "")
            photographer = photo["user"]["name"]

            photo_html += f"""
                <div style="margin: 15px 0; text-align: center;">
                    <img src="{thumb_url}" alt="{description}" style="max-width: 100%; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); cursor: pointer;"
                         onclick="window.open('{photo_url}', '_blank')">
                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9em;">
                        {description[:50]}{'...' if len(description) > 50 else ''}<br>
                        📸 by {photographer}
                    </p>
                </div>
            """

        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #2196F3; border-radius: 12px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
            <h3 style="margin-top: 0; color: #0D47A1; display: flex; align-items: center;">
                📷 Photos for "{query}"
            </h3>
            {photo_html}
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">
                Powered by Unsplash • Showing {len(photos[:3])} of {len(photos)} results • Click to view full size
            </p>
        </div>
        """

    def _create_youtube_embed(self, videos: List[Dict], query: str) -> str:
        """Create HTML embed for YouTube videos."""
        video_html = ""
        for video in videos[:2]:  # Show first 2 videos
            video_id = video["id"]["videoId"]
            title = video["snippet"]["title"]
            description = video["snippet"]["description"]
            thumbnail = video["snippet"]["thumbnails"]["medium"]["url"]

            video_html += f"""
                <div style="margin: 15px 0; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                    <iframe width="100%" height="200" src="https://www.youtube.com/embed/{video_id}"
                            frameborder="0" allowfullscreen style="border-radius: 8px 8px 0 0;"></iframe>
                    <div style="padding: 10px;">
                        <h4 style="margin: 0 0 5px 0; color: #333;">{title[:60]}{'...' if len(title) > 60 else ''}</h4>
                        <p style="margin: 0; color: #666; font-size: 0.9em;">{description[:100]}{'...' if len(description) > 100 else ''}</p>
                    </div>
                </div>
            """

        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FF0000; border-radius: 12px; background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);">
            <h3 style="margin-top: 0; color: #C62828; display: flex; align-items: center;">
                📺 YouTube Videos for "{query}"
            </h3>
            {video_html}
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">
                Powered by YouTube • Showing {len(videos[:2])} of {len(videos)} results
            </p>
        </div>
        """

    def _create_weather_embed(self, weather_data: Dict, query: str) -> str:
        """Create HTML embed for weather information."""
        try:
            city = weather_data["name"]
            country = weather_data["sys"]["country"]
            temp = weather_data["main"]["temp"]
            feels_like = weather_data["main"]["feels_like"]
            humidity = weather_data["main"]["humidity"]
            description = weather_data["weather"][0]["description"].title()
            icon = weather_data["weather"][0]["icon"]

            return f"""
            <div style="margin: 20px 0; padding: 20px; border: 2px solid #03A9F4; border-radius: 12px; background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);">
                <h3 style="margin-top: 0; color: #0277BD; display: flex; align-items: center;">
                    🌤️ Weather in {city}, {country}
                </h3>
                <div style="display: flex; align-items: center; margin: 15px 0;">
                    <img src="https://openweathermap.org/img/w/{icon}.png" alt="{description}" style="width: 50px; height: 50px;">
                    <div style="margin-left: 15px;">
                        <div style="font-size: 2em; font-weight: bold; color: #0277BD;">{temp:.1f}°C</div>
                        <div style="color: #666;">{description}</div>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 15px;">
                    <div style="background: rgba(255,255,255,0.7); padding: 10px; border-radius: 6px;">
                        <strong>Feels like:</strong> {feels_like:.1f}°C
                    </div>
                    <div style="background: rgba(255,255,255,0.7); padding: 10px; border-radius: 6px;">
                        <strong>Humidity:</strong> {humidity}%
                    </div>
                </div>
                <p style="margin-bottom: 0; color: #666; font-size: 0.9em; margin-top: 10px;">
                    Powered by OpenWeatherMap
                </p>
            </div>
            """
        except Exception as e:
            return f"""
            <div style="margin: 20px 0; padding: 15px; border: 1px solid #f44336; border-radius: 8px; background: #ffebee;">
                <p style="color: #d32f2f; margin: 0;">🌤️ Weather data error: {e}</p>
            </div>
            """

    def _create_news_embed(self, articles: List[Dict], query: str) -> str:
        """Create HTML embed for news articles."""
        article_html = ""
        for article in articles[:3]:  # Show first 3 articles
            title = article.get("title", "No title")
            description = article.get("description", "")
            url = article.get("url", "#")
            source = article.get("source", {}).get("name", "Unknown")
            published = article.get("publishedAt", "")

            # Format date
            if published:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(published.replace('Z', '+00:00'))
                    published_str = dt.strftime("%B %d, %Y at %H:%M")
                except:
                    published_str = published
            else:
                published_str = "Unknown date"

            article_html += f"""
                <div style="margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: white;">
                    <h4 style="margin: 0 0 10px 0; color: #333;">
                        <a href="{url}" target="_blank" style="color: #1976D2; text-decoration: none;">{title}</a>
                    </h4>
                    <p style="margin: 0 0 10px 0; color: #666; line-height: 1.4;">{description[:150]}{'...' if len(description) > 150 else ''}</p>
                    <div style="font-size: 0.9em; color: #999;">
                        📰 {source} • {published_str}
                    </div>
                </div>
            """

        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #4CAF50; border-radius: 12px; background: linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%);">
            <h3 style="margin-top: 0; color: #2E7D32; display: flex; align-items: center;">
                📰 Latest News: "{query}"
            </h3>
            {article_html}
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">
                Powered by NewsAPI • Showing {len(articles[:3])} of {len(articles)} articles
            </p>
        </div>
        """

    def _create_fact_embed(self, fact: str, title: str) -> str:
        """Create HTML embed for facts."""
        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FF5722; border-radius: 12px; background: linear-gradient(135deg, #fbe9e7 0%, #ffccbc 100%);">
            <h3 style="margin-top: 0; color: #D84315; display: flex; align-items: center;">
                🐱 {title}
            </h3>
            <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #FF5722;">
                <p style="margin: 0; font-size: 1.1em; line-height: 1.5; color: #333;">{fact}</p>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em; margin-top: 10px;">
                Random cat fact from catfact.ninja
            </p>
        </div>
        """

    def _create_joke_embed(self, joke_data: Dict) -> str:
        """Create HTML embed for jokes."""
        setup = joke_data.get("setup", "")
        punchline = joke_data.get("punchline", "")

        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FFC107; border-radius: 12px; background: linear-gradient(135deg, #fffde7 0%, #fff9c4 100%);">
            <h3 style="margin-top: 0; color: #F57F17; display: flex; align-items: center;">
                😄 Random Joke
            </h3>
            <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #FFC107;">
                <p style="margin: 0 0 10px 0; font-size: 1.1em; line-height: 1.5; color: #333;">{setup}</p>
                <p style="margin: 0; font-size: 1.1em; line-height: 1.5; color: #333; font-weight: bold;">{punchline}</p>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em; margin-top: 10px;">
                Random joke from official-joke-api
            </p>
        </div>
        """

    def _create_quote_embed(self, quote_data: Dict) -> str:
        """Create HTML embed for quotes."""
        content = quote_data.get("content", "")
        author = quote_data.get("author", "Unknown")

        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #9C27B0; border-radius: 12px; background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);">
            <h3 style="margin-top: 0; color: #6A1B9A; display: flex; align-items: center;">
                💭 Inspirational Quote
            </h3>
            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #9C27B0;">
                <blockquote style="margin: 0; font-size: 1.2em; line-height: 1.6; color: #333; font-style: italic;">
                    "{content}"
                </blockquote>
                <p style="margin: 15px 0 0 0; text-align: right; color: #666; font-weight: bold;">
                    — {author}
                </p>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em; margin-top: 10px;">
                Quote from Quotable API
            </p>
        </div>
        """

    def get_available_plugins(self) -> List[Dict[str, Any]]:
        """Get list of available plugins."""
        return [
            {
                "name": config.name,
                "description": config.description,
                "keywords": config.keywords,
                "content_type": config.content_type,
                "enabled": config.enabled,
                "api_key_required": config.api_key_required
            }
            for config in self.plugins.values()
        ]

    def enable_plugin(self, plugin_name: str):
        """Enable a plugin."""
        if plugin_name in self.plugins:
            self.plugins[plugin_name].enabled = True

    def disable_plugin(self, plugin_name: str):
        """Disable a plugin."""
        if plugin_name in self.plugins:
            self.plugins[plugin_name].enabled = False

    def get_plugin_status(self) -> Dict[str, bool]:
        """Get status of all plugins."""
        return {name: config.enabled for name, config in self.plugins.items()}


# Helper functions for easy usage
async def process_message_with_plugins(
    message: str,
    provider: str = "auto",
    max_results: int = 5
) -> Dict[str, Any]:
    """
    Process message and execute plugins if needed.

    Args:
        message: User message
        provider: AI provider for intent analysis
        max_results: Maximum results from plugins

    Returns:
        Dictionary with response and plugin results
    """
    manager = ExternalPluginManager()

    # Detect plugin intent
    intent = await manager.detect_plugin_intent(message, provider)

    if intent:
        # Execute plugin
        result = await manager.execute_plugin(intent, max_results)

        return {
            "has_plugin_result": True,
            "plugin_result": result,
            "intent": intent,
            "message": message
        }
    else:
        return {
            "has_plugin_result": False,
            "message": message
        }


async def search_gifs(query: str, max_results: int = 5) -> PluginResult:
    """Helper function to search for GIFs."""
    manager = ExternalPluginManager()

    intent = {
        "plugin_name": "giphy",
        "plugin_config": manager.plugins["giphy"],
        "search_query": query,
        "confidence": 1.0
    }

    return await manager.execute_plugin(intent, max_results)


async def generate_music(prompt: str) -> PluginResult:
    """Helper function to generate music."""
    manager = ExternalPluginManager()

    intent = {
        "plugin_name": "huggingface_audio",
        "plugin_config": manager.plugins["huggingface_audio"],
        "search_query": prompt,
        "confidence": 1.0
    }

    return await manager.execute_plugin(intent, 1)


async def get_weather(location: str) -> PluginResult:
    """Helper function to get weather."""
    manager = ExternalPluginManager()

    intent = {
        "plugin_name": "weather",
        "plugin_config": manager.plugins["weather"],
        "search_query": location,
        "confidence": 1.0
    }

    return await manager.execute_plugin(intent, 1)


async def search_photos(query: str, max_results: int = 5) -> PluginResult:
    """Helper function to search for photos."""
    manager = ExternalPluginManager()

    intent = {
        "plugin_name": "unsplash",
        "plugin_config": manager.plugins["unsplash"],
        "search_query": query,
        "confidence": 1.0
    }

    return await manager.execute_plugin(intent, max_results)


def create_plugin_response(plugin_result: PluginResult, original_message: str) -> str:
    """Create formatted response with plugin result."""
    if not plugin_result.success:
        return f"❌ Plugin Error: {plugin_result.error_message}"

    response_parts = []

    # Add context about the request
    response_parts.append(f"Based on your request: \"{original_message}\"")

    # Add plugin-specific response
    if plugin_result.content_type == "audio":
        response_parts.append(f"🎵 Generated audio using {plugin_result.plugin_name}")
        if plugin_result.content_url:
            response_parts.append(f"Audio saved to: {plugin_result.content_url}")

    elif plugin_result.content_type == "gif":
        count = plugin_result.metadata.get("count", 0)
        response_parts.append(f"🎭 Found {count} GIFs using {plugin_result.plugin_name}")

    elif plugin_result.content_type == "image":
        count = plugin_result.metadata.get("count", 0)
        response_parts.append(f"📷 Found {count} photos using {plugin_result.plugin_name}")

    elif plugin_result.content_type == "video":
        count = plugin_result.metadata.get("count", 0)
        response_parts.append(f"📺 Found {count} videos using {plugin_result.plugin_name}")

    elif plugin_result.content_type == "json":
        if plugin_result.plugin_name == "weather":
            response_parts.append("🌤️ Here's the current weather information")
        elif plugin_result.plugin_name == "news":
            count = len(plugin_result.metadata.get("articles", []))
            response_parts.append(f"📰 Found {count} recent news articles")
        elif plugin_result.plugin_name in ["cat_facts", "jokes", "quotes"]:
            response_parts.append(f"Here's something interesting from {plugin_result.plugin_name}")

    # Add the embed HTML if available
    if plugin_result.embed_html:
        response_parts.append(plugin_result.embed_html)

    return "\n\n".join(response_parts)


# Plugin keyword mapping for quick reference
PLUGIN_KEYWORDS = {
    "music": ["huggingface_audio"],
    "audio": ["huggingface_audio", "freesound"],
    "gif": ["giphy", "tenor"],
    "image": ["unsplash"],
    "photo": ["unsplash"],
    "video": ["youtube_search"],
    "weather": ["weather"],
    "news": ["news"],
    "joke": ["jokes"],
    "quote": ["quotes"],
    "cat": ["cat_facts"]
}
