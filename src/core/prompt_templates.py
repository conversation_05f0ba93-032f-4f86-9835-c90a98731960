"""
Advanced prompt templates for code generation and debugging.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class PromptType(Enum):
    """Types of prompts available."""
    CODE_GENERATION = "code_generation"
    DEBUG_ANALYSIS = "debug_analysis"
    CODE_REVIEW = "code_review"
    CODE_OPTIMIZATION = "code_optimization"
    DOCUMENTATION = "documentation"


@dataclass
class PromptTemplate:
    """Represents a prompt template."""
    name: str
    type: PromptType
    template: str
    variables: List[str]
    description: str
    examples: Optional[List[str]] = None


class PromptTemplateManager:
    """Manages prompt templates for different use cases."""
    
    def __init__(self):
        self.templates = self._load_default_templates()
    
    def _load_default_templates(self) -> Dict[str, PromptTemplate]:
        """Load default prompt templates."""
        templates = {}
        
        # Code Generation Templates
        templates["basic_function"] = PromptTemplate(
            name="basic_function",
            type=PromptType.CODE_GENERATION,
            template="""
You are an expert Python developer. Create a clean, efficient Python function based on the following requirements:

**Function Requirements:**
- Description: {description}
- Function name: {function_name}
- Parameters: {parameters}
- Return type: {return_type}
- Complexity level: {complexity}

**Code Standards:**
1. Follow PEP 8 style guidelines
2. Include comprehensive docstring with type hints
3. Add input validation where appropriate
4. Include error handling for edge cases
5. Use meaningful variable names
6. Add inline comments for complex logic

**Output Format:**
```python
def {function_name}({parameters}) -> {return_type}:
    \"\"\"
    {description}
    
    Args:
        {args_documentation}
    
    Returns:
        {return_documentation}
    
    Raises:
        {exceptions_documentation}
    
    Examples:
        {usage_examples}
    \"\"\"
    # Implementation here
    pass
```

**Additional Requirements:**
{additional_requirements}
""",
            variables=["description", "function_name", "parameters", "return_type", "complexity", "args_documentation", "return_documentation", "exceptions_documentation", "usage_examples", "additional_requirements"],
            description="Template for generating basic Python functions",
            examples=[
                "Create a function that calculates compound interest",
                "Create a function that validates email addresses",
                "Create a function that sorts a list of dictionaries"
            ]
        )
        
        templates["class_generator"] = PromptTemplate(
            name="class_generator",
            type=PromptType.CODE_GENERATION,
            template="""
You are an expert Python developer. Create a well-designed Python class based on the following specifications:

**Class Requirements:**
- Description: {description}
- Class name: {class_name}
- Inheritance: {inheritance}
- Attributes: {attributes}
- Methods: {methods}
- Design pattern: {design_pattern}

**Code Standards:**
1. Follow PEP 8 and Python naming conventions
2. Include comprehensive class and method docstrings
3. Implement proper `__init__` method
4. Add `__str__` and `__repr__` methods if appropriate
5. Include property decorators for getters/setters
6. Add type hints throughout
7. Implement error handling

**Output Format:**
```python
class {class_name}({inheritance}):
    \"\"\"
    {class_description}
    
    Attributes:
        {attributes_documentation}
    \"\"\"
    
    def __init__(self, {init_parameters}):
        \"\"\"Initialize {class_name}.\"\"\"
        # Implementation
        pass
    
    # Additional methods
    {methods_implementation}
```

**Design Considerations:**
{design_considerations}
""",
            variables=["description", "class_name", "inheritance", "attributes", "methods", "design_pattern", "class_description", "attributes_documentation", "init_parameters", "methods_implementation", "design_considerations"],
            description="Template for generating Python classes",
            examples=[
                "Create a BankAccount class with deposit/withdraw methods",
                "Create a DataProcessor class for handling CSV files",
                "Create a WebScraper class with rate limiting"
            ]
        )
        
        templates["api_client"] = PromptTemplate(
            name="api_client",
            type=PromptType.CODE_GENERATION,
            template="""
You are an expert Python developer specializing in API integrations. Create a robust API client based on these requirements:

**API Client Requirements:**
- Service: {service_name}
- Base URL: {base_url}
- Authentication: {auth_method}
- Endpoints: {endpoints}
- Response format: {response_format}

**Implementation Requirements:**
1. Use requests library for HTTP calls
2. Implement proper authentication handling
3. Add comprehensive error handling and retries
4. Include rate limiting if specified
5. Add request/response logging
6. Implement timeout handling
7. Add input validation
8. Include response parsing and error checking

**Code Structure:**
```python
import requests
import time
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

class {client_class_name}:
    \"\"\"
    API client for {service_name}.
    
    Handles authentication, rate limiting, and error handling.
    \"\"\"
    
    def __init__(self, api_key: str, base_url: str = "{base_url}"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        \"\"\"Configure the requests session.\"\"\"
        # Implementation
        pass
    
    # API methods
    {api_methods}
```

**Error Handling:**
{error_handling_requirements}

**Rate Limiting:**
{rate_limiting_requirements}
""",
            variables=["service_name", "base_url", "auth_method", "endpoints", "response_format", "client_class_name", "api_methods", "error_handling_requirements", "rate_limiting_requirements"],
            description="Template for generating API client classes",
            examples=[
                "Create a client for GitHub API",
                "Create a client for weather service API",
                "Create a client for payment processing API"
            ]
        )
        
        # Debug Analysis Templates
        templates["error_analysis"] = PromptTemplate(
            name="error_analysis",
            type=PromptType.DEBUG_ANALYSIS,
            template="""
You are an expert Python debugger and code analyst. Analyze the following error and provide comprehensive debugging assistance.

**Error Information:**
```
{error_log}
```

**Code Context:**
```python
{code_context}
```

**Analysis Framework:**
1. **Root Cause Analysis**: Identify the fundamental reason for the error
2. **Error Classification**: Categorize the error type and severity
3. **Impact Assessment**: Evaluate the scope and consequences
4. **Solution Strategy**: Provide step-by-step fixes
5. **Prevention Measures**: Suggest ways to avoid similar issues

**Required Output:**

**ROOT_CAUSE:**
[Explain the fundamental reason for this error in simple terms]

**ERROR_CLASSIFICATION:**
- Type: {error_type}
- Category: [Runtime/Syntax/Logic/Import/Type]
- Severity: [Low/Medium/High/Critical]
- Scope: [Local/Module/Application/System]

**DETAILED_EXPLANATION:**
[Provide a thorough explanation of why this error occurred, including the sequence of events that led to it]

**IMMEDIATE_FIXES:**
1. [Specific, actionable fix #1]
2. [Specific, actionable fix #2]
3. [Additional fixes if needed]

**CODE_SOLUTION:**
```python
# Corrected code with explanatory comments
{corrected_code}
```

**PREVENTION_STRATEGIES:**
1. [How to prevent this error in the future]
2. [Best practices to follow]
3. [Code patterns to adopt]
4. [Testing strategies]

**DEBUGGING_TECHNIQUES:**
1. [Debugging methods for this type of error]
2. [Tools and techniques to use]
3. [Logging and monitoring suggestions]

**RELATED_ISSUES:**
- [Common related problems]
- [Similar error patterns to watch for]

**CONFIDENCE_LEVEL:** [0-100]%
**ESTIMATED_FIX_TIME:** [Time estimate to implement fixes]
""",
            variables=["error_log", "code_context", "error_type", "corrected_code"],
            description="Comprehensive template for error analysis and debugging",
            examples=[
                "Analyze a NameError with variable scope issues",
                "Debug a TypeError in data processing",
                "Investigate an ImportError with missing dependencies"
            ]
        )
        
        templates["performance_analysis"] = PromptTemplate(
            name="performance_analysis",
            type=PromptType.CODE_OPTIMIZATION,
            template="""
You are an expert Python performance analyst. Analyze the provided code for performance issues and optimization opportunities.

**Code to Analyze:**
```python
{code_to_analyze}
```

**Performance Context:**
- Expected data size: {data_size}
- Performance requirements: {performance_requirements}
- Current bottlenecks: {current_bottlenecks}
- Target environment: {target_environment}

**Analysis Areas:**
1. **Time Complexity**: Analyze algorithmic efficiency
2. **Space Complexity**: Evaluate memory usage
3. **I/O Operations**: Check file/network operations
4. **Data Structures**: Assess data structure choices
5. **Library Usage**: Review library and framework usage

**Output Format:**

**PERFORMANCE_ASSESSMENT:**
- Current Time Complexity: O({time_complexity})
- Current Space Complexity: O({space_complexity})
- Estimated Performance: {performance_rating}/10

**BOTTLENECK_ANALYSIS:**
1. [Primary bottleneck with explanation]
2. [Secondary bottleneck with explanation]
3. [Additional issues if any]

**OPTIMIZATION_RECOMMENDATIONS:**
1. **High Impact:**
   - [Optimization #1 with expected improvement]
   - [Optimization #2 with expected improvement]

2. **Medium Impact:**
   - [Optimization #3 with expected improvement]
   - [Optimization #4 with expected improvement]

3. **Low Impact:**
   - [Minor optimizations]

**OPTIMIZED_CODE:**
```python
# Optimized version with performance improvements
{optimized_code}
```

**PERFORMANCE_COMPARISON:**
- Before: {before_metrics}
- After: {after_metrics}
- Improvement: {improvement_percentage}%

**MONITORING_SUGGESTIONS:**
1. [Metrics to track]
2. [Profiling tools to use]
3. [Performance testing strategies]
""",
            variables=["code_to_analyze", "data_size", "performance_requirements", "current_bottlenecks", "target_environment", "time_complexity", "space_complexity", "performance_rating", "optimized_code", "before_metrics", "after_metrics", "improvement_percentage"],
            description="Template for performance analysis and optimization",
            examples=[
                "Optimize a data processing pipeline",
                "Improve algorithm efficiency",
                "Reduce memory usage in large datasets"
            ]
        )
        
        return templates
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """Get a specific template by name."""
        return self.templates.get(template_name)
    
    def list_templates(self, prompt_type: Optional[PromptType] = None) -> List[PromptTemplate]:
        """List all templates, optionally filtered by type."""
        if prompt_type:
            return [t for t in self.templates.values() if t.type == prompt_type]
        return list(self.templates.values())
    
    def render_template(self, template_name: str, **kwargs) -> str:
        """Render a template with provided variables."""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        # Check for missing required variables
        missing_vars = []
        for var in template.variables:
            if var not in kwargs:
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")
        
        # Render template
        try:
            return template.template.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Template rendering failed: {e}")
    
    def add_custom_template(self, template: PromptTemplate):
        """Add a custom template."""
        self.templates[template.name] = template
    
    def create_code_generation_prompt(
        self,
        description: str,
        category: str = "general",
        complexity: str = "medium",
        additional_requirements: str = "",
        **kwargs
    ) -> str:
        """Create a code generation prompt with smart template selection."""
        
        # Select appropriate template based on description keywords
        template_name = "basic_function"  # default
        
        if any(word in description.lower() for word in ["class", "object", "inheritance"]):
            template_name = "class_generator"
        elif any(word in description.lower() for word in ["api", "client", "request", "endpoint"]):
            template_name = "api_client"
        
        # Prepare template variables
        template_vars = {
            "description": description,
            "complexity": complexity,
            "additional_requirements": additional_requirements,
            **kwargs
        }
        
        # Add default values for missing variables
        template = self.get_template(template_name)
        if template:
            for var in template.variables:
                if var not in template_vars:
                    template_vars[var] = self._get_default_value(var, category, complexity)
        
        return self.render_template(template_name, **template_vars)
    
    def _get_default_value(self, variable: str, category: str, complexity: str) -> str:
        """Get default value for template variables."""
        defaults = {
            "function_name": "generated_function",
            "parameters": "data",
            "return_type": "Any",
            "class_name": "GeneratedClass",
            "inheritance": "",
            "attributes": "Basic attributes",
            "methods": "Standard methods",
            "design_pattern": "Standard OOP",
            "args_documentation": "data: Input data to process",
            "return_documentation": "Processed result",
            "exceptions_documentation": "ValueError: If input is invalid",
            "usage_examples": ">>> result = function(data)",
            "additional_requirements": f"Category: {category}, Complexity: {complexity}",
            "service_name": "API Service",
            "base_url": "https://api.example.com",
            "auth_method": "API Key",
            "endpoints": "Standard REST endpoints",
            "response_format": "JSON",
            "client_class_name": "APIClient",
            "api_methods": "Standard CRUD methods",
            "error_handling_requirements": "Handle HTTP errors and timeouts",
            "rate_limiting_requirements": "Implement basic rate limiting"
        }
        
        return defaults.get(variable, "")


# Global instance
prompt_manager = PromptTemplateManager()
