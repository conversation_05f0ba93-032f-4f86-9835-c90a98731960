#!/usr/bin/env python3
"""
Run AI Chat System with real API integration.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def load_env_file():
    """Load environment variables from .env file."""
    env_file = Path('.env')
    if env_file.exists():
        print("🔑 Loading API keys from .env file...")
        
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')
                    
                    # Only set if not already in environment and not placeholder
                    if not os.getenv(key) and value and not value.endswith('_here'):
                        os.environ[key] = value
                        print(f"   ✅ {key}")
        
        print("🔑 API keys loaded!")
    else:
        print("❌ .env file not found. Run setup_api_keys.py first.")

async def test_real_plugin_system():
    """Test the plugin system with real APIs."""
    print("\n🔌 Testing Real Plugin System")
    print("=" * 50)
    
    try:
        from core.plugin_manager import ExternalPluginManager
        
        manager = ExternalPluginManager()
        
        # Test cases with real API calls
        test_cases = [
            ("Show me funny cat GIFs", "Should use Giphy API"),
            ("What's the weather in London?", "Should use OpenWeather API"),
            ("Generate relaxing music", "Should use Hugging Face API"),
            ("Find sunset photos", "Should use Unsplash API"),
            ("Tell me a joke", "Should use Jokes API"),
            ("Random cat fact", "Should use Cat Facts API")
        ]
        
        for message, expected in test_cases:
            print(f"\n💬 Testing: '{message}'")
            print(f"   Expected: {expected}")
            
            # Detect plugin intent
            intent = await manager.detect_plugin_intent(message)
            
            if intent:
                plugin_name = intent['plugin_name']
                confidence = intent['confidence']
                search_query = intent['search_query']
                
                print(f"   🎯 Plugin: {plugin_name}")
                print(f"   📊 Confidence: {confidence:.0%}")
                print(f"   🔍 Query: '{search_query}'")
                
                # Execute plugin with real API
                print(f"   📡 Calling real API...")
                result = await manager.execute_plugin(intent, max_results=3)
                
                if result.success:
                    print(f"   ✅ SUCCESS! Content type: {result.content_type}")
                    
                    if result.content_url:
                        print(f"   📁 Content saved: {result.content_url}")
                    
                    if result.metadata:
                        print(f"   📊 Metadata: {result.metadata}")
                    
                    if result.embed_html:
                        print(f"   🎨 HTML embed: {len(result.embed_html)} characters")
                    
                    print(f"   🎉 REAL API CALL SUCCESSFUL!")
                    
                else:
                    print(f"   ❌ Failed: {result.error_message}")
                    if "API key" in result.error_message:
                        print(f"   💡 Add API key to .env file for this service")
                    
            else:
                print(f"   ❌ No plugin detected")
            
            print()
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def interactive_chat():
    """Interactive chat with real API integration."""
    print("\n💬 Interactive Chat with Real APIs")
    print("=" * 50)
    print("Type messages to test real plugin integration.")
    print("Type 'quit' to exit, 'help' for examples.")
    print()
    
    try:
        from core.plugin_manager import ExternalPluginManager
        
        manager = ExternalPluginManager()
        
        while True:
            try:
                message = input("💬 You: ").strip()
                
                if message.lower() in ['quit', 'exit', 'q']:
                    break
                
                if message.lower() == 'help':
                    print("\n📋 Example Messages:")
                    print("  • Show me funny GIFs")
                    print("  • Generate upbeat music")
                    print("  • Weather in Tokyo")
                    print("  • Find nature photos")
                    print("  • Tell me a joke")
                    print("  • Random cat fact")
                    print("  • Latest tech news")
                    continue
                
                if not message:
                    continue
                
                print("🤖 AI: Analyzing your request...")
                
                # Detect intent
                intent = await manager.detect_plugin_intent(message)
                
                if intent:
                    plugin_name = intent['plugin_name']
                    print(f"🔌 Using {plugin_name} plugin...")
                    
                    # Execute plugin
                    result = await manager.execute_plugin(intent)
                    
                    if result.success:
                        print(f"✅ Found {result.content_type} content!")
                        
                        # Show different responses based on content type
                        if result.content_type == "gif":
                            print("🎭 Here are some great GIFs for you!")
                        elif result.content_type == "audio":
                            print("🎵 I've generated audio for you!")
                        elif result.content_type == "image":
                            print("📷 Found some beautiful photos!")
                        elif result.content_type == "json":
                            if "weather" in plugin_name:
                                print("🌤️ Here's the weather information!")
                            elif "joke" in plugin_name:
                                print("😄 Here's a joke for you!")
                            elif "cat" in plugin_name:
                                print("🐱 Here's a cat fact!")
                            elif "news" in plugin_name:
                                print("📰 Here are the latest news!")
                        
                        # Show metadata if available
                        if result.metadata:
                            for key, value in result.metadata.items():
                                if isinstance(value, str) and len(value) < 100:
                                    print(f"   {key}: {value}")
                        
                        print("🎨 [Rich content would be displayed in web interface]")
                        
                    else:
                        print(f"❌ Sorry, I couldn't get that content: {result.error_message}")
                        
                        if "API key" in result.error_message:
                            print("💡 This service requires an API key. Run setup_api_keys.py to configure it.")
                
                else:
                    print("💬 I can help you with GIFs, music, weather, photos, jokes, and more!")
                    print("   Try asking for something specific!")
                
                print()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error: {e}")
        
        print("\n👋 Chat ended!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

def check_api_status():
    """Check which APIs are configured and working."""
    print("🔍 API Configuration Status")
    print("=" * 40)
    
    api_keys = {
        'GROQ_API_KEY': 'Groq AI (chat responses)',
        'GIPHY_API_KEY': 'Giphy (GIFs)',
        'OPENWEATHER_API_KEY': 'OpenWeather (weather)',
        'HUGGINGFACE_API_KEY': 'Hugging Face (audio)',
        'UNSPLASH_API_KEY': 'Unsplash (photos)',
        'YOUTUBE_API_KEY': 'YouTube (videos)',
        'NEWS_API_KEY': 'News API (news)',
        'TENOR_API_KEY': 'Tenor (GIFs)'
    }
    
    configured_count = 0
    
    for key, description in api_keys.items():
        value = os.getenv(key)
        if value and value != f"your_{key.lower()}_here":
            print(f"✅ {description}")
            configured_count += 1
        else:
            print(f"❌ {description}")
    
    print(f"\n📊 Status: {configured_count}/{len(api_keys)} APIs configured")
    
    if configured_count == 0:
        print("\n⚠️ No API keys configured!")
        print("🔧 Run: python3 setup_api_keys.py")
    elif configured_count < len(api_keys):
        print(f"\n💡 {len(api_keys) - configured_count} APIs still need configuration")
        print("🔧 Run: python3 setup_api_keys.py")
    else:
        print("\n🎉 All APIs configured! System ready for full functionality!")
    
    return configured_count > 0

async def main():
    """Main function."""
    print("🚀 AI Chat System - Real API Integration")
    print("=" * 60)
    
    # Load environment variables
    load_env_file()
    
    # Check API status
    has_apis = check_api_status()
    
    if not has_apis:
        print("\n🔧 Setup Required:")
        print("1. Run: python3 setup_api_keys.py")
        print("2. Add your API keys")
        print("3. Run this script again")
        return
    
    print("\n🎯 Choose an option:")
    print("1. 🧪 Test plugin system")
    print("2. 💬 Interactive chat")
    print("3. 🌐 Start web interface")
    print("4. ❌ Exit")
    
    while True:
        choice = input("\nSelect option [1-4]: ").strip()
        
        if choice == '1':
            await test_real_plugin_system()
            break
        elif choice == '2':
            await interactive_chat()
            break
        elif choice == '3':
            print("🌐 Starting web interface...")
            print("💡 Run: streamlit run src/ui/plugin_interface.py")
            break
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please select 1-4.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
