#!/usr/bin/env python3
"""
Simple demo of the AI Chat System without external dependencies.
"""

import re
import json
from typing import Dict, List, Optional, Any
from datetime import datetime


class SimplePluginManager:
    """Simplified plugin manager for demonstration."""
    
    def __init__(self):
        self.plugins = {
            "gif_search": {
                "keywords": ["gif", "animated", "funny", "meme"],
                "description": "Search for GIFs and animated images"
            },
            "music_generator": {
                "keywords": ["music", "audio", "song", "generate", "create"],
                "description": "Generate music and audio content"
            },
            "weather": {
                "keywords": ["weather", "temperature", "forecast", "climate"],
                "description": "Get weather information"
            },
            "photo_search": {
                "keywords": ["photo", "image", "picture", "photography"],
                "description": "Search for photos and images"
            },
            "joke_generator": {
                "keywords": ["joke", "funny", "humor", "laugh"],
                "description": "Generate random jokes"
            }
        }
    
    def detect_plugin_intent(self, message: str) -> Optional[Dict[str, Any]]:
        """Detect if message requires plugin execution."""
        message_lower = message.lower()
        
        # Score plugins based on keyword matches
        plugin_scores = {}
        
        for plugin_name, plugin_config in self.plugins.items():
            score = 0
            matched_keywords = []
            
            for keyword in plugin_config["keywords"]:
                if keyword in message_lower:
                    score += len(keyword.split())
                    matched_keywords.append(keyword)
            
            if score > 0:
                plugin_scores[plugin_name] = {
                    "score": score,
                    "matched_keywords": matched_keywords,
                    "plugin_config": plugin_config
                }
        
        # Return highest scoring plugin
        if plugin_scores:
            best_plugin = max(plugin_scores.items(), key=lambda x: x[1]["score"])
            plugin_name, plugin_data = best_plugin
            
            # Extract search query
            search_query = self._extract_search_query(message, plugin_data["matched_keywords"])
            
            return {
                "plugin_name": plugin_name,
                "search_query": search_query,
                "confidence": min(plugin_data["score"] / 3.0, 1.0),
                "matched_keywords": plugin_data["matched_keywords"],
                "description": plugin_data["plugin_config"]["description"]
            }
        
        return None
    
    def _extract_search_query(self, message: str, matched_keywords: List[str]) -> str:
        """Extract search query from message."""
        query = message
        
        # Remove matched keywords
        for keyword in matched_keywords:
            query = re.sub(rf'\b{re.escape(keyword)}\b', '', query, flags=re.IGNORECASE)
        
        # Clean up the query
        query = re.sub(r'\s+', ' ', query).strip()
        
        # Remove common words
        stop_words = ["find", "search", "get", "show", "play", "generate", "create", "for", "me", "a", "an", "the"]
        words = query.split()
        filtered_words = [word for word in words if word.lower() not in stop_words]
        
        return " ".join(filtered_words) if filtered_words else query
    
    def execute_plugin(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate plugin execution."""
        plugin_name = intent["plugin_name"]
        search_query = intent["search_query"]
        
        # Simulate different plugin responses
        if plugin_name == "gif_search":
            return {
                "success": True,
                "content_type": "gif",
                "results": [
                    {"url": f"https://giphy.com/gif/{search_query}_1", "title": f"Funny {search_query} GIF 1"},
                    {"url": f"https://giphy.com/gif/{search_query}_2", "title": f"Funny {search_query} GIF 2"},
                    {"url": f"https://giphy.com/gif/{search_query}_3", "title": f"Funny {search_query} GIF 3"}
                ],
                "embed_html": self._create_gif_embed(search_query)
            }
        
        elif plugin_name == "music_generator":
            return {
                "success": True,
                "content_type": "audio",
                "results": {
                    "audio_file": f"generated_music_{search_query.replace(' ', '_')}.wav",
                    "duration": "2:30",
                    "style": search_query or "ambient"
                },
                "embed_html": self._create_audio_embed(search_query)
            }
        
        elif plugin_name == "weather":
            location = search_query or "London"
            return {
                "success": True,
                "content_type": "json",
                "results": {
                    "location": location,
                    "temperature": "22°C",
                    "condition": "Partly Cloudy",
                    "humidity": "65%",
                    "wind": "10 km/h"
                },
                "embed_html": self._create_weather_embed(location)
            }
        
        elif plugin_name == "photo_search":
            return {
                "success": True,
                "content_type": "image",
                "results": [
                    {"url": f"https://unsplash.com/photo/{search_query}_1", "photographer": "John Doe"},
                    {"url": f"https://unsplash.com/photo/{search_query}_2", "photographer": "Jane Smith"},
                    {"url": f"https://unsplash.com/photo/{search_query}_3", "photographer": "Bob Wilson"}
                ],
                "embed_html": self._create_photo_embed(search_query)
            }
        
        elif plugin_name == "joke_generator":
            return {
                "success": True,
                "content_type": "text",
                "results": {
                    "setup": "Why don't scientists trust atoms?",
                    "punchline": "Because they make up everything!",
                    "category": "science"
                },
                "embed_html": self._create_joke_embed()
            }
        
        return {"success": False, "error": f"Plugin {plugin_name} not implemented"}
    
    def _create_gif_embed(self, query: str) -> str:
        """Create HTML embed for GIFs."""
        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FF9800; border-radius: 12px; background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);">
            <h3 style="margin-top: 0; color: #E65100;">🎭 GIFs for "{query}"</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                <div style="text-align: center;">
                    <div style="width: 200px; height: 150px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        [GIF 1: Funny {query}]
                    </div>
                </div>
                <div style="text-align: center;">
                    <div style="width: 200px; height: 150px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        [GIF 2: Animated {query}]
                    </div>
                </div>
                <div style="text-align: center;">
                    <div style="width: 200px; height: 150px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        [GIF 3: Reaction {query}]
                    </div>
                </div>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">Powered by Giphy • 3 results found</p>
        </div>
        """
    
    def _create_audio_embed(self, query: str) -> str:
        """Create HTML embed for audio."""
        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #4CAF50; border-radius: 12px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
            <h3 style="margin-top: 0; color: #2E7D32;">🎵 Generated Audio: {query}</h3>
            <div style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; background: #4CAF50; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">▶</div>
                    <div style="flex: 1;">
                        <div style="background: #e0e0e0; height: 4px; border-radius: 2px; position: relative;">
                            <div style="background: #4CAF50; height: 4px; width: 30%; border-radius: 2px;"></div>
                        </div>
                        <div style="font-size: 0.9em; color: #666; margin-top: 5px;">0:45 / 2:30</div>
                    </div>
                </div>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">Generated using AI Music Generator</p>
        </div>
        """
    
    def _create_weather_embed(self, location: str) -> str:
        """Create HTML embed for weather."""
        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #03A9F4; border-radius: 12px; background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);">
            <h3 style="margin-top: 0; color: #0277BD;">🌤️ Weather in {location}</h3>
            <div style="display: flex; align-items: center; margin: 15px 0;">
                <div style="font-size: 3em; margin-right: 20px;">☁️</div>
                <div>
                    <div style="font-size: 2em; font-weight: bold; color: #0277BD;">22°C</div>
                    <div style="color: #666;">Partly Cloudy</div>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 15px;">
                <div style="background: rgba(255,255,255,0.7); padding: 10px; border-radius: 6px;">
                    <strong>Humidity:</strong> 65%
                </div>
                <div style="background: rgba(255,255,255,0.7); padding: 10px; border-radius: 6px;">
                    <strong>Wind:</strong> 10 km/h
                </div>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em; margin-top: 10px;">Powered by Weather API</p>
        </div>
        """
    
    def _create_photo_embed(self, query: str) -> str:
        """Create HTML embed for photos."""
        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #2196F3; border-radius: 12px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
            <h3 style="margin-top: 0; color: #0D47A1;">📷 Photos for "{query}"</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                <div style="text-align: center;">
                    <div style="width: 150px; height: 100px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        [Photo 1]
                    </div>
                    <p style="font-size: 0.8em; margin: 5px 0;">by John Doe</p>
                </div>
                <div style="text-align: center;">
                    <div style="width: 150px; height: 100px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        [Photo 2]
                    </div>
                    <p style="font-size: 0.8em; margin: 5px 0;">by Jane Smith</p>
                </div>
                <div style="text-align: center;">
                    <div style="width: 150px; height: 100px; background: #f0f0f0; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                        [Photo 3]
                    </div>
                    <p style="font-size: 0.8em; margin: 5px 0;">by Bob Wilson</p>
                </div>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em;">Powered by Unsplash • 3 results found</p>
        </div>
        """
    
    def _create_joke_embed(self) -> str:
        """Create HTML embed for jokes."""
        return f"""
        <div style="margin: 20px 0; padding: 20px; border: 2px solid #FFC107; border-radius: 12px; background: linear-gradient(135deg, #fffde7 0%, #fff9c4 100%);">
            <h3 style="margin-top: 0; color: #F57F17;">😄 Random Joke</h3>
            <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #FFC107;">
                <p style="margin: 0 0 10px 0; font-size: 1.1em; line-height: 1.5;">Why don't scientists trust atoms?</p>
                <p style="margin: 0; font-size: 1.1em; line-height: 1.5; font-weight: bold;">Because they make up everything!</p>
            </div>
            <p style="margin-bottom: 0; color: #666; font-size: 0.9em; margin-top: 10px;">Random joke from Joke API</p>
        </div>
        """


def demo_plugin_detection():
    """Demonstrate plugin detection."""
    print("🔌 Plugin Detection Demo")
    print("=" * 50)
    
    manager = SimplePluginManager()
    
    test_messages = [
        "Show me funny cat GIFs",
        "Generate relaxing music",
        "What's the weather like today?",
        "Find beautiful sunset photos",
        "Tell me a joke",
        "Hello, how are you?",  # Should not trigger plugin
        "Create upbeat dance music",
        "Search for mountain images",
        "Get weather forecast for Tokyo"
    ]
    
    for message in test_messages:
        print(f"\n📝 Message: '{message}'")
        
        intent = manager.detect_plugin_intent(message)
        
        if intent:
            print(f"✅ Plugin detected: {intent['plugin_name']}")
            print(f"   Description: {intent['description']}")
            print(f"   Confidence: {intent['confidence']:.0%}")
            print(f"   Search query: '{intent['search_query']}'")
            print(f"   Keywords matched: {intent['matched_keywords']}")
            
            # Execute plugin
            result = manager.execute_plugin(intent)
            
            if result["success"]:
                print(f"   ✅ Plugin executed successfully")
                print(f"   Content type: {result['content_type']}")
                
                if result["content_type"] == "gif":
                    print(f"   Found {len(result['results'])} GIFs")
                elif result["content_type"] == "audio":
                    print(f"   Generated audio: {result['results']['audio_file']}")
                elif result["content_type"] == "json":
                    if "location" in result["results"]:
                        weather = result["results"]
                        print(f"   Weather: {weather['temperature']}, {weather['condition']}")
                elif result["content_type"] == "image":
                    print(f"   Found {len(result['results'])} photos")
                elif result["content_type"] == "text":
                    joke = result["results"]
                    print(f"   Joke: {joke['setup']} {joke['punchline']}")
            else:
                print(f"   ❌ Plugin failed: {result.get('error', 'Unknown error')}")
        else:
            print("ℹ️ No plugin detected - would use regular chat AI")


def demo_html_embeds():
    """Demonstrate HTML embed generation."""
    print("\n🎨 HTML Embed Demo")
    print("=" * 50)
    
    manager = SimplePluginManager()
    
    # Test different embed types
    test_cases = [
        ("Show me cat GIFs", "gif_search"),
        ("Generate piano music", "music_generator"),
        ("Weather in Paris", "weather"),
        ("Find nature photos", "photo_search"),
        ("Tell me a joke", "joke_generator")
    ]
    
    for message, expected_plugin in test_cases:
        print(f"\n📝 Testing: '{message}'")
        
        intent = manager.detect_plugin_intent(message)
        
        if intent and intent["plugin_name"] == expected_plugin:
            result = manager.execute_plugin(intent)
            
            if result["success"] and "embed_html" in result:
                print(f"✅ HTML embed generated ({len(result['embed_html'])} characters)")
                print("📄 HTML Preview:")
                print("─" * 60)
                # Show first 200 characters of HTML
                html_preview = result["embed_html"][:200] + "..." if len(result["embed_html"]) > 200 else result["embed_html"]
                print(html_preview)
                print("─" * 60)
            else:
                print("❌ No embed generated")
        else:
            print(f"❌ Expected {expected_plugin}, got {intent['plugin_name'] if intent else 'None'}")


def demo_chat_simulation():
    """Simulate a chat conversation with plugins."""
    print("\n💬 Chat Simulation Demo")
    print("=" * 50)
    
    manager = SimplePluginManager()
    
    conversation = [
        "Hi there!",
        "Show me some funny GIFs",
        "Can you generate some relaxing music?",
        "What's the weather like?",
        "Find me some beautiful photos",
        "Tell me a joke to cheer me up",
        "Thanks, that was great!"
    ]
    
    for i, message in enumerate(conversation, 1):
        print(f"\n--- Turn {i} ---")
        print(f"User: {message}")
        
        intent = manager.detect_plugin_intent(message)
        
        if intent:
            print(f"🔌 Plugin triggered: {intent['plugin_name']}")
            
            result = manager.execute_plugin(intent)
            
            if result["success"]:
                print(f"AI: I found some great {result['content_type']} content for you!")
                
                # Simulate different responses based on content type
                if result["content_type"] == "gif":
                    print(f"    Here are {len(result['results'])} funny GIFs about '{intent['search_query']}'")
                elif result["content_type"] == "audio":
                    print(f"    I've generated a {result['results']['duration']} audio track for you")
                elif result["content_type"] == "json" and "temperature" in str(result["results"]):
                    weather = result["results"]
                    print(f"    The weather is {weather['temperature']} and {weather['condition']}")
                elif result["content_type"] == "image":
                    print(f"    I found {len(result['results'])} beautiful photos for you")
                elif result["content_type"] == "text":
                    joke = result["results"]
                    print(f"    Here's a joke: {joke['setup']} {joke['punchline']}")
                
                print("    [Rich content would be embedded here]")
            else:
                print(f"AI: Sorry, I couldn't get that content right now.")
        else:
            # Regular chat response
            responses = {
                "Hi there!": "Hello! I'm your AI assistant. I can help you find GIFs, generate music, check weather, and more!",
                "Thanks, that was great!": "You're welcome! I'm glad I could help. Feel free to ask for more content anytime!"
            }
            
            response = responses.get(message, "I'm here to help! Try asking for GIFs, music, weather, photos, or jokes.")
            print(f"AI: {response}")


def main():
    """Run all demos."""
    print("🚀 AI Chat System with External Plugins - Demo")
    print("=" * 60)
    print("This demo shows how the system detects user intent and calls appropriate plugins")
    print("to fetch and embed multimedia content in chat responses.\n")
    
    try:
        demo_plugin_detection()
        demo_html_embeds()
        demo_chat_simulation()
        
        print("\n" + "=" * 60)
        print("✅ Demo completed successfully!")
        print("\n📋 Summary of Features Demonstrated:")
        print("1. 🎯 Automatic plugin detection from user messages")
        print("2. 🔌 Plugin execution with simulated API calls")
        print("3. 🎨 Rich HTML embed generation for different content types")
        print("4. 💬 Chat integration with multimedia content")
        print("5. 🛡️ Error handling and fallback responses")
        
        print("\n🚀 Next Steps:")
        print("- Configure real API keys for external services")
        print("- Run: streamlit run src/ui/plugin_interface.py")
        print("- Test with: python scripts/run_plugin_manager.py chat")
        print("- Integrate into your chat application")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
