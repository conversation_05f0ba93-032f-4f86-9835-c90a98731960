#!/usr/bin/env python3
"""
Examples of using the generate_response function.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.response_generator import (
    generate_response,
    generate_groq_response,
    generate_huggingface_response,
    generate_streaming_response,
    get_available_providers,
    validate_provider
)


async def example_basic_usage():
    """Example 1: Basic usage with auto provider selection."""
    print("🤖 Example 1: Basic Response Generation")
    print("=" * 50)
    
    # Simple conversation
    messages = [
        {"role": "user", "content": "Hello! What is artificial intelligence?"}
    ]
    
    try:
        response = await generate_response(messages)
        print(f"User: {messages[0]['content']}")
        print(f"AI: {response}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_conversation_history():
    """Example 2: Multi-turn conversation with history."""
    print("\n💬 Example 2: Conversation with History")
    print("=" * 50)
    
    # Multi-turn conversation
    messages = [
        {"role": "user", "content": "What is Python?"},
        {"role": "assistant", "content": "Python is a high-level programming language known for its simplicity and readability."},
        {"role": "user", "content": "Can you give me a simple Python example?"}
    ]
    
    try:
        response = await generate_response(messages, provider="auto")
        
        print("Conversation History:")
        for msg in messages:
            role = msg["role"].capitalize()
            print(f"{role}: {msg['content']}")
        
        print(f"AI: {response}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_specific_providers():
    """Example 3: Using specific providers."""
    print("\n🔧 Example 3: Specific Providers")
    print("=" * 50)
    
    messages = [
        {"role": "user", "content": "Explain machine learning in simple terms."}
    ]
    
    # Check available providers
    providers = await get_available_providers()
    print(f"Available providers: {providers}")
    
    # Try Groq
    if "groq" in providers:
        print("\n--- Using Groq ---")
        try:
            response = await generate_groq_response(messages)
            print(f"Groq: {response[:100]}...")
        except Exception as e:
            print(f"Groq error: {e}")
    
    # Try Hugging Face
    if "huggingface" in providers:
        print("\n--- Using Hugging Face ---")
        try:
            response = await generate_huggingface_response(messages)
            print(f"HuggingFace: {response[:100]}...")
        except Exception as e:
            print(f"HuggingFace error: {e}")


async def example_with_parameters():
    """Example 4: Using different parameters."""
    print("\n⚙️ Example 4: Custom Parameters")
    print("=" * 50)
    
    messages = [
        {"role": "user", "content": "Write a very short poem about coding."}
    ]
    
    try:
        # High creativity
        creative_response = await generate_response(
            messages, 
            temperature=0.9,
            max_tokens=100
        )
        print("High creativity (temperature=0.9):")
        print(creative_response)
        
        print("\n" + "-" * 30)
        
        # Low creativity (more focused)
        focused_response = await generate_response(
            messages,
            temperature=0.1,
            max_tokens=100
        )
        print("Low creativity (temperature=0.1):")
        print(focused_response)
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_streaming():
    """Example 5: Streaming response."""
    print("\n🌊 Example 5: Streaming Response")
    print("=" * 50)
    
    messages = [
        {"role": "user", "content": "Tell me a short story about a robot learning to paint."}
    ]
    
    try:
        print("User: Tell me a short story about a robot learning to paint.")
        print("AI: ", end="", flush=True)
        
        async for chunk in generate_streaming_response(messages):
            print(chunk, end="", flush=True)
        
        print("\n")
        
    except Exception as e:
        print(f"\n❌ Streaming error: {e}")


async def example_error_handling():
    """Example 6: Error handling and validation."""
    print("\n🛡️ Example 6: Error Handling")
    print("=" * 50)
    
    # Test invalid messages
    print("Testing invalid messages...")
    
    try:
        # Empty messages
        await generate_response([])
    except ValueError as e:
        print(f"✅ Caught expected error: {e}")
    
    try:
        # Invalid message format
        await generate_response([{"invalid": "format"}])
    except ValueError as e:
        print(f"✅ Caught expected error: {e}")
    
    try:
        # Invalid role
        await generate_response([{"role": "invalid", "content": "test"}])
    except ValueError as e:
        print(f"✅ Caught expected error: {e}")
    
    # Test provider validation
    print("\nTesting provider validation...")
    
    valid_groq = await validate_provider("groq")
    valid_hf = await validate_provider("huggingface")
    valid_invalid = await validate_provider("invalid_provider")
    
    print(f"Groq valid: {valid_groq}")
    print(f"HuggingFace valid: {valid_hf}")
    print(f"Invalid provider valid: {valid_invalid}")


async def example_real_chat_simulation():
    """Example 7: Simulate a real chat conversation."""
    print("\n💭 Example 7: Real Chat Simulation")
    print("=" * 50)
    
    # Start with empty conversation
    conversation = []
    
    # Simulate user messages
    user_messages = [
        "Hi there! I'm learning Python programming.",
        "What are the most important concepts I should focus on?",
        "Can you explain what functions are?",
        "Thank you! That was very helpful."
    ]
    
    for user_msg in user_messages:
        # Add user message
        conversation.append({"role": "user", "content": user_msg})
        
        print(f"\nUser: {user_msg}")
        
        try:
            # Generate AI response
            ai_response = await generate_response(conversation.copy())
            
            # Add AI response to conversation
            conversation.append({"role": "assistant", "content": ai_response})
            
            print(f"AI: {ai_response}")
            
        except Exception as e:
            print(f"AI: Sorry, I encountered an error: {e}")
            break
    
    print(f"\nFinal conversation length: {len(conversation)} messages")


async def example_provider_comparison():
    """Example 8: Compare responses from different providers."""
    print("\n🔍 Example 8: Provider Comparison")
    print("=" * 50)
    
    messages = [
        {"role": "user", "content": "What are the benefits of renewable energy?"}
    ]
    
    providers = await get_available_providers()
    responses = {}
    
    for provider in providers:
        try:
            print(f"\nTesting {provider}...")
            response = await generate_response(messages, provider=provider)
            responses[provider] = response
            print(f"{provider.capitalize()}: {response[:150]}...")
            
        except Exception as e:
            print(f"{provider} failed: {e}")
    
    print(f"\nSuccessfully got responses from {len(responses)} providers")


async def main():
    """Run all examples."""
    print("🚀 Response Generation Examples")
    print("=" * 60)
    
    try:
        await example_basic_usage()
        await example_conversation_history()
        await example_specific_providers()
        await example_with_parameters()
        await example_streaming()
        await example_error_handling()
        await example_real_chat_simulation()
        await example_provider_comparison()
        
        print("\n✅ All examples completed!")
        
    except Exception as e:
        print(f"\n❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
