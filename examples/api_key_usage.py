#!/usr/bin/env python3
"""
Example usage of secure API key loading and client initialization.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config.logging_config import app_logger
from config.api_keys import api_key_manager
from utils.client_factory import ClientFactory, create_all_available_clients
from utils.env_loader import SecureEnvLoader
from utils.api_key_tester import test_all_api_keys


async def example_basic_usage():
    """Example: Basic API key loading and client creation."""
    print("🔑 Example 1: Basic API Key Loading")
    print("=" * 50)
    
    # Load API keys securely
    api_keys = api_key_manager.load_api_keys_from_env()
    
    print(f"Loaded API keys: {list(api_keys.keys())}")
    
    # Create client factory
    factory = ClientFactory()
    
    # Get available providers
    providers = factory.get_available_providers()
    print(f"Available providers: {providers}")
    
    # Create all available clients
    clients = await factory.create_all_clients()
    print(f"Created clients: {list(clients.keys())}")
    
    return clients


async def example_individual_clients():
    """Example: Creating individual clients with validation."""
    print("\n🤖 Example 2: Individual Client Creation")
    print("=" * 50)
    
    factory = ClientFactory()
    
    # Create Groq client
    print("Creating Groq client...")
    groq_client = factory.create_groq_client()
    if groq_client:
        print("✅ Groq client created successfully")
        
        # Test the client
        is_valid = await groq_client.validate_connection()
        print(f"Groq client validation: {'✅ PASS' if is_valid else '❌ FAIL'}")
    else:
        print("❌ Failed to create Groq client")
    
    # Create Hugging Face client
    print("\nCreating Hugging Face client...")
    hf_client = factory.create_huggingface_client()
    if hf_client:
        print("✅ Hugging Face client created successfully")
        
        # Test the client
        is_valid = await hf_client.validate_connection()
        print(f"HF client validation: {'✅ PASS' if is_valid else '❌ FAIL'}")
    else:
        print("❌ Failed to create Hugging Face client")
    
    return {"groq": groq_client, "huggingface": hf_client}


async def example_secure_key_management():
    """Example: Secure key management features."""
    print("\n🔐 Example 3: Secure Key Management")
    print("=" * 50)
    
    # Validate API key formats
    print("Validating API key formats...")
    
    # Example Groq key validation
    test_groq_key = "gsk_test123456789012345678901234567890"
    groq_validation = api_key_manager.validate_groq_api_key(test_groq_key)
    print(f"Groq key validation: {groq_validation}")
    
    # Example HF key validation
    test_hf_key = "hf_test123456789012345678901234567890"
    hf_validation = api_key_manager.validate_huggingface_api_key(test_hf_key)
    print(f"HF key validation: {hf_validation}")
    
    # Demonstrate key masking
    print(f"Masked Groq key: {groq_validation['masked_key']}")
    print(f"Masked HF key: {hf_validation['masked_key']}")


async def example_environment_loading():
    """Example: Environment variable loading."""
    print("\n🌍 Example 4: Environment Loading")
    print("=" * 50)
    
    # Create secure environment loader
    env_loader = SecureEnvLoader()
    
    # Load environment variables
    env_vars = env_loader.load_environment()
    
    # Get specific variables
    app_name = env_loader.get_var("APP_NAME", "Chat AI")
    debug_mode = env_loader.get_bool("DEBUG", False)
    port = env_loader.get_int("PORT", 8000)
    
    print(f"App Name: {app_name}")
    print(f"Debug Mode: {debug_mode}")
    print(f"Port: {port}")
    
    # Validate API key formats
    groq_valid = env_loader.validate_api_key_format("GROQ_API_KEY", "gsk_")
    hf_valid = env_loader.validate_api_key_format("HUGGINGFACE_API_KEY", "hf_")
    
    print(f"Groq API key valid: {groq_valid}")
    print(f"HF API key valid: {hf_valid}")


async def example_client_testing():
    """Example: Testing API clients."""
    print("\n🧪 Example 5: Client Testing")
    print("=" * 50)
    
    # Test all API keys
    print("Testing all API keys...")
    test_results = await test_all_api_keys()
    
    for provider, result in test_results.items():
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"{provider.upper()}: {status}")
        
        if result["success"]:
            response_time = result.get("response_time", 0)
            print(f"  Response time: {response_time:.2f}s")
            
            if result.get("model_info"):
                model_info = result["model_info"]
                print(f"  Model: {model_info.get('model_name', 'Unknown')}")
                print(f"  Sample: {model_info.get('sample_response', 'N/A')}")
        else:
            print(f"  Error: {result.get('error', 'Unknown')}")


async def example_chat_conversation():
    """Example: Simple chat conversation."""
    print("\n💬 Example 6: Chat Conversation")
    print("=" * 50)
    
    # Create clients
    clients = await create_all_available_clients()
    
    if not clients:
        print("❌ No clients available for chat")
        return
    
    # Use the first available client
    provider_name = list(clients.keys())[0]
    client = clients[provider_name]
    
    print(f"Using {provider_name} for conversation...")
    
    # Simple conversation
    conversation = [
        {"role": "user", "content": "Hello! What's your name?"}
    ]
    
    try:
        response = await client.generate_response(conversation)
        print(f"User: {conversation[0]['content']}")
        print(f"AI: {response}")
        
        # Continue conversation
        conversation.append({"role": "assistant", "content": response})
        conversation.append({"role": "user", "content": "Can you help me with Python programming?"})
        
        response2 = await client.generate_response(conversation)
        print(f"User: {conversation[2]['content']}")
        print(f"AI: {response2}")
        
    except Exception as e:
        print(f"❌ Conversation failed: {e}")


async def example_streaming_chat():
    """Example: Streaming chat response."""
    print("\n🌊 Example 7: Streaming Chat")
    print("=" * 50)
    
    # Create clients
    clients = await create_all_available_clients()
    
    if not clients:
        print("❌ No clients available for streaming")
        return
    
    # Use the first available client
    provider_name = list(clients.keys())[0]
    client = clients[provider_name]
    
    print(f"Using {provider_name} for streaming...")
    
    conversation = [
        {"role": "user", "content": "Tell me a short story about a robot."}
    ]
    
    try:
        print("User: Tell me a short story about a robot.")
        print("AI: ", end="", flush=True)
        
        async for chunk in client.stream_response(conversation):
            print(chunk, end="", flush=True)
        
        print("\n")
        
    except Exception as e:
        print(f"\n❌ Streaming failed: {e}")


async def main():
    """Run all examples."""
    print("🚀 Chat AI - API Key Usage Examples")
    print("=" * 60)
    
    try:
        # Run examples
        await example_basic_usage()
        await example_individual_clients()
        await example_secure_key_management()
        await example_environment_loading()
        await example_client_testing()
        await example_chat_conversation()
        await example_streaming_chat()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
