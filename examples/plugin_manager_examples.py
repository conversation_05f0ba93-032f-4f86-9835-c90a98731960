#!/usr/bin/env python3
"""
Examples of using the External Plugin Manager for calling APIs and embedding results.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.plugin_manager import (
    ExternalPluginManager,
    process_message_with_plugins,
    search_gifs,
    generate_music,
    get_weather,
    search_photos,
    create_plugin_response
)


async def example_automatic_plugin_detection():
    """Example: Automatic plugin detection from messages."""
    print("🔌 Example 1: Automatic Plugin Detection")
    print("=" * 50)
    
    messages = [
        "Show me some funny cat GIFs",
        "Generate relaxing piano music",
        "What's the weather like in Tokyo?",
        "Find me photos of mountains",
        "Get latest technology news",
        "Tell me a random joke",
        "Give me an inspirational quote",
        "Show me a cat fact"
    ]
    
    for message in messages:
        print(f"\n--- Processing: '{message}' ---")
        
        try:
            result = await process_message_with_plugins(message, provider="auto", max_results=3)
            
            if result.get('has_plugin_result'):
                plugin_result = result['plugin_result']
                intent = result['intent']
                
                print(f"✅ Plugin detected: {intent['plugin_name']}")
                print(f"   Confidence: {intent['confidence']:.0%}")
                print(f"   Search query: '{intent['search_query']}'")
                print(f"   Success: {plugin_result.success}")
                
                if plugin_result.success:
                    print(f"   Content type: {plugin_result.content_type}")
                    if plugin_result.metadata:
                        if 'count' in plugin_result.metadata:
                            print(f"   Results count: {plugin_result.metadata['count']}")
                else:
                    print(f"   Error: {plugin_result.error_message}")
            else:
                print("ℹ️ No plugin needed")
                
        except Exception as e:
            print(f"❌ Error: {e}")


async def example_specific_plugin_usage():
    """Example: Using specific plugins directly."""
    print("\n🎯 Example 2: Specific Plugin Usage")
    print("=" * 50)
    
    # Example 1: Search GIFs
    print("\n--- Searching for GIFs ---")
    try:
        gif_result = await search_gifs("happy dance", max_results=3)
        
        if gif_result.success:
            print(f"✅ Found {gif_result.metadata.get('count', 0)} GIFs")
            print(f"   Plugin: {gif_result.plugin_name}")
            print(f"   Content type: {gif_result.content_type}")
            
            # Show first few GIF URLs
            gifs = gif_result.metadata.get('gifs', [])
            for i, gif in enumerate(gifs[:2], 1):
                print(f"   GIF {i}: {gif.get('url', 'N/A')}")
        else:
            print(f"❌ GIF search failed: {gif_result.error_message}")
            
    except Exception as e:
        print(f"❌ GIF search error: {e}")
    
    # Example 2: Generate music
    print("\n--- Generating Music ---")
    try:
        music_result = await generate_music("upbeat electronic dance music")
        
        if music_result.success:
            print(f"✅ Music generated successfully")
            print(f"   Plugin: {music_result.plugin_name}")
            print(f"   Content type: {music_result.content_type}")
            print(f"   Audio file: {music_result.content_url}")
        else:
            print(f"❌ Music generation failed: {music_result.error_message}")
            
    except Exception as e:
        print(f"❌ Music generation error: {e}")
    
    # Example 3: Get weather
    print("\n--- Getting Weather ---")
    try:
        weather_result = await get_weather("London")
        
        if weather_result.success:
            print(f"✅ Weather retrieved successfully")
            print(f"   Plugin: {weather_result.plugin_name}")
            
            # Extract weather data
            if weather_result.metadata:
                city = weather_result.metadata.get('name', 'Unknown')
                temp = weather_result.metadata.get('main', {}).get('temp', 'N/A')
                description = weather_result.metadata.get('weather', [{}])[0].get('description', 'N/A')
                
                print(f"   City: {city}")
                print(f"   Temperature: {temp}°C")
                print(f"   Description: {description}")
        else:
            print(f"❌ Weather retrieval failed: {weather_result.error_message}")
            
    except Exception as e:
        print(f"❌ Weather error: {e}")
    
    # Example 4: Search photos
    print("\n--- Searching Photos ---")
    try:
        photo_result = await search_photos("sunset ocean", max_results=3)
        
        if photo_result.success:
            print(f"✅ Found {photo_result.metadata.get('count', 0)} photos")
            print(f"   Plugin: {photo_result.plugin_name}")
            
            # Show photo details
            photos = photo_result.metadata.get('photos', [])
            for i, photo in enumerate(photos[:2], 1):
                print(f"   Photo {i}: {photo.get('description', 'No description')}")
                print(f"            by {photo.get('photographer', 'Unknown')}")
        else:
            print(f"❌ Photo search failed: {photo_result.error_message}")
            
    except Exception as e:
        print(f"❌ Photo search error: {e}")


async def example_plugin_management():
    """Example: Plugin management and configuration."""
    print("\n⚙️ Example 3: Plugin Management")
    print("=" * 50)
    
    manager = ExternalPluginManager()
    
    # Show available plugins
    print("\n--- Available Plugins ---")
    plugins = manager.get_available_plugins()
    
    for plugin in plugins:
        status = "✅ Enabled" if plugin['enabled'] else "❌ Disabled"
        api_key = "🔑 Required" if plugin['api_key_required'] else "🆓 Free"
        
        print(f"{plugin['name']}: {status} | {api_key} | {plugin['content_type']}")
        print(f"   Description: {plugin['description']}")
        print(f"   Keywords: {', '.join(plugin['keywords'][:5])}...")
        print()
    
    # Show plugin status
    print("--- Plugin Status ---")
    status = manager.get_plugin_status()
    enabled_count = sum(1 for enabled in status.values() if enabled)
    
    print(f"Total plugins: {len(status)}")
    print(f"Enabled: {enabled_count}")
    print(f"Disabled: {len(status) - enabled_count}")
    
    # Test plugin enable/disable
    print("\n--- Testing Plugin Management ---")
    
    # Disable a plugin
    manager.disable_plugin("cat_facts")
    print("Disabled cat_facts plugin")
    
    # Check status
    new_status = manager.get_plugin_status()
    print(f"cat_facts status: {'Enabled' if new_status['cat_facts'] else 'Disabled'}")
    
    # Re-enable it
    manager.enable_plugin("cat_facts")
    print("Re-enabled cat_facts plugin")


async def example_intent_detection():
    """Example: Testing intent detection accuracy."""
    print("\n🎯 Example 4: Intent Detection Testing")
    print("=" * 50)
    
    manager = ExternalPluginManager()
    
    test_messages = [
        ("Show me funny GIFs", "giphy"),
        ("Generate some music", "huggingface_audio"),
        ("What's the weather?", "weather"),
        ("Find beautiful photos", "unsplash"),
        ("Tell me a joke", "jokes"),
        ("Random cat fact", "cat_facts"),
        ("Latest news", "news"),
        ("YouTube videos", "youtube_search"),
        ("Hello world", None),  # Should not trigger any plugin
        ("How are you?", None)   # Should not trigger any plugin
    ]
    
    correct_predictions = 0
    total_tests = len(test_messages)
    
    for message, expected_plugin in test_messages:
        print(f"\n--- Testing: '{message}' ---")
        print(f"Expected plugin: {expected_plugin or 'None'}")
        
        try:
            intent = await manager.detect_plugin_intent(message, provider="auto")
            
            if intent:
                detected_plugin = intent['plugin_name']
                confidence = intent['confidence']
                search_query = intent['search_query']
                
                print(f"Detected plugin: {detected_plugin}")
                print(f"Confidence: {confidence:.0%}")
                print(f"Search query: '{search_query}'")
                
                # Check if prediction is correct
                if detected_plugin == expected_plugin:
                    print("✅ Correct prediction!")
                    correct_predictions += 1
                else:
                    print("❌ Incorrect prediction")
            else:
                print("No plugin detected")
                if expected_plugin is None:
                    print("✅ Correct prediction!")
                    correct_predictions += 1
                else:
                    print("❌ Incorrect prediction")
                    
        except Exception as e:
            print(f"❌ Error: {e}")
    
    accuracy = (correct_predictions / total_tests) * 100
    print(f"\n📊 Intent Detection Accuracy: {accuracy:.1f}% ({correct_predictions}/{total_tests})")


async def example_response_formatting():
    """Example: Response formatting with embedded content."""
    print("\n📝 Example 5: Response Formatting")
    print("=" * 50)
    
    # Test different types of plugin results
    test_cases = [
        "Show me cat GIFs",
        "What's the weather in Paris?",
        "Tell me a joke",
        "Find photos of coffee"
    ]
    
    for message in test_cases:
        print(f"\n--- Processing: '{message}' ---")
        
        try:
            result = await process_message_with_plugins(message, max_results=2)
            
            if result.get('has_plugin_result'):
                plugin_result = result['plugin_result']
                
                # Create formatted response
                formatted_response = create_plugin_response(plugin_result, message)
                
                print("Formatted Response:")
                print("─" * 40)
                print(formatted_response[:300] + "..." if len(formatted_response) > 300 else formatted_response)
                print("─" * 40)
                
                # Show if HTML embed is available
                if plugin_result.embed_html:
                    print("✅ HTML embed available")
                    print(f"   Embed length: {len(plugin_result.embed_html)} characters")
                else:
                    print("❌ No HTML embed")
                    
        except Exception as e:
            print(f"❌ Error: {e}")


async def example_error_handling():
    """Example: Error handling and edge cases."""
    print("\n🛡️ Example 6: Error Handling")
    print("=" * 50)
    
    manager = ExternalPluginManager()
    
    # Test with invalid/empty queries
    test_cases = [
        ("", "Empty message"),
        ("   ", "Whitespace only"),
        ("a" * 1000, "Very long message"),
        ("🎵🎭🌤️📷", "Emoji only"),
        ("Generate music with invalid characters: \x00\x01", "Invalid characters")
    ]
    
    for message, description in test_cases:
        print(f"\n--- Testing: {description} ---")
        print(f"Message: '{message[:50]}{'...' if len(message) > 50 else ''}'")
        
        try:
            intent = await manager.detect_plugin_intent(message)
            
            if intent:
                print(f"✅ Intent detected: {intent['plugin_name']}")
                
                # Try to execute plugin
                result = await manager.execute_plugin(intent, max_results=1)
                
                if result.success:
                    print("✅ Plugin executed successfully")
                else:
                    print(f"❌ Plugin execution failed: {result.error_message}")
            else:
                print("ℹ️ No intent detected")
                
        except Exception as e:
            print(f"❌ Error handled: {e}")
    
    # Test rate limiting
    print("\n--- Testing Rate Limiting ---")
    
    # Make multiple rapid requests to test rate limiting
    for i in range(3):
        try:
            intent = {
                "plugin_name": "cat_facts",
                "plugin_config": manager.plugins["cat_facts"],
                "search_query": "",
                "confidence": 1.0
            }
            
            result = await manager.execute_plugin(intent)
            
            if result.success:
                print(f"Request {i+1}: ✅ Success")
            else:
                print(f"Request {i+1}: ❌ {result.error_message}")
                
        except Exception as e:
            print(f"Request {i+1}: ❌ Error: {e}")


async def main():
    """Run all examples."""
    print("🚀 External Plugin Manager Examples")
    print("=" * 60)
    
    try:
        await example_automatic_plugin_detection()
        await example_specific_plugin_usage()
        await example_plugin_management()
        await example_intent_detection()
        await example_response_formatting()
        await example_error_handling()
        
        print("\n✅ All examples completed successfully!")
        print("\nNext steps:")
        print("1. Try the Streamlit interface: streamlit run src/ui/plugin_interface.py")
        print("2. Configure API keys for external services")
        print("3. Test different message types and keywords")
        print("4. Integrate plugins into your chat application")
        
    except Exception as e:
        print(f"\n❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
