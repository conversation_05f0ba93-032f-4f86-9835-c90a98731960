#!/usr/bin/env python3
"""
Examples of using the Code Assistant for snippet generation and debugging.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.code_assistant import CodeAssistant


async def example_code_generation():
    """Example: Generate code snippets."""
    print("🐍 Example 1: Code Generation")
    print("=" * 50)
    
    assistant = CodeAssistant()
    
    # Example 1: Simple function
    print("\n--- Generating Simple Function ---")
    snippet1 = await assistant.generate_code_snippet(
        description="Create a function that calculates the factorial of a number",
        category="general",
        complexity="simple",
        include_tests=True
    )
    
    print(f"Description: {snippet1.description}")
    print(f"Dependencies: {snippet1.dependencies}")
    print("Generated Code:")
    print(snippet1.code)
    
    if snippet1.test_code:
        print("\nTest Code:")
        print(snippet1.test_code)
    
    # Example 2: Web scraping
    print("\n--- Generating Web Scraping Code ---")
    snippet2 = await assistant.generate_code_snippet(
        description="Create a function that scrapes product prices from an e-commerce website",
        category="web",
        complexity="medium",
        include_tests=False
    )
    
    print(f"Description: {snippet2.description}")
    print(f"Dependencies: {snippet2.dependencies}")
    print("Generated Code:")
    print(snippet2.code[:300] + "..." if len(snippet2.code) > 300 else snippet2.code)
    
    # Example 3: Data analysis
    print("\n--- Generating Data Analysis Code ---")
    snippet3 = await assistant.generate_code_snippet(
        description="Create a function that analyzes sales data and creates visualizations",
        category="data",
        complexity="complex",
        include_tests=True
    )
    
    print(f"Description: {snippet3.description}")
    print(f"Dependencies: {snippet3.dependencies}")
    print("Generated Code:")
    print(snippet3.code[:300] + "..." if len(snippet3.code) > 300 else snippet3.code)


async def example_error_debugging():
    """Example: Debug error logs."""
    print("\n🔍 Example 2: Error Debugging")
    print("=" * 50)
    
    assistant = CodeAssistant()
    
    # Example 1: Syntax Error
    print("\n--- Debugging Syntax Error ---")
    syntax_error = """
Traceback (most recent call last):
  File "test.py", line 3
    if x = 5:
         ^
SyntaxError: invalid syntax
"""
    
    analysis1 = await assistant.analyze_error_log(syntax_error)
    
    print(f"Error Type: {analysis1.error_type}")
    print(f"Error Message: {analysis1.error_message}")
    print(f"Severity: {analysis1.severity}")
    print(f"Confidence: {analysis1.confidence:.0%}")
    print("Suggested Fixes:")
    for i, fix in enumerate(analysis1.suggested_fixes, 1):
        print(f"  {i}. {fix}")
    
    # Example 2: Name Error with context
    print("\n--- Debugging Name Error ---")
    name_error = """
Traceback (most recent call last):
  File "script.py", line 10, in <module>
    result = calculate_average(numbers)
  File "script.py", line 5, in calculate_average
    return sum(data) / len(data)
NameError: name 'data' is not defined
"""
    
    code_context = """
def calculate_average(numbers):
    total = sum(data)  # Error: 'data' should be 'numbers'
    count = len(data)  # Error: 'data' should be 'numbers'
    return total / count

numbers = [1, 2, 3, 4, 5]
result = calculate_average(numbers)
print(result)
"""
    
    analysis2 = await assistant.analyze_error_log(name_error, code_context)
    
    print(f"Error Type: {analysis2.error_type}")
    print(f"Error Message: {analysis2.error_message}")
    print(f"Severity: {analysis2.severity}")
    print(f"Confidence: {analysis2.confidence:.0%}")
    print("Suggested Fixes:")
    for i, fix in enumerate(analysis2.suggested_fixes, 1):
        print(f"  {i}. {fix}")
    
    if analysis2.code_suggestions:
        print("Code Suggestions:")
        for suggestion in analysis2.code_suggestions:
            print(suggestion)
    
    # Example 3: Type Error
    print("\n--- Debugging Type Error ---")
    type_error = """
Traceback (most recent call last):
  File "example.py", line 8, in <module>
    result = process_data(user_input)
  File "example.py", line 3, in process_data
    return data.upper() + " processed"
AttributeError: 'int' object has no attribute 'upper'
"""
    
    type_context = """
def process_data(data):
    return data.upper() + " processed"

user_input = 12345  # Should be string
result = process_data(user_input)
print(result)
"""
    
    analysis3 = await assistant.analyze_error_log(type_error, type_context)
    
    print(f"Error Type: {analysis3.error_type}")
    print(f"Error Message: {analysis3.error_message}")
    print(f"Severity: {analysis3.severity}")
    print(f"Confidence: {analysis3.confidence:.0%}")
    print("Suggested Fixes:")
    for i, fix in enumerate(analysis3.suggested_fixes, 1):
        print(f"  {i}. {fix}")


async def example_code_testing():
    """Example: Test generated code safely."""
    print("\n🧪 Example 3: Code Testing")
    print("=" * 50)
    
    assistant = CodeAssistant()
    
    # Test 1: Working code
    print("\n--- Testing Working Code ---")
    working_code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Test the function
for i in range(10):
    print(f"fibonacci({i}) = {fibonacci(i)}")
"""
    
    result1 = assistant.run_code_safely(working_code, timeout=5)
    
    print(f"Success: {result1['success']}")
    print(f"Execution Time: {result1['execution_time']:.3f}s")
    if result1['success']:
        print("Output:")
        print(result1['output'][:200] + "..." if len(result1['output']) > 200 else result1['output'])
    else:
        print("Error:")
        print(result1['error'])
    
    # Test 2: Code with error
    print("\n--- Testing Code with Error ---")
    error_code = """
def divide_numbers(a, b):
    return a / b

# This will cause division by zero
result = divide_numbers(10, 0)
print(result)
"""
    
    result2 = assistant.run_code_safely(error_code, timeout=5)
    
    print(f"Success: {result2['success']}")
    print(f"Execution Time: {result2['execution_time']:.3f}s")
    if not result2['success']:
        print("Error:")
        print(result2['error'])
    
    # Test 3: Infinite loop (timeout test)
    print("\n--- Testing Timeout Protection ---")
    infinite_code = """
while True:
    print("This will run forever...")
"""
    
    result3 = assistant.run_code_safely(infinite_code, timeout=2)
    
    print(f"Success: {result3['success']}")
    print(f"Execution Time: {result3['execution_time']:.3f}s")
    print("Error:")
    print(result3['error'])


async def example_advanced_features():
    """Example: Advanced features and templates."""
    print("\n⚡ Example 4: Advanced Features")
    print("=" * 50)
    
    assistant = CodeAssistant()
    
    # Example 1: API client generation
    print("\n--- Generating API Client ---")
    api_snippet = await assistant.generate_code_snippet(
        description="Create a REST API client class for a weather service with authentication and error handling",
        category="api",
        complexity="complex",
        include_tests=True
    )
    
    print(f"Dependencies: {api_snippet.dependencies}")
    print("Generated Code (first 500 chars):")
    print(api_snippet.code[:500] + "...")
    
    # Example 2: Machine learning pipeline
    print("\n--- Generating ML Pipeline ---")
    ml_snippet = await assistant.generate_code_snippet(
        description="Create a machine learning pipeline for text classification using scikit-learn",
        category="ml",
        complexity="complex",
        include_tests=False
    )
    
    print(f"Dependencies: {ml_snippet.dependencies}")
    print("Generated Code (first 500 chars):")
    print(ml_snippet.code[:500] + "...")
    
    # Example 3: Automation script
    print("\n--- Generating Automation Script ---")
    automation_snippet = await assistant.generate_code_snippet(
        description="Create a script that monitors a directory for new files and processes them automatically",
        category="automation",
        complexity="medium",
        include_tests=True
    )
    
    print(f"Dependencies: {automation_snippet.dependencies}")
    print("Generated Code (first 500 chars):")
    print(automation_snippet.code[:500] + "...")


async def example_real_world_debugging():
    """Example: Real-world debugging scenarios."""
    print("\n🌍 Example 5: Real-World Debugging")
    print("=" * 50)
    
    assistant = CodeAssistant()
    
    # Example 1: Pandas error
    print("\n--- Debugging Pandas Error ---")
    pandas_error = """
Traceback (most recent call last):
  File "data_analysis.py", line 15, in analyze_sales
    monthly_sales = df.groupby('month').sum()
  File "/usr/local/lib/python3.9/site-packages/pandas/core/groupby/groupby.py", line 1321, in sum
    return self._cython_transform("sum", numeric_only=numeric_only, **kwargs)
  File "/usr/local/lib/python3.9/site-packages/pandas/core/groupby/groupby.py", line 1999, in _cython_transform
    return self._transform(
KeyError: 'month'
"""
    
    pandas_context = """
import pandas as pd

def analyze_sales(csv_file):
    df = pd.read_csv(csv_file)
    print(df.columns)  # Debug: check column names
    
    # Error: column might be named 'Month' or 'date' instead of 'month'
    monthly_sales = df.groupby('month').sum()
    return monthly_sales

# Usage
sales_data = analyze_sales('sales.csv')
print(sales_data)
"""
    
    analysis = await assistant.analyze_error_log(pandas_error, pandas_context)
    
    print(f"Error Analysis:")
    print(f"  Type: {analysis.error_type}")
    print(f"  Severity: {analysis.severity}")
    print(f"  Confidence: {analysis.confidence:.0%}")
    print("Suggested Fixes:")
    for i, fix in enumerate(analysis.suggested_fixes, 1):
        print(f"  {i}. {fix}")
    
    # Example 2: Import error
    print("\n--- Debugging Import Error ---")
    import_error = """
Traceback (most recent call last):
  File "ml_model.py", line 3, in <module>
    from sklearn.ensemble import RandomForestClassifier
ModuleNotFoundError: No module named 'sklearn'
"""
    
    import_context = """
# ml_model.py
import pandas as pd
from sklearn.ensemble import RandomForestClassifier  # Error: sklearn not installed
from sklearn.model_selection import train_test_split

def train_model(data):
    # Model training code
    pass
"""
    
    analysis2 = await assistant.analyze_error_log(import_error, import_context)
    
    print(f"Error Analysis:")
    print(f"  Type: {analysis2.error_type}")
    print(f"  Severity: {analysis2.severity}")
    print(f"  Confidence: {analysis2.confidence:.0%}")
    print("Suggested Fixes:")
    for i, fix in enumerate(analysis2.suggested_fixes, 1):
        print(f"  {i}. {fix}")


async def main():
    """Run all examples."""
    print("🚀 Code Assistant Examples")
    print("=" * 60)
    
    try:
        await example_code_generation()
        await example_error_debugging()
        await example_code_testing()
        await example_advanced_features()
        await example_real_world_debugging()
        
        print("\n✅ All examples completed successfully!")
        print("\nNext steps:")
        print("1. Try the Streamlit interface: streamlit run src/ui/code_assistant_interface.py")
        print("2. Experiment with different code descriptions")
        print("3. Test the debugging features with your own error logs")
        
    except Exception as e:
        print(f"\n❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
