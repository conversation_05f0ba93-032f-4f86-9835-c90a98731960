#!/usr/bin/env python3
"""
Examples of using the Diagram Generator for creating architecture diagrams.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.diagram_generator import DiagramGenerator, generate_architecture_diagram, create_mermaid_diagram
from core.chat_with_diagrams import DiagramChatAssistant, process_chat_message_with_diagrams


async def example_basic_diagram_generation():
    """Example: Basic diagram generation."""
    print("🏗️ Example 1: Basic Diagram Generation")
    print("=" * 50)
    
    generator = DiagramGenerator()
    
    # Example 1: E-commerce microservices
    print("\n--- E-commerce Microservices Architecture ---")
    description = """
    Create a microservices architecture for an e-commerce platform with:
    - React frontend for customer interface
    - Admin dashboard with Vue.js
    - API Gateway (Kong) for routing
    - User Management Service (Node.js)
    - Product Catalog Service (Python/Django)
    - Order Processing Service (Java/Spring)
    - Payment Service (Node.js)
    - Notification Service (Python)
    - PostgreSQL databases for each service
    - Redis cache for session management
    - RabbitMQ for async messaging
    - Elasticsearch for product search
    - Docker containers
    - Kubernetes orchestration
    - Load balancer and CDN
    """
    
    diagram = await generator.generate_diagram_from_text(
        description=description,
        diagram_type="system",
        style="modern"
    )
    
    print(f"Generated: {diagram.title}")
    print(f"Description: {diagram.description}")
    print(f"Components: {len(diagram.components)}")
    print(f"Connections: {len(diagram.connections)}")
    
    if diagram.components:
        print("\nComponents:")
        for comp in diagram.components[:5]:  # Show first 5
            print(f"  - {comp.name} ({comp.type}): {comp.description}")
    
    if diagram.mermaid_code:
        print(f"\nMermaid Code (first 200 chars):")
        print(diagram.mermaid_code[:200] + "...")
    
    # Example 2: Data pipeline
    print("\n--- Data Processing Pipeline ---")
    data_description = """
    Design a real-time data processing pipeline:
    - Data ingestion from multiple sources (APIs, databases, files)
    - Apache Kafka for streaming data
    - Apache Spark for real-time processing
    - Apache Airflow for workflow orchestration
    - Data warehouse (Snowflake)
    - Analytics dashboard (Tableau)
    - Monitoring with Prometheus and Grafana
    - Data lake storage (AWS S3)
    - Machine learning pipeline with MLflow
    """
    
    data_diagram = await generator.generate_diagram_from_text(
        description=data_description,
        diagram_type="data_flow",
        style="detailed"
    )
    
    print(f"Generated: {data_diagram.title}")
    print(f"Components: {len(data_diagram.components)}")
    print(f"Image saved to: {data_diagram.generated_image_path}")


async def example_mermaid_generation():
    """Example: Mermaid diagram generation."""
    print("\n🔄 Example 2: Mermaid Diagram Generation")
    print("=" * 50)
    
    # Example: Mobile app backend
    description = """
    Mobile application backend architecture:
    - iOS and Android mobile apps
    - API Gateway with authentication
    - User service for profile management
    - Content service for app content
    - Push notification service
    - Analytics service for user tracking
    - File storage service for media
    - PostgreSQL for user data
    - MongoDB for content data
    - Redis for caching
    - CDN for media delivery
    - Load balancer for high availability
    """
    
    mermaid_code = await create_mermaid_diagram(description)
    
    print("Generated Mermaid Code:")
    print("─" * 60)
    print(mermaid_code)
    print("─" * 60)


async def example_ai_image_generation():
    """Example: AI image generation (placeholder)."""
    print("\n🎨 Example 3: AI Image Generation")
    print("=" * 50)
    
    generator = DiagramGenerator()
    
    description = """
    Cloud-native web application:
    - React frontend deployed on Vercel
    - Node.js API on AWS Lambda
    - GraphQL API layer
    - PostgreSQL on AWS RDS
    - Redis on AWS ElastiCache
    - S3 for file storage
    - CloudFront CDN
    - API Gateway
    - Cognito for authentication
    - CloudWatch for monitoring
    """
    
    result = await generator.generate_diagram_with_ai_image(
        description=description,
        use_external_api=True
    )
    
    print("AI Image Generation Result:")
    ai_image = result.get("ai_generated_image")
    if ai_image:
        print(f"  Image URL: {ai_image.get('image_url', 'N/A')}")
        print(f"  Image ID: {ai_image.get('image_id', 'N/A')}")
        print(f"  Status: {ai_image.get('status', 'N/A')}")
    else:
        print("  AI image generation not available (placeholder)")
    
    print(f"Local image: {result.get('local_image_path', 'N/A')}")
    print(f"Mermaid available: {bool(result.get('mermaid_code'))}")


async def example_chat_integration():
    """Example: Chat integration with diagrams."""
    print("\n💬 Example 4: Chat Integration")
    print("=" * 50)
    
    assistant = DiagramChatAssistant()
    
    # Simulate conversation
    conversation = []
    
    messages = [
        "I need help designing a system for a social media platform",
        "Create an architecture diagram for a social media platform with real-time features",
        "How would you handle the real-time messaging in this system?",
        "Show me the database design for user relationships"
    ]
    
    for i, message in enumerate(messages, 1):
        print(f"\n--- Message {i} ---")
        print(f"User: {message}")
        
        result = await assistant.process_message(message, conversation)
        
        print(f"Has diagram: {result.get('has_diagram', False)}")
        print(f"Response (first 150 chars): {result['response'][:150]}...")
        
        if result.get('has_diagram'):
            diagram_result = result.get('diagram_result')
            if diagram_result:
                diagram = diagram_result.get('diagram')
                if diagram:
                    print(f"  Diagram: {diagram.title}")
                    print(f"  Components: {len(diagram.components)}")
        
        # Add to conversation
        conversation.append({"role": "user", "content": message})
        conversation.append({"role": "assistant", "content": result['response']})


async def example_helper_functions():
    """Example: Using helper functions."""
    print("\n⚡ Example 5: Helper Functions")
    print("=" * 50)
    
    # Example 1: Direct diagram generation
    print("\n--- Direct Architecture Diagram ---")
    description = """
    Serverless e-commerce API:
    - AWS Lambda functions for business logic
    - API Gateway for HTTP endpoints
    - DynamoDB for product catalog
    - S3 for image storage
    - CloudFront for CDN
    - Cognito for user authentication
    - SQS for order processing queue
    - SNS for notifications
    - CloudWatch for monitoring
    """
    
    response = await generate_architecture_diagram(description)
    print(f"Generated response length: {len(response)} characters")
    print(f"Response preview: {response[:200]}...")
    
    # Example 2: Chat message processing
    print("\n--- Chat Message Processing ---")
    chat_message = "Design a microservices architecture for a video streaming platform like Netflix"
    
    chat_result = await process_chat_message_with_diagrams(chat_message)
    
    print(f"Chat response type: {'Diagram' if chat_result.get('has_diagram') else 'Text'}")
    print(f"Response length: {len(chat_result['response'])} characters")
    
    if chat_result.get('has_diagram'):
        print("Diagram components available!")


async def example_advanced_features():
    """Example: Advanced features and customization."""
    print("\n🚀 Example 6: Advanced Features")
    print("=" * 50)
    
    generator = DiagramGenerator()
    
    # Example: Network topology diagram
    print("\n--- Network Topology Diagram ---")
    network_description = """
    Enterprise network topology:
    - Internet gateway and firewall
    - DMZ with web servers
    - Internal network with application servers
    - Database subnet with master-slave setup
    - Management network for monitoring
    - VPN access for remote users
    - Load balancers for high availability
    - Network switches and routers
    - Security appliances (IDS/IPS)
    - Backup and disaster recovery systems
    """
    
    network_diagram = await generator.generate_diagram_from_text(
        description=network_description,
        diagram_type="network",
        style="detailed"
    )
    
    print(f"Network diagram: {network_diagram.title}")
    print(f"Components: {len(network_diagram.components)}")
    
    # Example: Deployment diagram
    print("\n--- Deployment Diagram ---")
    deployment_description = """
    Kubernetes deployment architecture:
    - Multiple availability zones
    - Kubernetes master nodes
    - Worker nodes with pods
    - Ingress controller
    - Service mesh (Istio)
    - Persistent volumes
    - ConfigMaps and Secrets
    - Horizontal Pod Autoscaler
    - Monitoring stack (Prometheus/Grafana)
    - Logging stack (ELK)
    - CI/CD pipeline integration
    """
    
    deployment_diagram = await generator.generate_diagram_from_text(
        description=deployment_description,
        diagram_type="deployment",
        style="modern"
    )
    
    print(f"Deployment diagram: {deployment_diagram.title}")
    print(f"Components: {len(deployment_diagram.components)}")
    
    # Show component types distribution
    if deployment_diagram.components:
        component_types = {}
        for comp in deployment_diagram.components:
            component_types[comp.type] = component_types.get(comp.type, 0) + 1
        
        print("Component types distribution:")
        for comp_type, count in component_types.items():
            print(f"  {comp_type}: {count}")


async def example_error_handling():
    """Example: Error handling and edge cases."""
    print("\n🛡️ Example 7: Error Handling")
    print("=" * 50)
    
    generator = DiagramGenerator()
    
    # Test with minimal description
    print("\n--- Minimal Description ---")
    try:
        minimal_diagram = await generator.generate_diagram_from_text(
            description="Simple web app",
            diagram_type="system"
        )
        print(f"Minimal diagram generated: {minimal_diagram.title}")
        print(f"Components: {len(minimal_diagram.components)}")
    except Exception as e:
        print(f"Error with minimal description: {e}")
    
    # Test with very detailed description
    print("\n--- Very Detailed Description ---")
    try:
        detailed_description = """
        Comprehensive enterprise architecture for a global e-commerce platform with:
        - Multi-region deployment across US, Europe, and Asia
        - React/Next.js frontend with server-side rendering
        - Mobile apps for iOS and Android with React Native
        - API Gateway with rate limiting and authentication
        - Microservices architecture with 20+ services
        - Event-driven architecture with Apache Kafka
        - CQRS pattern with separate read/write databases
        - PostgreSQL for transactional data
        - MongoDB for product catalog
        - Elasticsearch for search functionality
        - Redis for caching and session management
        - Apache Spark for real-time analytics
        - Machine learning recommendations with TensorFlow
        - Container orchestration with Kubernetes
        - Service mesh with Istio
        - CI/CD pipeline with Jenkins and GitLab
        - Infrastructure as Code with Terraform
        - Monitoring with Prometheus, Grafana, and Jaeger
        - Logging with ELK stack
        - Security scanning and compliance tools
        - Disaster recovery and backup systems
        """
        
        detailed_diagram = await generator.generate_diagram_from_text(
            description=detailed_description,
            diagram_type="system",
            style="detailed"
        )
        print(f"Detailed diagram generated: {detailed_diagram.title}")
        print(f"Components: {len(detailed_diagram.components)}")
    except Exception as e:
        print(f"Error with detailed description: {e}")


async def main():
    """Run all examples."""
    print("🚀 Diagram Generator Examples")
    print("=" * 60)
    
    try:
        await example_basic_diagram_generation()
        await example_mermaid_generation()
        await example_ai_image_generation()
        await example_chat_integration()
        await example_helper_functions()
        await example_advanced_features()
        await example_error_handling()
        
        print("\n✅ All examples completed successfully!")
        print("\nNext steps:")
        print("1. Try the Streamlit interface: streamlit run src/ui/diagram_interface.py")
        print("2. Experiment with different architecture descriptions")
        print("3. Test the chat integration with diagram generation")
        print("4. Customize the diagram styles and types")
        
    except Exception as e:
        print(f"\n❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
